import react from '@vitejs/plugin-react-swc';
import { defineConfig, loadEnv } from 'vite';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';

// https://vitejs.dev/config
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isDev = command === 'serve';
  const isProd = mode === 'production';

  return {
    plugins: [
      svgr({
        svgrOptions: {
          icon: true,
        },
      }),
      react(),
      tsconfigPaths(),
    ],

    server: {
      port: env.PORT ? parseInt(env.PORT) : 3000,
      host: true,
      open: true,
      cors: true,
      fs: {
        strict: true,
      },
    },

    preview: {
      port: 4173,
      host: true,
      open: true,
    },

    build: {
      target: 'esnext',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: isDev,
      minify: isProd ? 'esbuild' : false,
      reportCompressedSize: isProd,
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            router: ['react-router', 'react-router-dom'],
            ui: ['antd'],
            redux: ['@reduxjs/toolkit', 'react-redux', 'redux-saga'],
            utils: ['lodash', 'dayjs', 'axios'],
          },
        },
      },
    },

    css: {
      devSourcemap: isDev,
      modules: {
        localsConvention: 'camelCase',
      },
    },

    define: {
      'process.env.REMOTE_URL': JSON.stringify(env.REMOTE_URL),
      'process.env.ACCESS_KEY': JSON.stringify(env.ACCESS_KEY),
      'process.env.FIREBASE_API_KEY': JSON.stringify(env.FIREBASE_API_KEY),
      'process.env.FIREBASE_AUTH_DOMAIN': JSON.stringify(
        env.FIREBASE_AUTH_DOMAIN,
      ),
      'process.env.FIREBASE_PROJECT_ID': JSON.stringify(
        env.FIREBASE_PROJECT_ID,
      ),
      'process.env.FIREBASE_STORAGE_BUCKET': JSON.stringify(
        env.FIREBASE_STORAGE_BUCKET,
      ),
      'process.env.FIREBASE_MESSAGING_SENDER_ID': JSON.stringify(
        env.FIREBASE_MESSAGING_SENDER_ID,
      ),
      'process.env.FIREBASE_APP_ID': JSON.stringify(env.FIREBASE_APP_ID),
      'process.env.FIREBASE_VAPID_KEY': JSON.stringify(env.FIREBASE_VAPID_KEY),
      __DEV__: isDev,
      __PROD__: isProd,
      'process.env.DEFAULT_LAT': JSON.stringify(env.DEFAULT_LAT),
      'process.env.DEFAULT_LNG': JSON.stringify(env.DEFAULT_LNG),
      'process.env.RECAPTCHA_SITE_KEY': JSON.stringify(env.RECAPTCHA_SITE_KEY),
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'antd',
        '@reduxjs/toolkit',
        'react-redux',
        'axios',
        'lodash',
        'dayjs',
        'react-mobile-app-button',
      ],
      exclude: ['@vite/client', '@vite/env'],
    },
  };
});
