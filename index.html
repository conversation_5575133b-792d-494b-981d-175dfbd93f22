<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/navio-favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Navio</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
    
    <script>
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker
          .register('/firebase-messaging-sw.js')
          .then(registration => {
            console.log('Service Worker registered:', registration.scope);
          })
          .catch(err => {
            console.error('Service Worker registration failed:', err);
          });
      }
    </script>
  </body>
</html>
