{"compilerOptions": {"baseUrl": "./src", "target": "ES2022", "lib": ["dom", "dom.iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noImplicitAny": false, "jsx": "react-jsx", "types": ["vite/client", "vite-plugin-svgr/client"]}, "include": ["src/**/*", "src/**/*.tsx", "src/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "build"]}