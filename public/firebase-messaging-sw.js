/* eslint-disable */
// @ts-nocheck

importScripts(
  'https://www.gstatic.com/firebasejs/11.5.0/firebase-app-compat.js',
);
importScripts(
  'https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging-compat.js',
);

const firebaseConfig = {
  apiKey: 'AIzaSyAzradtdgHr23IQyXYdOatmPWuTfllUc4o',
  authDomain: 'navio-staging.firebaseapp.com',
  databaseURL:
    'https://navio-staging-default-rtdb.asia-southeast1.firebasedatabase.app',
  projectId: 'navio-staging',
  storageBucket: 'navio-staging.firebasestorage.app',
  messagingSenderId: '166400805714',
  appId: '1:166400805714:web:8e7e7b1b4e654eacc9488b',
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

messaging.onBackgroundMessage(payload => {
  const notificationTitle = payload.notification?.title || 'New Message';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/favicon.ico',
    badge: '/favicon.ico',
    tag: payload.messageId || 'notification',
    data: payload.data || {},
    actions: [
      {
        action: 'open',
        title: 'Open App',
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
      },
    ],
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener('notificationclick', event => {
  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  event.waitUntil(
    clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then(clientList => {
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus();
          }
        }

        if (clients.openWindow) {
          return clients.openWindow('/');
        }
      }),
  );
});
