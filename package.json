{"name": "navio-app", "description": "Navio web app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:debug": "vite --debug", "dev:host": "vite --host", "dev:https": "vite --https", "build": "tsc && vite build", "serve": "vite preview", "preview": "vite preview", "preview:host": "vite preview --host", "lint": "eslint src --max-warnings=0", "lint:fix": "eslint src --fix", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}", "format:check": "prettier --check src/**/*.{ts,tsx,js,jsx,json,css,md}", "typecheck": "tsc --project tsconfig.json --noEmit", "prepare": "husky install", "commit": "cz", "pre-commit": "lint-staged", "validate": "npm run typecheck && npm run lint && npm run format:check", "analyze": "vite-bundle-analyzer dist", "deps:check": "pnpm audit && pnpm outdated", "deps:update": "pnpm update --latest", "clean": "rm -rf dist node_modules/.vite", "clean:all": "rm -rf dist node_modules pnpm-lock.yaml", "size": "size-limit", "deploy": "pnpm run build && tar -cvf ./deploy.tar --exclude='*.map' ./captain-definition ./dist/* && caprover deploy -t ./deploy.tar && rm deploy.tar"}, "dependencies": {"@casl/ability": "^6.7.2", "@casl/react": "^4.0.0", "@nivo/bar": "^0.87.0", "@nivo/core": "^0.87.0", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.0", "add": "^2.0.6", "antd": "^5.21.1", "axios": "^1.7.7", "camelcase-keys": "^9.1.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "firebase": "^11.5.0", "i18next": "^23.15.1", "immer": "^10.1.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lodash": "^4.17.21", "query-string": "^9.1.1", "rc-virtual-list": "^3.17.0", "react": "^18.3.1", "react-custom-scrollbars": "^4.2.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.2", "react-infinite-scroll-component": "^6.1.0", "react-map4d-map": "^1.3.9", "react-mobile-app-button": "^1.2.18", "react-name-initials-avatar": "^0.0.7", "react-router": "^6.26.2", "react-router-dom": "6.26.1", "react-svg": "^16.1.34", "tailwind-styled-components": "^2.2.0", "type-fest": "^4.26.1", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/cz-commitlint": "^19.2.0", "@size-limit/file": "^11.1.6", "@size-limit/webpack": "^11.1.6", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/js-cookie": "^3.0.6", "@types/jspdf": "^2.0.0", "@types/lodash": "^4.17.10", "@types/node": "^20.16.7", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.20", "commitizen": "^4.3.0", "device-uuid": "^1.0.4", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.36.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-tailwindcss": "^3.17.4", "husky": "^9.0.11", "jspdf-autotable": "^5.0.2", "lint-staged": "^15.2.7", "postcss": "^8.4.47", "prettier": "3.1.1", "react-error-boundary": "^6.0.0", "size-limit": "^11.1.6", "tailwindcss": "^3.4.13", "typescript": "^5.6.2", "vite": "^5.4.8", "vite-bundle-analyzer": "^0.11.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "yup": "^1.6.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "size-limit": [{"path": "dist/assets/*.js", "limit": "500 KB"}, {"path": "dist/assets/*.css", "limit": "50 KB"}]}