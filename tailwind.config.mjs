/** @type {import('tailwindcss').Config} */
import { Colors } from './src/assets/styles';
export default {
  content: ['./src/**/*.{mjs,js,ts,jsx,tsx}'],
  // corePlugins: {
  //   preflight: false,
  // },
  theme: {
    extend: {
      screens: {
        nv: '10px',
        '4xl': '1920px',
      },
      fontFamily: {
        sans: ['Inter'],
      },
      boxShadow: {
        sidebar: 'inset 0 2px 4px 0 #E1E3EB;',
        panel: '0 1px 8px 0 rgba(0, 0, 0, 0.12)'
      },
    },
    colors: Colors,
  },
  plugins: [],
  important: true,
};
