import React, { useMemo } from 'react';
import {
  BrowserRouter as Router,
  Navigate,
  Route,
  Routes,
} from 'react-router-dom';

export interface PageRoute {
  path: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: React.ComponentType<any>;
  exact?: boolean;
  permissions?: string[];
  featureFlag?: string;
}

interface Props {
  routes: PageRoute[];
  defaultRoute?: string;
}

const SwitchRoute = ({ routes, defaultRoute }: Props) => {
  const finalRoute = useMemo(() => {
    if (defaultRoute) {
      return defaultRoute;
    } else if (window.location.pathname) {
      return window.location.pathname;
    } else {
      return routes[0].path;
    }
  }, [window.location.pathname, defaultRoute]);
  if (routes.length === 0) return null;
  return (
    <Router>
      <Routes>
        {routes.map(route => {
          return (
            <Route
              key={route.path}
              path={route.path}
              element={<route.component />}
            />
          );
        })}
        <Route path="*" element={<Navigate to={finalRoute} />} />
      </Routes>
    </Router>
  );
};

export default SwitchRoute;
