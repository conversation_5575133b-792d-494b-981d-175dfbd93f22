import React, { useEffect, useMemo, useState } from 'react';

import dayjs, { Dayjs } from 'dayjs';
import { capitalize } from 'lodash';

import { useHistoryData, useOverviewData } from 'features/Map/hooks/useMapData';
import { DataItem } from 'features/Map/types';
import type { DeviceType } from 'types/device';

import images from 'assets/images';
import { BodyMdExtend, FontBold } from 'assets/styles';
import { Icon, RRCalendar } from 'components';
import RRScrollView from 'components/RRScrollView/RRScrollView';
import Spinner from 'components/Spinner';

import DeviceJourneyDetailInfo from './DeviceJourneyDetailInfo';
import DeviceJourneyInfo from './DeviceJourneyInfo';

interface DeviceHistoryProps {
  selectedPreviewDate: Dayjs | undefined;
  currentViewData: DataItem | undefined;
  className?: string;
  visible?: boolean;
  device: DeviceType;
  onGoBack?: () => void;
  onViewPreview?: (previewDate?: Dayjs, item?: DataItem) => void;
}

const DeviceHistory: React.FC<DeviceHistoryProps> = ({
  selectedPreviewDate,
  currentViewData,
  visible,
  device,
  onGoBack,
  onViewPreview,
}) => {
  const [startDate, setStartDate] = useState<Dayjs>(dayjs());
  const [endDate, setEndDate] = useState<Dayjs>(dayjs());
  const [isSelectingCalendar, setIsSelectingCalendar] = useState(false);

  useEffect(() => {
    if (!visible) {
      setStartDate(dayjs());
      setEndDate(dayjs());
      setIsSelectingCalendar(false);
    }
  }, [visible]);

  const headerPreviewDate = useMemo(() => {
    if (selectedPreviewDate) {
      const dayOfWeek = selectedPreviewDate
        ?.format('dddd')
        ?.split(' ')
        .map(word => capitalize(word))
        .join(' ');
      const fullDate = selectedPreviewDate?.format('DD/MM/YYYY');
      return `${dayOfWeek}, ${fullDate}`;
    }
    return '';
  }, [selectedPreviewDate]);

  const isValidRange = useMemo(() => {
    if (startDate && endDate && startDate.isSame(endDate, 'day')) {
      return true;
    }
    return startDate && endDate && startDate.isBefore(endDate, 'day');
  }, [startDate, endDate]);

  const strStartDate = useMemo(() => {
    return dayjs(startDate).format('YYYY-MM-DD');
  }, [startDate]);
  const strEndDate = useMemo(() => {
    return dayjs(endDate).format('YYYY-MM-DD');
  }, [endDate]);

  // Queries
  const { data: historyData, isLoading: isHistoryLoading } = useHistoryData({
    imei: device?.imei ?? '',
    startDate: strStartDate,
    endDate: strEndDate,
    enabled: !!visible && isValidRange && !isSelectingCalendar,
  });

  const {
    data: overviewData = {
      data: {},
    },
    isLoading: isOverviewLoading,
  } = useOverviewData({
    imei: device?.imei ?? '',
    startDate: strStartDate,
    endDate: strEndDate,
    enabled: !!visible && isValidRange && !isSelectingCalendar,
  });

  const handleDateChange = (type?: 'start' | 'end', date?: Dayjs) => {
    if (!type || !date) return;

    if (type === 'start') {
      setIsSelectingCalendar(true);
      setStartDate(date);
      return;
    }
    setIsSelectingCalendar(false);
    setEndDate(date);
  };

  if (!visible) {
    return undefined;
  }

  if (selectedPreviewDate) {
    return (
      <div className=' flex h-full w-[360px] max-w-md flex-col gap-3 rounded-xl bg-white-1000 p-4 shadow-panel'>
        <div className='relative flex w-full flex-row items-center justify-center'>
          <Icon
            className='absolute left-0 cursor-pointer'
            src={images.Icon.ChevronLeft}
            onClick={onGoBack}
          />
          <div className={`${BodyMdExtend} ${FontBold}`}>
            {headerPreviewDate || 'Lịch sử di chuyển'}
          </div>
        </div>
        <RRScrollView className='w-[calc(100%+12px)]'>
          <div className='h-full w-[calc(100%-12px)] overflow-x-hidden'>
            <DeviceJourneyDetailInfo
              data={currentViewData as DataItem}
              selectedDate={selectedPreviewDate}
              hideSectionHeader={true}
              timezone={device?.timezone}
            />
          </div>
        </RRScrollView>
      </div>
    );
  }

  return (
    <div className=' flex h-full w-[360px] max-w-md flex-col gap-3 rounded-xl bg-white-1000 shadow-panel'>
      <div className='relative flex w-full flex-row items-center justify-center p-4'>
        <Icon
          className='absolute left-4 cursor-pointer'
          src={images.Icon.ChevronLeft}
          onClick={onGoBack}
        />
        <div className={`${BodyMdExtend} ${FontBold}`}>
          {headerPreviewDate || 'Lịch sử di chuyển'}
        </div>
      </div>
      <div className='flex w-full flex-col gap-3 px-4'>
        <div className='w-full gap-2 rounded-lg border-none bg-grey-50 px-3 py-2 text-left text-sm uppercase text-grey-600'>
          {`${device?.device_plate_number || ''} - ${
            device?.device_name || ''
          }`}
        </div>
        <RRCalendar
          startDate={startDate}
          endDate={endDate}
          maxDateTime={new Date()}
          // minDateTime={dayjs().subtract(30, 'day').toDate()}
          onSelect={handleDateChange}
        />
      </div>
      {isHistoryLoading || isOverviewLoading ? (
        <div className='my-10 flex w-full items-center justify-center p-4'>
          <Spinner />
        </div>
      ) : (
        <DeviceJourneyInfo
          overviewData={(overviewData as any).data}
          historyData={historyData as any}
          onPreviewClick={onViewPreview}
          device={device}
        />
      )}
    </div>
  );
};

export default DeviceHistory;
