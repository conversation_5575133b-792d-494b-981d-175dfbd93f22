import React from 'react';

import dayjs, { Dayjs } from 'dayjs';
import tw from 'tailwind-styled-components';

import { HistoryData, OverviewData } from 'features/Map/types';
import { t } from 'i18next';
import type { DeviceType } from 'types/device';
import { convertMeterToKm } from 'utils';

import images from 'assets/images';
import { H4 } from 'assets/styles';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import DeviceJourneyDetailInfo from './DeviceJourneyDetailInfo';
import { formatDuration } from './utils';

interface DeviceJourneyInfoProps {
  className?: string;
  onPreviewClick?: (date?: Dayjs, data?: any) => void;
  historyData: HistoryData;
  overviewData?: OverviewData;
  device?: DeviceType;
}

const DeviceJourneyInfo: React.FC<DeviceJourneyInfoProps> = ({
  className,
  onPreviewClick,
  historyData,
  overviewData,
  device,
}) => {
  const { data = [] } = historyData || {};

  return (
    <DeviceJourneyInfoWrapper id='history' className={className}>
      <div className='flex flex-col gap-3 px-4 transition-all duration-500'>
        <InfoSection>
          <i className={`${H4} font-bold leading-[40px]`}>
            {convertMeterToKm(overviewData?.total_distance, true)}
          </i>
          <div className='text-sm font-medium leading-[20px] text-grey-600'>
            {t('map.movingRoad')}
          </div>
        </InfoSection>
        <div className='flex flex-row items-start justify-start gap-10 self-stretch text-xs text-grey-600'>
          <div className='flex flex-row items-start justify-start gap-2 overflow-hidden'>
            <img className='relative size-4' src={images.Icon.ParkingSign} />
            <div className='flex flex-col items-start justify-start gap-0.5'>
              <MainLabel>{t('map.movingTiming')}</MainLabel>
              <SubLabel>{formatDuration(overviewData?.move_time)}</SubLabel>
            </div>
          </div>
          <div className='flex flex-row items-start justify-start gap-2 overflow-hidden'>
            <img
              className='relative size-4'
              src={images.Icon.WarningElectric}
            />
            <div className='flex flex-col items-start justify-start gap-0.5'>
              <MainLabel>{t('map.stopTiming')}</MainLabel>
              <SubLabel>{formatDuration(overviewData?.stop_time)}</SubLabel>
            </div>
          </div>
        </div>
      </div>
      <div className='relative mt-3 h-px w-full bg-grey-100' />
      <div className='h-[calc(100vh-316px)]'>
        <RRScrollView
          scrollbarTrackStyle={{
            right: 4,
          }}
        >
          <div className='px-4'>
            {data.map((item, index) => {
              const currentDate = dayjs(item?.time_from);
              return (
                <>
                  <Container key={item?.time_from?.toString()}>
                    <DeviceJourneyDetailInfo
                      onPreviewClick={() => {
                        onPreviewClick?.(currentDate, item);
                      }}
                      selectedDate={currentDate}
                      data={item}
                      timezone={device?.timezone}
                    />
                  </Container>
                  {index !== data.length - 1 && (
                    <div className='relative ml-[-16px] mt-3 h-px w-[calc(100%+32px)] bg-grey-100' />
                  )}
                </>
              );
            })}
          </div>
        </RRScrollView>
      </div>
    </DeviceJourneyInfoWrapper>
  );
};

export default DeviceJourneyInfo;

const DeviceJourneyInfoWrapper = tw.div`flex flex-col`;
const Container = tw.div`flex flex-col items-center relative`;
const InfoSection = tw.div`flex flex-col`;
const SubLabel = tw.div`text-sm font-semibold leading-[20px] text-black-1000`;
const MainLabel = tw.div`font-medium leading-[16px]`;
