import * as React from 'react';

import { Tooltip } from 'antd';
import { DeviceStatus, StatusColorsMapping } from 'constants/device';
import dayjs from 'dayjs';
import { useProfile } from 'hooks';
import { useNavigate } from 'react-router-dom';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import useDeviceAddress from 'features/DeviceBusiness/hooks/useDeviceAddress';
import {
  getDeviceLabelStatus,
  getDeviceLabelStatusFromPoint,
} from 'features/DeviceBusiness/utils';
import { useMapGeocodeReverse } from 'features/Map/hooks/useMapData';
import { t } from 'i18next';

import { ChevronDownIcon } from 'assets/icons';
import images from 'assets/images';

import { bottomItemActionListing } from './common';

const ListCustomItem = ({
  label,
  value,
  className = '',
  classNameValue = '',
}) => {
  return (
    <div className={twMerge('relative', className)}>
      <span>{label}</span>
      <span className={twMerge('ml-1 text-black-1000', classNameValue)}>
        {value}
      </span>
    </div>
  );
};

const getTimeUpdated = date => {
  if (!date) return 'Mất kết nối';
  return dayjs(date).format('DD/MM/YYYY HH:mm:ss');
};

const convertSecondToHHMM = (seconds: number) => {
  if (seconds === undefined) return '(-)';
  if (seconds === 0) return '0 giờ 0 phút';
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours} giờ ${minutes} phút`;
};

const convertMinuteToHHMM = (minutes: number) => {
  if (minutes === undefined) return '(-)';
  if (minutes === 0) return '0 giờ 0 phút';
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return `${hours} giờ ${remainingMinutes} phút`;
};

const DeviceMapActions: React.FC<any> = ({
  isRealTime,
  className,
  device,
  onItemActionClick,
  selectedItem,
  currentPoint,
}) => {
  const { profile } = useProfile();
  const navigate = useNavigate();
  const secondContainerRef = React.useRef<HTMLDivElement>(null);
  const [fixedHeight, setFixedHeight] = React.useState<number | null>(null);

  const deviceAddress = useDeviceAddress(device);
  const realTimeAddress = useMapGeocodeReverse({
    lat: currentPoint?.latitude,
    lng: currentPoint?.longitude,
  }) as { data: any | undefined };
  const [driver_license, driver_name] =
    // eslint-disable-next-line no-unsafe-optional-chaining
    (device?.driver?.driver_license_and_name || '')?.split(',');

  const [expanded, setExpanded] = React.useState(false);

  // Capture height of second container when not expanded
  React.useEffect(() => {
    if (!expanded && secondContainerRef.current && !fixedHeight) {
      setFixedHeight(secondContainerRef.current.offsetHeight);
    }
  }, [expanded, fixedHeight]);

  const infoDevice = React.useMemo(() => {
    if (isRealTime) {
      return {
        coordinate: `${currentPoint?.latitude || 0}, ${
          currentPoint?.longitude || 0
        }`,
        status: currentPoint?.status,
        address: realTimeAddress,
        updatedAt: currentPoint?.timestamp,
        position: {
          lat: currentPoint?.latitude || 0,
          lng: currentPoint?.longitude || 0,
        },
        timezone: device?.timezone,
        device_label_status: getDeviceLabelStatusFromPoint(currentPoint),
        ...(StatusColorsMapping[currentPoint?.status] || {}),
      };
    }
    return {
      coordinate: `${device?.gps?.latitude || 0}, ${
        device?.gps?.longitude || 0
      }`,
      status: device?.status,
      address: deviceAddress,
      updatedAt:
        device?.device_status_info?.timestamp ||
        device?.timestamp ||
        device?.gps?.timestamp,
      position: {
        lat: device?.gps?.latitude || 0,
        lng: device?.gps?.longitude || 0,
      },
      timezone: device?.timezone,
      device_status_info: device?.device_status_info,
      device_label_status: getDeviceLabelStatus(device),
      ...(StatusColorsMapping[device?.status] || {}),
    };
  }, [isRealTime, currentPoint, deviceAddress, realTimeAddress, device]);

  if (!device) {
    return;
  }

  const handleRedirectToDriver = () => {
    // profile?active=driver
    navigate(`/profile?active=driver`);
  };

  const handleOpenGoogleMap = () => {
    window.open(
      `https://www.google.com/maps/search/?api=1&query=${infoDevice.position.lat},${infoDevice.position.lng}`,
      '_blank',
    );
  };

  return (
    <Container className={className}>
      <div
        className={`flex flex-row justify-center gap-1 ${
          expanded ? 'items-start' : 'items-stretch'
        }`}
      >
        <div
          className='box-border flex flex-1 flex-col items-start justify-between gap-1 rounded-xl bg-white-1000 p-2 shadow-panel'
          style={fixedHeight ? { height: `${fixedHeight}px` } : {}}
        >
          {bottomItemActionListing.map(item => {
            return (
              <Tooltip
                className='menu-collapse-tooltip'
                title={item.title}
                placement='left'
                arrow={{
                  pointAtCenter: true,
                  arrowPointAtCenter: true,
                }}
                color='white'
                overlayInnerStyle={{
                  padding: '12px 16px',
                  borderRadius: 'var(--Tooltip-radiusCorner, 8px)',
                  background: 'var(--Tooltip-background, #FFF)',
                  boxShadow: '0px 1px 8px 0px rgba(0, 0, 0, 0.20)',
                  color: '#000',
                  fontSize: '14px',
                  fontWeight: '500',
                }}
                key={item.source}
              >
                <ActionContainer
                  role='button'
                  onClick={() => onItemActionClick?.(item.type)}
                >
                  <ActionIcon
                    className={`${
                      selectedItem === item.type ? 'border-black-1000' : ''
                    }`}
                  >
                    <img src={item.source} />
                  </ActionIcon>
                </ActionContainer>
              </Tooltip>
            );
          })}
        </div>
        <div
          ref={secondContainerRef}
          className='box-border flex w-[248px] shrink-0 flex-col items-start justify-start rounded-xl bg-white-1000 px-4 py-2 shadow-panel'
        >
          <div className='flex flex-col items-start justify-start gap-1 self-stretch'>
            <div className='flex flex-row items-center justify-between self-stretch text-grey-400'>
              <div className='flex flex-row items-center justify-start'>
                <div className='relative font-medium leading-[16px]'>
                  {`${t('map.deviceInfo')}`}
                </div>
              </div>
              {/* rotate down and up */}
              <div
                className={`cursor-pointer text-black-1000 transition-transform duration-200 ease-in-out ${
                  expanded ? 'rotate-180' : ''
                }`}
                role='button'
                onClick={() => setExpanded(prev => !prev)}
              >
                <ChevronDownIcon />
              </div>
            </div>
            {/* <b className='relative self-stretch leading-[16px] text-black-1000'>
              {device?.device_plate_number} -{device?.device_name}
            </b> */}
            <Status
              bg={infoDevice?.backgroundColor}
              color={infoDevice.color}
              className={`w-fit text-xs`}
            >
              {infoDevice.device_label_status}
            </Status>
            <ListCustomItem
              label={`${t('map.licenseNo')}:`}
              value={device?.device_plate_number || ''}
            />
            <ListCustomItem
              label={`${t('map.address')}:`}
              value={infoDevice.address || ''}
              className='truncate-3-lines !break-normal'
            />
            <ListCustomItem
              label={`${t('map.lastUpdated')}:`}
              value={getTimeUpdated(infoDevice.updatedAt)}
            />
            <ListCustomItem
              label={`${t('map.location')}:`}
              className='flex w-full items-center'
              classNameValue='text-blue-200 [text-decoration:underline]'
              value={
                <div
                  className='cursor-pointer'
                  role='button'
                  onClick={handleOpenGoogleMap}
                >
                  {infoDevice.coordinate || ''}
                </div>
              }
            />
          </div>
          {expanded && (
            <>
              <div className='my-2 flex flex-col items-start justify-start gap-1 self-stretch border-t border-grey-100 pt-2'>
                <div className='flex flex-row items-center justify-start text-grey-400'>
                  <div className='relative font-medium leading-[16px]'>
                    {t('map.otherData')}
                  </div>
                </div>
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label='Dữ liệu di động:'
                  value={`${
                    device?.device_status_info?.gsm_signal_percent || 0
                  }%`}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label='Tín hiệu vệ tinh:'
                  value={`${device?.gps?.satellites || 0}`}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.accumulator')}:`}
                  value={`${device?.device_status_info?.power || 0}V`}
                />
                <Row>
                  <div className='flex flex-row items-start justify-start gap-0.5'>
                    <div className='relative leading-[16px]'>{`${t(
                      'map.ignitionSwitch',
                    )}:`}</div>
                  </div>
                  <div className='flex flex-row items-center justify-center gap-1'>
                    {String(device?.device_status_info?.acc) === '1' ? (
                      <div className='relative font-semibold leading-[16px]  text-green-200'>
                        Bật
                      </div>
                    ) : (
                      <div className='relative font-semibold leading-[16px] text-red-200'>
                        Tắt
                      </div>
                    )}
                    <div className='relative font-semibold leading-[16px] text-black-1000'>
                      {convertSecondToHHMM(
                        device?.device_status_info?.acc_duration_seconds,
                      )}
                    </div>
                  </div>
                </Row>
                <Row>
                  <div className='flex flex-row items-start justify-start gap-0.5'>
                    <div className='relative leading-[16px]'>{`${t(
                      'map.engine',
                    )}:`}</div>
                  </div>
                  <div className='flex flex-row items-center justify-center gap-1'>
                    {String(device?.device_status_info?.engine) === '1' ? (
                      <div className='relative font-semibold leading-[16px]  text-green-200'>
                        Bật
                      </div>
                    ) : (
                      <div className='relative font-semibold leading-[16px] text-red-200'>
                        Tắt
                      </div>
                    )}
                    <div className='relative font-semibold leading-[16px] text-black-1000'>
                      {convertSecondToHHMM(
                        device?.device_status_info?.engine_duration_seconds,
                      )}
                    </div>
                  </div>
                </Row>
              </div>
              <div className='flex flex-col items-start justify-start gap-1 self-stretch border-t border-grey-100 pt-2'>
                <div className='flex flex-row items-center justify-between self-stretch text-grey-400'>
                  <div className='flex flex-row items-center justify-start'>
                    <div className='relative font-medium leading-[16px]'>
                      {`${t('map.driverInfo')}`}
                    </div>
                  </div>
                  {profile?.is_end_user && (
                    <div
                      role='button'
                      className='flex cursor-pointer items-center justify-center gap-1'
                      onClick={handleRedirectToDriver}
                    >
                      <img
                        className='relative size-4'
                        src={images.Icon.PlusCircle}
                      />
                    </div>
                  )}
                </div>
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label='Họ tên:'
                  value={driver_name || '(-)'}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label='Giấy phép:'
                  value={driver_license || '(-)'}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingOnTheTime')}:`}
                  value={convertMinuteToHHMM(
                    device?.operation?.time_of_continuous_driving || 0,
                  )}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingDuringDay')}:`}
                  value={convertMinuteToHHMM(
                    device?.operation?.driving_time_per_day || 0,
                  )}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingDuringWeek')}:`}
                  value={convertMinuteToHHMM(
                    device?.operation?.driving_time_this_week || 0,
                  )}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingOver4h')}:`}
                  value={`${device?.operation?.over_4h_count || 0}`}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </Container>
  );
};

export default DeviceMapActions;

const Container = tw.div`flex flex-col items-end justify-start bg-transparent text-left text-xs text-grey-600`;
const ActionContainer = tw.div`flex w-8 flex-row items-start justify-start self-stretch cursor-pointer`;
const ActionIcon = tw.div`p-space-inset-3 box-border flex size-8 flex-row items-center justify-center rounded-lg border-[1.5px] border-solid border-grey-100`;
const Row = tw.div`flex flex-row items-start justify-between self-stretch relative leading-[16px]`;

export const Status = tw.div<{ bg: string; color: string }>`
  rounded-md px-1.5 py-1 inline-block
  ${props => props.bg && `${props.bg}`}
  ${props => props.color && `${props.color}`}
`;
