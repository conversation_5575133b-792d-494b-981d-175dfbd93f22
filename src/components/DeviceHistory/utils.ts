import dayjs from 'dayjs';

export const formatTimeRange = ({
  startTimestamp,
  endTimestamp,
  timezone,
}): string => {
  const startDate = dayjs(startTimestamp).tz(timezone);
  const endDate = dayjs(endTimestamp).tz(timezone);

  const strStartDate = startDate.format('HH:mm');
  const strEndDate = endDate.format('HH:mm');

  const start = dayjs(startDate.format('YYYY/MM/DD HH:mm'), 'YYYY/MM/DD HH:mm');
  const end = dayjs(endDate.format('YYYY/MM/DD HH:mm'), 'YYYY/MM/DD HH:mm');

  const diff = dayjs.duration(end.diff(start));
  const hours = diff.hours();
  const minutes = diff.minutes();

  return `${strStartDate} - ${strEndDate} (${hours} giờ ${minutes} phút)`;
};

export const formatDuration = (timestamp?: number): string => {
  if (timestamp === undefined) {
    return '0 giờ 0 phút';
  }

  const totalMinutes = Math.floor(timestamp / 60000);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours} giờ ${minutes} phút`;
};
