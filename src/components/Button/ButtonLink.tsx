import React, { forwardRef } from 'react';

import { But<PERSON>, Spinner } from './styled';

export interface LinkButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  children: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  htmlType?: 'button' | 'submit' | 'reset';

  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}
const LinkButton = forwardRef<HTMLButtonElement, LinkButtonProps>(
  (
    {
      children,
      loading = false,
      disabled = false,
      icon,
      iconPosition = 'left',
      size = 'medium',
      fullWidth = false,
      htmlType = 'button',
      onClick,
      ...rest
    },
    ref,
  ) => {
    const isDisabled = disabled || loading;

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (isDisabled) {
        e.preventDefault();
        return;
      }
      onClick?.(e);
    };

    return (
      <Button
        ref={ref}
        type={htmlType}
        disabled={isDisabled}
        $size={size}
        $variant='link'
        $fullWidth={fullWidth}
        $disabled={isDisabled}
        onClick={handleClick}
        {...rest}
      >
        {loading && <Spinner />}
        {!loading && icon && iconPosition === 'left' && icon}
        {children}
        {!loading && icon && iconPosition === 'right' && icon}
      </Button>
    );
  },
);

export default LinkButton;
