import React from 'react';

import { Select, SelectProps, Tooltip } from 'antd';
import tw from 'tailwind-styled-components';

import { getCombinedInputStyles } from '../FormField/styles';
import './select.css';

interface Props extends SelectProps {
  className?: string;
}

const RRSelect: React.FC<Props> = ({
  className,
  tagRender,
  placeholder,
  ...props
}) => {
  return (
    <StyledSelect
      variant='borderless'
      className={`${getCombinedInputStyles()} ${className || ''}`}
      maxTagCount={1}
      showSearch={false}
      placeholder={<div className='text-black-1000'>{placeholder}</div>}
      tagRender={
        tagRender
          ? tagRender
          : ({ label }) => {
              const labelDisplay =
                props.value && Array.isArray(props.value)
                  ? props.value
                      .map(
                        v =>
                          props.options?.find(item => item.value === v)?.label,
                      )
                      .join(', ')
                  : label;

              return (
                <TagItem className='bg-gray-200'>
                  <Tooltip title={labelDisplay}>
                    <TagLabel>{labelDisplay}</TagLabel>
                  </Tooltip>
                </TagItem>
              );
            }
      }
      {...props}
    />
  );
};

export default RRSelect;

const StyledSelect = tw(Select)`w-full rounded-lg border-none bg-grey-50 p-1`;
const TagItem = tw.div`pl-2 w-full`;
const TagLabel = tw.span`text-black-1000 text-sm font-medium leading-normal absolute inset-0 overflow-hidden truncate whitespace-nowrap inset-y-0 my-auto h-fit`;
