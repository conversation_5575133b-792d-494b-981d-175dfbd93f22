import React, { useEffect, useMemo, useState } from 'react';

import tw from 'tailwind-styled-components';

import images from 'assets/images';
import { InputBase } from 'assets/styles';
import { Icon } from 'components';

interface RRToastMessageProps {
  type: 'info' | 'success' | 'failed' | 'warning';
  content: string;
  visible: boolean;
}

const getStyles = type => {
  switch (type) {
    case 'info':
      return {
        borderColor: 'border-blue-200',
        backgroundColor: 'bg-blue-10',
        icon: images.Icon.InfoCircle,
      };
    case 'success':
      return {
        borderColor: 'border-green-200',
        backgroundColor: 'bg-green-10',
        icon: images.Icon.CheckCircle,
      };
    case 'failed':
      return {
        borderColor: 'border-red-200',
        backgroundColor: 'bg-red-10',
        icon: images.Icon.ToastFailed,
      };
    case 'warning':
      return {
        borderColor: 'border-yellow-300',
        backgroundColor: 'bg-yellow-100',
        icon: images.Icon.WarningCircle,
      };
    default:
      return {
        borderColor: 'border-blue-200',
        backgroundColor: 'bg-blue-10',
        icon: images.Icon.InfoCircle,
      };
  }
};

const RRToastMessage: React.FC<RRToastMessageProps> = ({
  type,
  content,
  visible,
}) => {
  const [isVisible, setIsVisible] = useState(visible);

  useEffect(() => {
    if (visible) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [visible]);

  const styles = useMemo(() => getStyles(type), [type]);

  return (
    <ToastMessageContainer
      className={`${styles.backgroundColor} ${styles.borderColor} ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <Icon src={styles.icon} />
      <div className={`${InputBase} ml-2`}>{content}</div>
    </ToastMessageContainer>
  );
};

export default RRToastMessage;

const ToastMessageContainer = tw.div`
  fixed left-1/2 top-10 flex -translate-x-1/2 items-center rounded-lg border p-3 transition-opacity duration-500 opacity-0 z-[9999]
`;
