import { RRToastMessage } from 'components';
import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';

const showToast = (
  type: 'info' | 'success' | 'failed' | 'warning',
  content: string,
) => {
  const ToastContainer = () => {
    const [visible, setVisible] = useState(true);

    useEffect(() => {
      const timer = setTimeout(() => {
        setVisible(false);
      }, 5000);

      return () => clearTimeout(timer);
    }, []);

    return <RRToastMessage type={type} content={content} visible={visible} />;
  };

  const div = document.createElement('div');
  document.body.appendChild(div);
  const root = createRoot(div);
  root.render(<ToastContainer />);

  setTimeout(() => {
    root.unmount();
    document.body.removeChild(div);
  }, 6000);
};

export default showToast;
