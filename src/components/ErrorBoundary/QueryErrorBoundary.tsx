import React from 'react';

import { ErrorBoundary } from 'react-error-boundary';

import showToast from 'components/RRToastMessage/Toast';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
}) => {
  return (
    <div className='flex h-screen flex-col items-center justify-center p-4'>
      <h1 className='text-red-600 mb-2 text-2xl font-semibold'>
        Đã có lỗi xảy ra
      </h1>
      <p className='text-gray-600 mb-4 text-center'>
        {error.message || 'Vui lòng thử lại sau'}
      </p>
      <button
        onClick={resetErrorBoundary}
        className='rounded bg-brand-300 px-4 py-2 text-black-1000 hover:bg-brand-400'
      >
        Thử lại
      </button>
    </div>
  );
};

interface QueryErrorBoundaryProps {
  children: React.ReactNode;
}

export const QueryErrorBoundary: React.FC<QueryErrorBoundaryProps> = ({
  children,
}) => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={error => {
        console.error('Query Error Boundary:', error);
        showToast('failed', 'Đã có lỗi xảy ra, vui lòng thử lại');
      }}
      onReset={() => {
        // Reset logic nếu cần
        window.location.reload();
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default QueryErrorBoundary;
