import React, { useRef } from 'react';

import { Button, Image, Upload } from 'antd';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { sortBy, uniqBy } from 'lodash';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { Icon } from 'components';
import showToast from 'components/RRToastMessage/Toast';

const { Dragger } = Upload;

const MAX_FILE_SIZE_MB = 25;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

const beforeUpload = (file: File) => {
  const isLt25MB = file.size <= MAX_FILE_SIZE_BYTES;
  if (!isLt25MB) {
    return Upload.LIST_IGNORE;
  }
  return true;
};

interface RRUploadProps {
  fileType: 'image' | 'file';
  fileList: UploadFile[];
  onChange: (fileList: UploadFile[]) => void;
}

const RRUpload: React.FC<RRUploadProps> = ({
  fileType,
  fileList,
  onChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    if (fileType === 'image') {
      // show toast if  newFileList has file with type not image/jpeg, image/png, image/gif
      let isValidFile = true;
      newFileList.forEach(file => {
        if (
          file &&
          file.type &&
          !['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
        ) {
          isValidFile = false;
          return;
        }
      });
      if (!isValidFile) {
        showToast('failed', 'File upload phải là file jpg, png, gif');
        return;
      }
    }
    const uniqueFileList = uniqBy([...fileList, ...newFileList], 'uid');
    const sortedFileList = sortBy(uniqueFileList, 'timestamp');
    onChange(sortedFileList);
  };

  const handleRemove = (file: UploadFile) => {
    const newFileList = fileList.filter(f => f.uid !== file.uid);
    onChange(newFileList);
  };

  return (
    <Container className='bg-white-1000' ref={containerRef}>
      {fileList.length === 0 ? (
        <Dragger
          beforeUpload={beforeUpload}
          onChange={handleChange}
          style={{ borderStyle: 'solid', backgroundColor: 'white' }}
        >
          <UploadIcon>
            <Icon src={images.Icon.Plus} />
          </UploadIcon>
          <UploadText>{t('upload.upload')}</UploadText>
        </Dragger>
      ) : (
        <>
          <div className='flex items-center gap-1'>
            {fileList.map(file => (
              <ImageContainer key={file.uid}>
                <Image
                  key={file.uid}
                  src={URL.createObjectURL(file.originFileObj as Blob)}
                  alt={file.name}
                  wrapperClassName='size-full  rounded-lg'
                  className='size-full rounded-lg object-contain'
                  preview={false}
                />
                <Button
                  shape='circle'
                  icon={<Icon src={images.Icon.TrashGrey} />}
                  onClick={() => handleRemove(file)}
                  className='bg-white hover:bg-white absolute bottom-2 right-2  border-none shadow-none'
                />
              </ImageContainer>
            ))}
            {fileList.length < 6 && (
              <Upload
                showUploadList={false}
                onChange={handleChange}
                beforeUpload={beforeUpload}
                className='size-[78px] rounded-lg border border-grey-100'
              >
                <UploadButton type='button'>
                  <UploadIcon>
                    <Icon src={images.Icon.Plus} />
                  </UploadIcon>
                  <UploadText>{t('upload.upload')}</UploadText>
                </UploadButton>
              </Upload>
            )}
          </div>
        </>
      )}
    </Container>
  );
};

export default RRUpload;

const Container = tw.div`w-full `;
const UploadIcon = tw.p`mb-2 flex justify-center`;
const UploadText = tw.p``;
const ImageContainer = tw.div`relative size-[78px] rounded-lg border border-grey-100 p-0.5`;
const UploadButton = tw.button`size-[78px] border-none bg-none`;
