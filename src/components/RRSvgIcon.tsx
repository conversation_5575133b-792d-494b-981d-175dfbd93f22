import React from 'react';
import { ReactSVG, Props } from 'react-svg';
type RRSvgIconProps = Pick<
  Props,
  'src' | 'className' | 'onClick' | 'wrapper'
> & {
  color?: string;
};

const RRSvgIcon: React.FC<RRSvgIconProps> = ({ color, ...props }) => {
  return (
    <ReactSVG
      {...props}
      fill={color}
      beforeInjection={svg => {
        if (color) {
          svg.setAttribute('fill', color);
        }
      }}
    />
  );
};

export default RRSvgIcon;
