import tw from 'tailwind-styled-components';

export const DeviceFormWrapper = tw.div`flex h-full flex-col gap-3 rounded-2xl border border-grey-100 p-6 pb-[86px]`;
export const Grid = tw.div`grid gap-3`;
export const Label = tw.label`mb-1 block text-left text-black-1000`;
export const Status = tw.div<{ bg: string; color: string }>`
  rounded-md px-1.5 py-1 inline-block
  ${props => props.bg && `${props.bg}`}
  ${props => props.color && `${props.color}`}
`;
