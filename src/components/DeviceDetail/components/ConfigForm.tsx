import { EngineSignalOptions, TimezoneOptions } from 'constants/device';
import { useProfile } from 'hooks/useProfile';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { RRFieldInput, RRFieldSelect } from 'components/FormField';

import { DeviceFormWrapper } from '../styles';

const ConfigForm = () => {
  const { t } = useTranslation();
  const { control, formState } = useFormContext();
  const { errors } = formState;
  const { profile } = useProfile();

  return (
    <DeviceFormWrapper className='grid grid-cols-2'>
      <RRFieldInput
        id='gprs_interval'
        control={control}
        label={t('deviceDetail.gprsTime')}
        placeholder={t('deviceDetail.gprsTime')}
        errors={errors}
        disabled={profile?.is_end_user}
      />
      <RRFieldInput
        id='min_speed'
        control={control}
        label={t('deviceDetail.minSpeedFilter')}
        placeholder={t('deviceDetail.minSpeedFilter')}
        errors={errors}
        disabled={profile?.is_end_user}
      />
      <RRFieldInput
        id='stop_time'
        control={control}
        label={t('deviceDetail.stopTime')}
        placeholder={t('deviceDetail.stopTime')}
        errors={errors}
        disabled={profile?.is_end_user}
      />
      <RRFieldSelect
        id='timezone'
        control={control}
        label={t('deviceDetail.timeZone')}
        options={TimezoneOptions}
        errors={errors}
        disabled={true}
      />
      <RRFieldSelect
        id='sensor_type'
        control={control}
        label={t('deviceDetail.engineSignal')}
        options={EngineSignalOptions}
        errors={errors}
        disabled={profile?.is_end_user}
      />
      <RRFieldInput
        id='stop_distance'
        control={control}
        label={t('deviceDetail.distanceCoefficient')}
        placeholder={t('deviceDetail.distanceCoefficient')}
        errors={errors}
        disabled={profile?.is_end_user}
      />
    </DeviceFormWrapper>
  );
};
export default ConfigForm;
