import { useMemo } from 'react';

import { DeviceStatusLabel, StatusColorsMapping } from 'constants/device';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { getDeviceLabelStatus } from 'features/DeviceBusiness/utils';

import { BodyMdExtend, BodySm, FontSemibold } from 'assets/styles';
import {
  RRFieldDatePicker,
  RRFieldInput,
  RRFieldPassword,
} from 'components/FormField';

import { DeviceFormWrapper, Grid, Label, Status } from '../styles';

const StatusInfoForm = ({ device }) => {
  const { t } = useTranslation();

  const { control, formState } = useFormContext();
  const { errors } = formState;

  const statusStyle = useMemo(() => {
    return device?.status ? StatusColorsMapping[device?.status] : {};
  }, [device?.status]);

  return (
    <DeviceFormWrapper>
      <div className='flex flex-col'>
        <Label className={`${BodyMdExtend} ${FontSemibold}`}>
          {t('deviceDetail.status')}
        </Label>
        <Status
          bg={statusStyle?.backgroundColor}
          color={statusStyle.color}
          className={`${BodySm} w-fit`}
        >
          {getDeviceLabelStatus(device)}
        </Status>
      </div>
      <Grid className='grid-cols-4 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='firmware'
          control={control}
          label='Firmware'
          placeholder='Firmware'
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='coordinates'
          control={control}
          label='Tọa độ'
          placeholder='Tọa độ'
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='power'
          control={control}
          label='Nguồn (V)'
          placeholder='Nguồn (V)'
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='speed'
          control={control}
          label='Tốc độ (km/h)'
          placeholder='Tốc độ (km/h)'
          errors={errors}
          disabled={true}
        />
      </Grid>
      <Grid className='grid-cols-4 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='engine'
          control={control}
          label='Động cơ'
          placeholder='Động cơ'
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='acc'
          control={control}
          label='Khóa điện'
          placeholder='Khóa điện'
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='gsm_signal'
          control={control}
          label='Dữ liệu di động (%)'
          placeholder='Dữ liệu di động'
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='satellites'
          control={control}
          label='Tín hiệu vệ tinh'
          placeholder='Tín hiệu vệ tinh'
          errors={errors}
          disabled={true}
        />
      </Grid>
      <Grid className='grid-cols-2 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='max_allowable_speed'
          control={control}
          label='Tốc độ giới hạn (km/h)'
          placeholder='Tốc độ giới hạn (km/h)'
          errors={errors}
        />
        <RRFieldInput
          id='not_turn_off_the_ignition_time'
          control={control}
          label='Thời gian quên tắt khóa'
          placeholder='Thời gian quên tắt khóa'
          errors={errors}
        />
      </Grid>
      <Grid className='grid-cols-2'>
        <RRFieldPassword
          id='device_pin'
          control={control}
          label='Mã PIN thiết bị'
          placeholder='Mã PIN thiết bị'
          errors={errors}
          autoComplete='new-password'
        />
        <RRFieldDatePicker
          id='updated_at'
          control={control}
          label='Cập nhật mới nhất'
          placeholder='Cập nhật mới nhất'
          errors={errors}
          dateFormat='DD/MM/YYYY HH:mm:ss'
          disabled={true}
        />
      </Grid>
    </DeviceFormWrapper>
  );
};

export default StatusInfoForm;
