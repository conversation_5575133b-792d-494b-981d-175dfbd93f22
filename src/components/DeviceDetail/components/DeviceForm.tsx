import {
  DepartmentOfTransportOptions,
  DeviceOptions,
  ServicePackageOptions,
  VehicleTypeOptions,
} from 'constants/device';
import { useProfile } from 'hooks/useProfile';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import images from 'assets/images';
import {
  RRFieldDatePicker,
  RRFieldInput,
  RRFieldRadio,
  RRFieldSelect,
} from 'components/FormField';

import { DeviceFormWrapper, Grid } from '../styles';

const DeviceInfoForm = () => {
  const { t } = useTranslation();
  const { control, formState } = useFormContext();
  const { errors } = formState;
  const { profile } = useProfile();

  return (
    <DeviceFormWrapper>
      <div className='w-full'>
        <RRFieldInput
          id='username'
          control={control}
          label={t('deviceDetail.account')}
          placeholder={t('deviceDetail.account')}
          errors={errors}
          disabled={true}
        />
      </div>
      <Grid className='grid-cols-3 border-b border-grey-100 pb-3'>
        <RRFieldSelect
          required={true}
          id='transport_department_id'
          control={control}
          label={t('deviceDetail.transportDepartment')}
          options={DepartmentOfTransportOptions}
          errors={errors}
          disabled={profile?.is_end_user}
        />
        <RRFieldSelect
          required={true}
          id='transport_type'
          control={control}
          label={t('deviceDetail.type')}
          options={VehicleTypeOptions}
          errors={errors}
          disabled={profile?.is_end_user}
        />
        <RRFieldRadio
          id='is_allow_data_transport'
          control={control}
          label={t('deviceDetail.radioOption')}
          errors={errors}
          disabled={profile?.is_end_user}
        />
      </Grid>
      <Grid className='grid-cols-3 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='device_name'
          control={control}
          label={t('deviceDetail.deviceName')}
          placeholder={t('deviceDetail.deviceName')}
          errors={errors}
          className='uppercase'
        />
        <RRFieldInput
          id='device_plate_number'
          control={control}
          label={t('deviceDetail.plate')}
          placeholder={t('deviceDetail.plate')}
          errors={errors}
        />
        <RRFieldInput
          id='imei'
          control={control}
          label={t('deviceDetail.IMEI')}
          placeholder={t('deviceDetail.IMEI')}
          errors={errors}
          disabled={true}
        />
      </Grid>
      <Grid className='grid-cols-2'>
        <RRFieldInput
          id='device_ccid'
          control={control}
          label={t('deviceDetail.CCID')}
          placeholder={t('deviceDetail.CCID')}
          errors={errors}
          disabled
        />
        <RRFieldInput
          id='device_sim_number'
          control={control}
          label={t('deviceDetail.simNumber')}
          placeholder={t('deviceDetail.simNumber')}
          errors={errors}
          disabled={profile?.is_end_user}
        />
        <RRFieldSelect
          id='device_category'
          control={control}
          label={t('deviceDetail.deviceType')}
          options={DeviceOptions}
          errors={errors}
          disabled={profile?.is_end_user}
        />
        <RRFieldSelect
          id='service_package'
          control={control}
          label={t('deviceDetail.servicePackage')}
          options={ServicePackageOptions}
          errors={errors}
          disabled={profile?.is_end_user}
        />
        <RRFieldDatePicker
          id='activated_at'
          control={control}
          label={t('deviceDetail.activationDate')}
          placeholder={t('deviceDetail.activationDate')}
          className='w-full'
          errors={errors}
          suffixIcon={images.Icon.CalendarSchedule}
          disabled={true}
        />
        <RRFieldDatePicker
          id='expired_at'
          control={control}
          label={t('deviceDetail.expirationDate')}
          placeholder={t('deviceDetail.expirationDate')}
          className='w-full'
          errors={errors}
          suffixIcon={images.Icon.CalendarSchedule}
          disabled={profile?.is_end_user || !!profile?.parent_info} // disabled with distributor level 2
        />
      </Grid>
    </DeviceFormWrapper>
  );
};

export default DeviceInfoForm;
