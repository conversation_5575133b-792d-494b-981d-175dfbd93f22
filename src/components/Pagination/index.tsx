import { Pagination } from 'antd';
import tw from 'tailwind-styled-components';

import { RRSelect } from 'components/FormField';

import './style.css';

interface Props {
  className?: string;
  page: number;
  pageSize: number;
  total: number;
  onPageChange?: (page: number, pageSize: number) => void;
  onChangePerPage?: (pageSize: number) => void;
}

const CustomPagination = (props: Props) => {
  const {
    className = '',
    page,
    pageSize,
    total,
    onPageChange,
    onChangePerPage,
  } = props;

  const startItem = (page - 1) * pageSize + 1;
  const endItem = page * pageSize;

  return (
    <Flex className={`items-center justify-between ${className}`}>
      <Flex className=' w-fit items-center gap-1'>
        <strong>
          {startItem}-{endItem}
        </strong>
        <span>của</span>
        <strong>{total}</strong>
      </Flex>
      <Flex className='items-center justify-center'>
        <Pagination
          current={page}
          pageSize={pageSize}
          total={total}
          onChange={onPageChange}
          showQuickJumper={false}
          className={`custom-pagination ${
            total <= pageSize ? 'only-page' : ''
          }`}
          showSizeChanger={false}
        />
      </Flex>
      <Flex className=' items-center space-x-2'>
        <span className='text-gray-600'>Xem</span>
        <RRSelect
          id='pageSize'
          name='pageSize'
          options={[
            { value: '10', label: '10' },
            { value: '20', label: '20' },
            { value: '50', label: '50' },
            { value: '100', label: '100' },
          ]}
          className='w-fit'
          containerClassName='!w-fit max-w-fit'
          value={pageSize?.toString()}
          onChange={value => onChangePerPage?.(Number(value))}
        />
        <span className='text-gray-600 whitespace-nowrap'>mỗi trang</span>
      </Flex>
    </Flex>
  );
};
const Flex = tw.div`flex`;

export default CustomPagination;
