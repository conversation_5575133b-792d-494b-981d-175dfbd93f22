import React from 'react';

interface IconProps {
  src: string;
  alt?: string;
  width?: string | number;
  height?: string | number;
  className?: string;
  onClick?: (e?: React.MouseEvent) => void;
}

const Icon: React.FC<IconProps> = ({
  src,
  alt = 'icon',
  width = '20px',
  height = '20px',
  className = '',
  onClick,
}) => {
  if (!src) return null;
  return (
    <img
      onClick={onClick}
      src={src}
      alt={alt}
      style={{ width, height }}
      className={className}
    />
  );
};

export default Icon;
