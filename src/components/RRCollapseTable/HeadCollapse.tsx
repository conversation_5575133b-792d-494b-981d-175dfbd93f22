import { useCallback, useMemo } from 'react';

import { twMerge } from 'tailwind-merge';

import images from 'assets/images';
import { Icon } from 'components';

import LevelItem from './LevelItem';
import { CollapseItemData } from './types';

interface Props {
  showAddMore?: boolean;
  isActive: boolean;
  isSelectedMultiple?: boolean;
  data: CollapseItemData;
  expandIds: string[];
  className?: string;
  onSelect: (
    selectedItem: CollapseItemData,
    path: CollapseItemData[],
    level: number,
  ) => void;
  onExpand: (id: string) => void;
  onAddNewItem?: (data: CollapseItemData) => void;
  onLoadMore?: (data: CollapseItemData) => void;
}

const HeadCollapse = (props: Props) => {
  const {
    isActive,
    isSelectedMultiple,
    data,
    expandIds,
    className,
    onSelect,
    onExpand,
    onAddNewItem,
  } = props;

  const isExpand = useMemo(() => {
    return expandIds.includes(data.id);
  }, [expandIds, data.id]);

  const handleAddNewDevice = useCallback(
    (event?: React.MouseEvent<Element, MouseEvent>) => {
      event?.stopPropagation();
      onAddNewItem?.(data);
    },
    [onAddNewItem, data],
  );

  const handleExpand = useCallback(
    e => {
      e.preventDefault();
      e.stopPropagation();
      if (data?.id && data?.is_distributor) {
        onExpand(data.id);
      }
    },
    [onExpand, data.id],
  );

  const handleSelect = useCallback(() => {
    if (isSelectedMultiple && data?.is_distributor) {
      return;
    }

    onSelect(data, [data], data.level);
  }, [onSelect, data, isSelectedMultiple]);

  if (data?.is_distributor) {
    return (
      <div
        className={twMerge(
          'flex items-center justify-between  py-3 pl-3',
          className,
        )}
        role='button'
        onClick={handleSelect}
      >
        <LevelItem
          isActive={isActive}
          isSelectedMultiple={false}
          data={data}
          onSelect={() => {}}
          className={data?.level === 0 ? 'hover:bg-grey-100' : ''}
        />
        <div className='flex gap-3 pr-0'>
          {onAddNewItem && (
            <Icon onClick={handleAddNewDevice} src={images.Icon.PlusCircle} />
          )}

          <div className='pr-4'>
            <Icon
              onClick={handleExpand}
              src={
                isExpand ? images.Icon.CaretDownSm : images.Icon.CaretRightSm
              }
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className='box-content py-3 pl-3 hover:bg-grey-50'
      role='button'
      onClick={handleSelect}
    >
      <LevelItem
        isActive={isActive}
        isSelectedMultiple={isSelectedMultiple}
        data={data}
        onSelect={() => {}}
      />
    </div>
  );
};

export default HeadCollapse;
