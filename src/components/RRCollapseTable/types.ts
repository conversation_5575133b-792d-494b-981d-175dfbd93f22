export interface LastDevice {
  device_category: string;
  device_imei: string;
}

export interface CollapseItemData {
  id: string;
  key: string; // Added missing key property
  name: string;
  username: string;
  full_name?: string;
  children_count?: number;
  description?: string;
  role_id?: string;
  role_type?: string;
  device_count?: number;
  children?: CollapseItemData[];
  level: number;
  is_distributor?: boolean;
  is_end_user?: boolean;
  last_device?: LastDevice;
  parent_info?: {
    full_name: string;
    id: string;
    role_name: string;
    username: string;
  };
  pagination?: {
    total_count: number;
    total_pages: number;
    page: number;
  };
}

export interface RRCollapseTableProps {
  expandIds: string[];
  selectedItem?: CollapseItemData | null;
  multipleSelectedIds?: string[];
  data: CollapseItemData[];
  isSelectedMultiple?: boolean;
  onSelectId?: (
    selectedItem: CollapseItemData,
    path: CollapseItemData[],
    level: number,
    isDistributor?: boolean,
  ) => void;
  onSelectKey?: (
    selectedItem: CollapseItemData,
    path: CollapseItemData[],
    level: number,
    isDistributor?: boolean,
  ) => void;
  onSelectedMultipleIds?: (selectedIds: string[]) => void;
  onExpand: (id: string) => void;
  onAddNewItem?: (data: CollapseItemData) => void;
  onLoadMore?: (item: CollapseItemData) => void;
}

export interface BaseLevelCollapseProps {
  showAddMore?: boolean;
  isSelectedMultiple?: boolean;
  items: CollapseItemData[];
  onSelect: (
    selectedItem: CollapseItemData,
    path: CollapseItemData[],
    level: number,
  ) => void;
  onLoadMore?: (item: CollapseItemData) => void;
  getActiveLevelItem: (item: CollapseItemData) => boolean;
}

export interface SecondLevelCollapseProps extends BaseLevelCollapseProps {
  parentInfo: CollapseItemData;
  expandIds: string[];
  onAddNewItem?: (data: CollapseItemData) => void;
  onExpand: (id: string) => void;
}
