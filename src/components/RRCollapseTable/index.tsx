import React, { useCallback } from 'react';

import { Collapse, List } from 'antd';
import tw from 'tailwind-styled-components';

import EmptyPanel from 'components/EmptyPanel';

import HeadCollapse from './HeadCollapse';
import LevelItem from './LevelItem';
import './styles.css';
import type {
  BaseLevelCollapseProps,
  CollapseItemData,
  RRCollapseTableProps,
  SecondLevelCollapseProps,
} from './types';

export { default as SearchCollapseItem } from './SearchCollapseItem';
export type { CollapseItemData, RRCollapseTableProps } from './types';

const { Panel } = Collapse;
const ThirdLevelItems: React.FC<BaseLevelCollapseProps> = ({
  showAddMore,
  isSelectedMultiple,
  items,
  onSelect,
  onLoadMore,
  getActiveLevelItem,
}) => (
  <List
    itemLayout='horizontal'
    dataSource={items}
    renderItem={(item, index) => (
      <LevelItem
        isSelectedMultiple={isSelectedMultiple}
        data={item}
        onSelect={onSelect}
        isActive={getActiveLevelItem(item)}
        showAddMore={showAddMore && index === items.length - 1}
        onLoadMore={onLoadMore}
      />
    )}
    locale={{
      emptyText: <EmptyPanel />,
    }}
  />
);

const SecondLevelCollapse: React.FC<SecondLevelCollapseProps> = ({
  parentInfo,
  showAddMore,
  isSelectedMultiple,
  items,
  onSelect,
  onLoadMore,
  expandIds,
  onAddNewItem,
  onExpand,
  getActiveLevelItem,
}) => (
  <CustomCollapse
    className='custom-collapse rounded-none'
    activeKey={expandIds}
    bordered={false}
  >
    {items.length === 0 ? (
      <EmptyPanel />
    ) : (
      items.map(item => (
        <CustomPanel
          header={
            <HeadCollapse
              showAddMore={showAddMore}
              isSelectedMultiple={isSelectedMultiple}
              isActive={getActiveLevelItem(item)}
              data={item}
              expandIds={expandIds}
              onSelect={onSelect}
              onExpand={onExpand}
              onAddNewItem={onAddNewItem}
              onLoadMore={() => onLoadMore?.(parentInfo)}
              className='hover:bg-grey-50'
            />
          }
          showArrow={false}
          key={item.id}
        >
          <ThirdLevelItems
            isSelectedMultiple={isSelectedMultiple}
            items={item.children || []}
            onSelect={(item, path, level) =>
              onSelect(item, [item, ...path], level)
            }
            getActiveLevelItem={getActiveLevelItem}
            showAddMore={
              item?.pagination &&
              item?.pagination?.total_pages > item?.pagination?.page
            }
            onLoadMore={() => onLoadMore?.(item)}
          />
        </CustomPanel>
      ))
    )}
  </CustomCollapse>
);

const RRCollapseTable = ({
  expandIds = [],
  selectedItem,
  isSelectedMultiple,
  multipleSelectedIds = [],
  data,
  onLoadMore,
  onExpand,
  onSelectId,
  onAddNewItem,
  onSelectedMultipleIds,
}: RRCollapseTableProps) => {
  const handleSelect = (
    selectedItem: CollapseItemData,
    path: CollapseItemData[],
    level: number,
  ) => {
    if (isSelectedMultiple && selectedItem?.is_end_user) {
      const newValues = multipleSelectedIds.includes(
        selectedItem.id?.toString(),
      )
        ? multipleSelectedIds.filter(
            id => id?.toString() !== selectedItem.id?.toString(),
          )
        : [...multipleSelectedIds, selectedItem.id.toString()];
      onSelectedMultipleIds?.(newValues);
    } else {
      onSelectId?.(selectedItem, path, level, selectedItem?.is_distributor);
    }
  };

  const getActiveLevelItem = useCallback(
    (item: CollapseItemData) => {
      return (
        selectedItem?.id?.toString() === item.id?.toString() ||
        multipleSelectedIds.includes(item.id?.toString())
      );
    },
    [selectedItem, multipleSelectedIds],
  );

  return (
    <Collapse activeKey={expandIds} style={{ borderRadius: 12 }}>
      {data.map((item, index) => (
        <Panel
          header={
            <HeadCollapse
              isSelectedMultiple={isSelectedMultiple}
              data={item}
              expandIds={expandIds}
              isActive={getActiveLevelItem(item)}
              onExpand={onExpand}
              onSelect={handleSelect}
              onAddNewItem={onAddNewItem}
              className='bg-grey-50 hover:bg-grey-100'
            />
          }
          showArrow={false}
          key={item.id}
        >
          <SecondLevelCollapse
            parentInfo={item}
            showAddMore={
              item.pagination &&
              item?.pagination?.total_pages > item?.pagination?.page &&
              index === data.length - 1
            }
            isSelectedMultiple={isSelectedMultiple}
            items={item.children || []}
            expandIds={expandIds}
            onExpand={onExpand}
            onAddNewItem={onAddNewItem}
            getActiveLevelItem={getActiveLevelItem}
            onSelect={(item, path, level) =>
              handleSelect(item, [item, ...path], level)
            }
            onLoadMore={onLoadMore}
          />
        </Panel>
      ))}
    </Collapse>
  );
};

export default RRCollapseTable;

const CustomCollapse = tw(Collapse)`border-none bg-transparent`;
const CustomPanel = tw(Panel)` bg-transparent`;
