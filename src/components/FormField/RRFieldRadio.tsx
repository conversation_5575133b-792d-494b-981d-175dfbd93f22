import React from 'react';

import { Radio } from 'antd';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import RR<PERSON>ieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

interface FieldRadioProps {
  id: string;
  name?: string;
  control: Control<any>;
  label: string;
  errors?: any;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  options?: { value: boolean | string | number; label: string }[];
}

const RRFieldRadio: React.FC<FieldRadioProps> = ({
  id,
  name,
  label,
  control,
  errors = {},
  className,
  disabled,
  required,
  options,
}) => {
  const { t } = useTranslation();
  const { watch } = useFormContext();
  const currentValue = watch(id);
  const error = getErrorMessage(errors?.[`${id || name}`]);

  // Default Yes/No options if not provided
  const defaultOptions = [
    { value: true, label: t('switch.yes') },
    { value: false, label: t('switch.no') },
  ];

  const radioOptions = options || defaultOptions;

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={CONTAINER_STYLES.base}>
          <RRFieldLabel
            id={id}
            required={required}
            label={label}
            disabled={disabled}
          />
          <Radio.Group
            className={`flex items-center justify-center gap-1 p-1 ${getCombinedInputStyles(
              {
                hasError: !!error,
              },
            )} ${className}`}
            onBlur={onBlur}
            onChange={onChange}
            value={value}
            disabled={disabled}
          >
            {radioOptions.map(option => (
              <Radio.Button
                key={option.value?.toString()}
                value={option.value}
                className={`hover:bg-white flex size-full flex-auto cursor-pointer items-center justify-center rounded text-sm font-semibold hover:text-black-1000`}
                style={{
                  ...(option.value?.toString() === currentValue?.toString()
                    ? { background: '#fff', color: '#000' }
                    : {
                        background: '#F0F1F5',
                        color: '#86888F',
                      }),
                }}
                disabled={disabled}
              >
                {option.label}
              </Radio.Button>
            ))}
          </Radio.Group>
          {error && <span className={LABEL_STYLES.error}>{error}</span>}
        </div>
      )}
    />
  );
};

export default RRFieldRadio;
