import React from 'react';

import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller } from 'react-hook-form';

import RRFieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

interface FieldDatePickerProps {
  id: string;
  name?: string;
  control: Control<any>;
  label?: string;
  placeholder?: [string, string];
  prefixIcon?: string;
  errors?: string;
  className?: string;
  disabled?: boolean;
  defaultValue?: dayjs.Dayjs;
  required?: boolean;
  dateFormat?: string;
  onChange?: (date: unknown, dateString: string | string[]) => void;
}

const { RangePicker } = DatePicker;

const RRFieldDatePicker: React.FC<FieldDatePickerProps> = ({
  id,
  name,
  control,
  label,
  errors = {},
  className,
  disabled,
  required,
  dateFormat = 'DD/MM/YYYY',
  placeholder = ['DD/MM/YYYY', 'DD/MM/YYYY'],
}) => {
  const error = getErrorMessage(errors?.[`${id || name}`]);

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={CONTAINER_STYLES.base}>
          <RRFieldLabel
            id={id}
            required={required}
            label={label}
            disabled={disabled}
          />
          <RangePicker
            variant='borderless'
            className={`${getCombinedInputStyles({
              hasError: !!error,
            })} ${className || ''}`}
            placeholder={placeholder}
            format={dateFormat}
            disabled={disabled}
            onChange={onChange}
            onBlur={onBlur}
            value={value}
          />
          {error && <span className={LABEL_STYLES.error}>{error}</span>}
        </div>
      )}
    />
  );
};

export default RRFieldDatePicker;
