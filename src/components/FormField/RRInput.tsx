import React from 'react';

import { Input, InputProps } from 'antd';

import images from 'assets/images';
import { Icon } from 'components';

import RRFieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  ICON_STYLES,
  getCombinedInputStyles,
} from './styles';

interface Props extends InputProps {
  containerClassName?: string;
  className?: string;
  prefixIcon?: string;
  label?: string;
  disabled?: boolean;
  showClearButton?: boolean;
  onClear?: () => void;
}

const RRInput: React.FC<Props> = ({
  containerClassName = '',
  className = '',
  prefixIcon,
  label,
  disabled,
  showClearButton = true,
  onClear,
  value,
  ...props
}) => {
  const hasValue = value && String(value).length > 0;
  const showClear = showClearButton && hasValue && !disabled;

  const handleClear = () => {
    if (onClear) {
      onClear();
    }
  };

  return (
    <div className={`${CONTAINER_STYLES.base} ${containerClassName}`}>
      <RRFieldLabel id={props.id} label={label} disabled={disabled} />
      <Input
        className={`${getCombinedInputStyles({
          hasValue: !!hasValue,
        })} ${className}`}
        prefix={
          prefixIcon ? (
            <Icon
              src={prefixIcon}
              alt='prefix'
              className={ICON_STYLES.prefix}
            />
          ) : null
        }
        suffix={
          showClear ? (
            <button type='button' onClick={handleClear}>
              <Icon
                src={images.Icon.InputClose}
                className={ICON_STYLES.clearButton}
              />
            </button>
          ) : null
        }
        value={value}
        disabled={disabled}
        {...props}
      />
    </div>
  );
};

export default RRInput;
