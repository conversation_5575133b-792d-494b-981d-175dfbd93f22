import React, { useCallback, useState } from 'react';

import { Popover } from 'antd';
import { twMerge } from 'tailwind-merge';

import images from 'assets/images';
import { Icon } from 'components';
import { SecondaryButton } from 'components/Button';

import { ICON_STYLES } from './styles';

// TypeScript interfaces
interface SelectOption {
  value: string;
  label: string;
}

interface RRMultipleSelectProps {
  placeholder?: string;
  options: SelectOption[];
  value?: string[];
  onChange?: (values: string[]) => void;
  width?: number;
  disabled?: boolean;
  className?: string;
  maxDisplayItems?: number;
  popoverHeight?: number;
  placement?:
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight'
    | 'leftTop'
    | 'leftBottom'
    | 'rightTop'
    | 'rightBottom';
}

const RRMultipleSelect: React.FC<RRMultipleSelectProps> = ({
  placeholder = '',
  options = [],
  value = [],
  onChange,
  width = 200,
  disabled = false,
  className = '',
  maxDisplayItems = 2,
  popoverHeight = 200,
  placement = 'bottomLeft',
}) => {
  const [open, setOpen] = useState(false);
  const [tempSelected, setTempSelected] = useState<string[]>([]);

  const handleToggleOpen = useCallback(() => {
    if (open) {
      setOpen(false);
      if (tempSelected?.length > 0) {
        setTempSelected([]);
      }
    } else {
      setTempSelected([...value]);
      setOpen(true);
    }
  }, [value, open, tempSelected]);

  const handleOptionClick = useCallback((optionValue: string) => {
    setTempSelected(prev => {
      const isSelected = prev.includes(optionValue);
      if (isSelected) {
        return prev.filter(val => val !== optionValue);
      } else {
        return [...prev, optionValue];
      }
    });
  }, []);

  const handleApply = useCallback(() => {
    onChange?.(tempSelected);
    setOpen(false);
  }, [tempSelected, onChange]);

  // Popover content
  const content = (
    <div style={{ width: Math.max(width - 16, 200) }}>
      {/* Options list */}
      <div className='overflow-y-auto' style={{ maxHeight: popoverHeight }}>
        {options.map((option, index) => (
          <div
            key={option.value}
            className={`
              flex
              cursor-pointer items-center rounded-md p-2 font-medium
              transition-colors duration-150 hover:bg-grey-50
            `}
            onClick={() => handleOptionClick(option.value)}
          >
            <span className='text-gray-900 truncate-1-line flex-1 select-none text-sm'>
              {option.label}
            </span>
            {tempSelected.includes(option.value) && (
              <Icon src={images.Icon.CheckMark} className='size-5' />
            )}
          </div>
        ))}
      </div>

      {/* Apply button */}
      <div className='mt-2'>
        <SecondaryButton
          variant='outline'
          onClick={handleApply}
          className='h-8 w-full'
        >
          Áp dụng
        </SecondaryButton>
      </div>
    </div>
  );

  return (
    <Popover
      content={content}
      trigger='click'
      open={open}
      onOpenChange={handleToggleOpen}
      placement={placement}
      overlayClassName='rr-multiple-select-popover'
      destroyTooltipOnHide
      arrow={false}
    >
      <button
        type='button'
        className={twMerge(
          'navio-form-field flex items-center px-3 py-2',
          className,
        )}
        disabled={disabled}
      >
        <span
          className={`flex-1 truncate ${
            value.length > 0 ? 'text-gray-900' : 'text-gray-500'
          }`}
        >
          {placeholder}
        </span>

        {/* Dropdown Arrow */}
        <Icon
          src={images.Icon.CaretDownSm}
          alt='chevron'
          className={`ml-0 ${
            ICON_STYLES.suffix
          } size-6 transition-transform duration-200 ease-in-out ${
            open ? 'rotate-180' : 'rotate-0'
          }`}
        />
      </button>
    </Popover>
  );
};

export default RRMultipleSelect;
