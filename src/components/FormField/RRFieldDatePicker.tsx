import React from 'react';

import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller } from 'react-hook-form';

import { Icon } from 'components';

import RRFieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  ICON_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

interface FieldDatePickerProps {
  id: string;
  name?: string;
  label?: string;
  control: Control<any>;
  placeholder?: string;
  prefixIcon?: string;
  suffixIcon?: string;
  errors?: any;
  className?: string;
  disabled?: boolean;
  defaultValue?: dayjs.Dayjs;
  required?: boolean;
  dateFormat?: string;
  onChange?: (date: unknown, dateString: string | string[]) => void;
}

const RRFieldDatePicker: React.FC<FieldDatePickerProps> = ({
  id,
  name,
  control,
  label,
  placeholder,
  prefixIcon,
  suffixIcon,
  errors = {},
  className,
  disabled,
  defaultValue,
  required,
  dateFormat = 'DD/MM/YYYY',
}) => {
  const error = getErrorMessage(errors?.[`${id || name}`]);

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={CONTAINER_STYLES.base}>
          <RRFieldLabel
            id={id}
            required={required}
            label={label}
            disabled={disabled}
          />
          <DatePicker
            width={360}
            defaultValue={defaultValue}
            className={`${getCombinedInputStyles({
              hasError: !!error,
            })} ${className}`}
            placeholder={placeholder}
            format={dateFormat}
            disabled={disabled}
            prefix={
              prefixIcon ? (
                <Icon
                  src={prefixIcon}
                  alt='prefix'
                  className={ICON_STYLES.prefix}
                />
              ) : null
            }
            suffixIcon={
              suffixIcon ? (
                <Icon
                  src={suffixIcon}
                  alt='suffix'
                  className={ICON_STYLES.suffix}
                />
              ) : null
            }
            nextIcon={
              <svg
                width='20'
                height='20'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  fillRule='evenodd'
                  clipRule='evenodd'
                  d='M7.05806 4.55806C7.30214 4.31398 7.69786 4.31398 7.94194 4.55806L12.9419 9.55806C13.186 9.80214 13.186 10.1979 12.9419 10.4419L7.94194 15.4419C7.69786 15.686 7.30214 15.686 7.05806 15.4419C6.81398 15.1979 6.81398 14.8021 7.05806 14.5581L11.6161 10L7.05806 5.44194C6.81398 5.19786 6.81398 4.80214 7.05806 4.55806Z'
                  fill='#54565C'
                />
              </svg>
            }
            prevIcon={
              <svg
                width='20'
                height='20'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  fillRule='evenodd'
                  clipRule='evenodd'
                  d='M12.9419 4.55806C13.186 4.80214 13.186 5.19786 12.9419 5.44194L8.38388 10L12.9419 14.5581C13.186 14.8021 13.186 15.1979 12.9419 15.4419C12.6979 15.686 12.3021 15.686 12.0581 15.4419L7.05806 10.4419C6.81398 10.1979 6.81398 9.80214 7.05806 9.55806L12.0581 4.55806C12.3021 4.31398 12.6979 4.31398 12.9419 4.55806Z'
                  fill='#54565C'
                />
              </svg>
            }
            superNextIcon={null}
            superPrevIcon={null}
            onChange={onChange}
            onBlur={onBlur}
            value={value ? dayjs(value) : defaultValue}
          />
          {error && <span className={LABEL_STYLES.error}>{error}</span>}
        </div>
      )}
    />
  );
};

export default RRFieldDatePicker;
