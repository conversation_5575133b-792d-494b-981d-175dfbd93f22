import React, { useState } from 'react';

import { Input } from 'antd';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller } from 'react-hook-form';

import images from 'assets/images';
import { Icon } from 'components';

import RRFieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  ICON_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

interface FieldPasswordProps {
  id: string;
  name?: string;
  control: Control<any>;
  label: string;
  placeholder?: string;
  prefixIcon?: string;
  errors?: any;
  className?: string;
  disabled?: boolean;
  defaultValue?: string;
  required?: boolean;
  autoComplete?: string;
}

const RRFieldPassword: React.FC<FieldPasswordProps> = ({
  id,
  name,
  control,
  required,
  label,
  prefixIcon,
  errors = {},
  className,
  disabled,
  placeholder,
  defaultValue,
  autoComplete,
}) => {
  const error = getErrorMessage(errors?.[`${id || name}`]);

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={CONTAINER_STYLES.base}>
          <RRFieldLabel
            id={id}
            required={required}
            label={label}
            disabled={disabled}
          />
          <Input
            defaultValue={defaultValue}
            className={`password-field ${
              value ? 'has-value' : ''
            } ${getCombinedInputStyles({
              hasError: !!error,
            })} ${className}`}
            type={showPassword ? 'text' : 'password'}
            placeholder={placeholder}
            // placeholder='&#9679;&#9679;&#9679;&#9679;&#9679;&#9679;&#9679;&#9679;'
            disabled={disabled}
            autoComplete={autoComplete || 'new-password'}
            prefix={
              prefixIcon ? (
                <Icon
                  src={prefixIcon}
                  alt='prefix'
                  className={ICON_STYLES.prefix}
                />
              ) : null
            }
            suffix={
              <button type='button' onClick={togglePasswordVisibility}>
                <Icon
                  src={
                    showPassword ? images.Icon.EyeHidden : images.Icon.EyeShow
                  }
                  className={ICON_STYLES.clearButton}
                />
              </button>
            }
            onChange={onChange}
            onBlur={onBlur}
            value={value}
          />
          {error && <span className={LABEL_STYLES.error}>{error}</span>}
        </div>
      )}
    />
  );
};

export default RRFieldPassword;
