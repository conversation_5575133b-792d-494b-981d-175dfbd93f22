import React, { useMemo, useState } from 'react';

import { Select } from 'antd';
import { twMerge } from 'tailwind-merge';

import images from 'assets/images';
import { Icon } from 'components';

import RRFieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  ICON_STYLES,
  getCombinedInputStyles,
} from './styles';

const { Option } = Select;

interface FieldSelectProps {
  id: string;
  name?: string;
  label?: string;
  placeholder?: string;
  options: { value: string; label: string }[];
  className?: string;
  containerClassName?: string;
  disabled?: boolean;
  required?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
}

const RRFieldSelect: React.FC<FieldSelectProps> = ({
  id,
  name,
  required,
  label,
  placeholder,
  options,
  className,
  containerClassName,
  disabled,
  value,
  onChange,
  onBlur,
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const handleDropdownVisibleChange = (open: boolean) => {
    setIsOpen(open);
  };

  const currentOption = useMemo(() => {
    return options.find(
      option => option.value?.toString() === value?.toString(),
    );
  }, [options, value]);

  return (
    <div className={twMerge(CONTAINER_STYLES.base, containerClassName)}>
      <RRFieldLabel
        id={id}
        required={required}
        label={label}
        disabled={disabled}
      />
      <Select
        className={`cursor-pointer ${getCombinedInputStyles()} ${
          className || ''
        }`}
        placeholder={placeholder}
        disabled={disabled}
        variant='borderless'
        onDropdownVisibleChange={handleDropdownVisibleChange}
        suffixIcon={
          <Icon
            src={images.Icon.CaretDownSm}
            alt='chevron'
            className={`${
              ICON_STYLES.suffix
            } size-6 transition-transform duration-200 ease-in-out ${
              isOpen ? 'rotate-180' : 'rotate-0'
            }`}
          />
        }
        value={currentOption?.value}
        onChange={onChange}
        onBlur={onBlur}
        removeIcon={null}
        {...rest}
      >
        {options.map(option => (
          <Option key={option.value} value={option.value}>
            {option.label}
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default RRFieldSelect;
