import React, { useState } from 'react';

import { Select } from 'antd';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller } from 'react-hook-form';

import images from 'assets/images';
import { Icon } from 'components';

import RRFieldLabel from './RRFieldLabel';
import {
  CONTAINER_STYLES,
  ICON_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

const { Option } = Select;

interface FieldSelectProps {
  id: string;
  name?: string;
  control: Control<any>;
  label: string;
  placeholder?: string;
  options: { value: number | string; label: string }[];
  errors?: any;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

const RRFieldSelect: React.FC<FieldSelectProps> = ({
  id,
  name,
  required,
  label,
  placeholder,
  options,
  control,
  errors = {},
  className,
  disabled,
}) => {
  const error = getErrorMessage(errors?.[`${id || name}`]);

  const [isOpen, setIsOpen] = useState(false);
  const handleDropdownVisibleChange = (open: boolean) => {
    setIsOpen(open);
  };

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => {
        const currentOption = options.find(
          option => option.value?.toString() === value?.toString(),
        );
        return (
          <div className={CONTAINER_STYLES.base}>
            <RRFieldLabel
              id={id}
              required={required}
              label={label}
              disabled={disabled}
            />
            <Select
              className={`${getCombinedInputStyles({
                hasError: !!error,
              })} ${className || ''}`}
              placeholder={placeholder}
              disabled={disabled}
              variant='borderless'
              onDropdownVisibleChange={handleDropdownVisibleChange}
              suffixIcon={
                <Icon
                  src={images.Icon.CaretDownSm}
                  alt='chevron'
                  className={`${
                    ICON_STYLES.suffix
                  } size-6 transition-transform duration-200 ease-in-out ${
                    isOpen ? 'rotate-180' : 'rotate-0'
                  }`}
                />
              }
              onChange={onChange}
              onBlur={onBlur}
              value={currentOption}
            >
              {options.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            {error && <span className={LABEL_STYLES.error}>{error}</span>}
          </div>
        );
      }}
    />
  );
};

export default RRFieldSelect;
