import { LABEL_STYLES } from './styles';

interface Props {
  id?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
}

const RRFieldLabel = (props: Props) => {
  const { id, label, required, disabled } = props;

  if (!label) return null;

  return (
    <label
      htmlFor={id}
      className={`${LABEL_STYLES.base} ${
        disabled ? LABEL_STYLES.disabled : ''
      }`}
    >
      {label} {required && <span className={LABEL_STYLES.required}>*</span>}
    </label>
  );
};

export default RRFieldLabel;
