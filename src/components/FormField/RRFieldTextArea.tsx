import React from 'react';

import { Input } from 'antd';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller } from 'react-hook-form';

import RRFieldLabel from './RRFieldLabel';
import './style.css';
import {
  CONTAINER_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

interface FieldInputProps {
  id: string;
  name?: string;
  label: string;
  control: Control<any>;
  placeholder?: string;
  errors?: any;
  className?: string;
  disabled?: boolean;
  defaultValue?: string;
  required?: boolean;
  rows?: number;
  autoSize?: boolean;
}

const RRFieldTextArea: React.FC<FieldInputProps> = ({
  id,
  name,
  required,
  label,
  placeholder,
  control,
  errors = {},
  className,
  disabled,
  defaultValue,
  rows = 4,
  autoSize = true,
}) => {
  const error = getErrorMessage(errors?.[`${id || name}`]);

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={CONTAINER_STYLES.base}>
          <RRFieldLabel
            id={id}
            required={required}
            label={label}
            disabled={disabled}
          />
          <Input.TextArea
            defaultValue={defaultValue}
            className={`${getCombinedInputStyles({
              hasError: !!error,
            })} ${className} textarea`}
            placeholder={placeholder}
            disabled={disabled}
            onChange={onChange}
            onBlur={onBlur}
            value={value}
            rows={rows}
            autoSize={autoSize}
          />
          {error && (
            <div className={CONTAINER_STYLES.error}>
              <span className={LABEL_STYLES.error}>{error}</span>
            </div>
          )}
        </div>
      )}
    />
  );
};

export default RRFieldTextArea;
