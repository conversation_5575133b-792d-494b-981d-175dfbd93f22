import { twMerge } from 'tailwind-merge';

export const CONTAINER_STYLES = {
  base: 'flex flex-col gap-1 relative mb-0 w-full',
  error: 'relative',
};

export const LABEL_STYLES = {
  base: 'block text-sm font-semibold text-black-1000 mb-1',
  required: 'text-red-200',
  error: 'text-red-200 text-xs',
  disabled: '',
};

export const ICON_STYLES = {
  prefix: 'size-5 text-grey-500',
  suffix:
    'ml-1 mr-0 text-grey-400 hover:text-grey-600 cursor-pointer transition-all duration-200 ease-in-out',
  clearButton:
    'text-grey-400 hover:text-grey-600 cursor-pointer transition-colors',
  dropdownIcon:
    'text-grey-400 hover:text-grey-600 transition-all duration-200 ease-in-out transform',
};

/**
 * Common Input Field Styles
 */
export const INPUT_STYLES = {
  typing: `
    border-grey-400
  `,

  // Filled state
  filled: `
    text-black-1000
  `,

  error: `
    !text-black-1000
    !border-red-200
  `,
};

// Combine
export const getCombinedInputStyles = ({
  hasError = false,
  isTyping = false,
  hasValue = false,
} = {}) => {
  // arrange follow priority
  const strClassName = [
    'navio-form-field',
    hasValue ? INPUT_STYLES.filled : '',
    isTyping ? INPUT_STYLES.typing : '',
    hasError ? INPUT_STYLES.error : '',
  ]
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
  return twMerge(strClassName);
};
