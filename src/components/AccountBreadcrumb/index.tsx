import { Breadcrumb } from 'antd';

import images from 'assets/images';
import { Icon } from 'components';

import './style.css';

export interface BreadcrumbItem {
  label: string;
  level: number;
}

interface Props {
  breadcrumb: BreadcrumbItem[];
}

const AccountBreadCrumb = (props: Props) => {
  const { breadcrumb = [] } = props || {};
  return (
    <Breadcrumb
      separator={<Icon src={images.Icon.ChevronRightGrey} className='size-4' />}
    >
      {breadcrumb.map((item, index) => (
        <Breadcrumb.Item key={index} className='flex items-center gap-2'>
          <Icon
            src={
              [
                images.Icon.Level0Star,
                images.Icon.Level1Star,
                images.Icon.Level2Star,
              ][item.level]
            }
          />
          <div className='truncate-1-line max-w-[280px] font-medium'>
            {item.label}
          </div>
        </Breadcrumb.Item>
      ))}
    </Breadcrumb>
  );
};

export default AccountBreadCrumb;
