import React from 'react';

import { UserPermission, UserProfile, UserRole } from 'types/UserRoleTypes';

const WithRoleAccess = <P = unknown,>(
  WrappedComponent: React.ComponentType<P>,
  allowRoles: UserRole[],
  _?: UserPermission[],
) => {
  return function CheckRole(props: P & { userProfile: UserProfile }) {
    const { userProfile } = props;
    return userProfile && allowRoles.includes(userProfile.role) ? (
      <WrappedComponent {...props} />
    ) : null;
  };
};

export default WithRoleAccess;
