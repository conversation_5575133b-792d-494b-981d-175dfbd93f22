import { useCallback, useEffect, useState } from 'react';

import { DeviceStatus } from 'constants/device';
import { MFMap } from 'react-map4d-map';
import tw from 'tailwind-styled-components';

import { Colors } from 'assets/styles';
import { RRLocationPin } from 'components';
import RRCircle from 'components/RRMap/RRCircle';
import RRMaker from 'components/RRMap/RRMaker';

import './styles.css';

export enum PinType {
  Car = 'car',
  Location = 'location',
}

interface MarkerItem {
  position: { lat: number; lng: number };
  label: string;
  deviceHalfOfCourse?: number;
  deviceStatus?: DeviceStatus;
}

interface RRStaticMapProps {
  className?: string;
  center: { lat: number; lng: number };
  radius?: number;
  zoom?: number;
  pinType?: PinType;
  statisticComponent?: React.ReactNode;
  markerList?: MarkerItem[];
}

const RRStaticMap: React.FC<RRStaticMapProps> = ({
  className,
  center,
  radius,
  pinType,
  zoom = 15,
  statisticComponent,
  markerList = [],
}) => {
  const [mapInstance, setMapInstance] = useState<any>(null);
  const [centerState, setCenterState] = useState({
    lat: Number(process.env.DEFAULT_LAT),
    lng: Number(process.env.DEFAULT_LNG),
  });

  const renderPin = useCallback(
    (item: MarkerItem) => {
      if (pinType === PinType.Car) {
        return (
          <RRMaker
            label={item?.label || ''}
            labelAnchor={[0.5, 2]}
            rotation={item?.deviceHalfOfCourse || 0}
            position={item?.position}
            status={item?.deviceStatus || DeviceStatus.Running}
          />
        );
      }
      if (pinType === PinType.Location) {
        return (
          <RRLocationPin label={item?.label || ''} position={item?.position} />
        );
      }
      return null;
    },
    [pinType],
  );

  useEffect(() => {
    if (center) {
      setCenterState({
        lat: center.lat || Number(process.env.DEFAULT_LAT),
        lng: center.lng || Number(process.env.DEFAULT_LNG),
      });
    }
  }, [center]);

  return (
    <MapContainer className={className}>
      <Map
        options={{
          zoom,
        }}
        center={centerState}
        version={'2.3'}
        accessKey={process.env.ACCESS_KEY ?? ''}
        onMapReady={map => {
          setMapInstance(map);
        }}
      >
        {markerList?.length > 0 && markerList.map(item => renderPin(item))}
        {radius && center && (
          <RRCircle
            radius={radius}
            fillColor={Colors.grey[300]}
            fillOpacity={0.4}
            strokeColor={Colors.grey[300]}
            strokeWidth={1}
            center={center}
          />
        )}
      </Map>
      <ExtraElement>{statisticComponent}</ExtraElement>
    </MapContainer>
  );
};

export default RRStaticMap;

const Map = tw(MFMap)`rounded-lg`;
const MapContainer = tw.div`relative w-full h-full`;
const ExtraElement = tw.div`
  w-full absolute bottom-0 left-1/2 transform -translate-x-1/2  bg-white p-2 rounded shadow
`;
