import React, { useCallback, useEffect } from 'react';

import { List, Modal } from 'antd';
import { useProfile, useRole } from 'hooks';
import tw from 'tailwind-styled-components';

import { addUsersToChildren } from 'features/AccountBusiness/adapters';
import {
  useAccountData,
  useAccountMutations,
  useAccountState,
  useSearchAccounts,
} from 'features/AccountBusiness/hooks';
import { findAccountById } from 'features/AccountBusiness/utils';
import { t } from 'i18next';
import { filterAccountByRole } from 'utils';

import images from 'assets/images';
import { Icon, RRCollapseTable, RRInput } from 'components';
import { SearchCollapseItem } from 'components/RRCollapseTable';

export interface Props {
  open: boolean;
  expandUserRole: string[];
  className?: string;
  selectedId: string;
  onSelectItem: (item: any) => void;
  onClose: () => void;
}

const AccountDestinationModal: React.FC<Props> = props => {
  const { open, expandUserRole, className, selectedId, onSelectItem, onClose } =
    props || {};
  const { profile } = useProfile();
  const { roles } = useRole();
  const [searchValue, setSearchValue] = React.useState('');
  const { state, updateState, changeExpandAccountIds } = useAccountState();

  const searchAccounts = useSearchAccounts(searchValue, profile);
  const mutations = useAccountMutations(profile);

  const accountSearch = searchAccounts.data || [];

  const {
    accounts: { data: profileData, status: profileStatus } = {
      data: [],
      profileStatus,
    },
  } = useAccountData(profile);

  const filterAccountSearch = filterAccountByRole({
    accounts: accountSearch,
    roles,
    roleType: expandUserRole,
  });

  const filterUser = filterAccountByRole({
    accounts: state.accountData,
    roles,
    roleType: expandUserRole,
  });

  const handleExpandAccount = useCallback(
    (id: string) => {
      const targetAccount = findAccountById(state.accountData, id?.toString());

      if (!targetAccount) {
        return;
      }

      if (targetAccount?.is_end_user) {
        return;
      }

      // only expand distributor
      if (
        expandUserRole?.includes('distributor') &&
        expandUserRole?.length === 1
      ) {
        if (targetAccount?.role_type === 'distributor') {
          return;
        }
      }

      // collapse
      if (state.expandAccountIds.includes(id)) {
        changeExpandAccountIds(state.expandAccountIds.filter(i => i !== id));
        return;
      }
      // expand
      changeExpandAccountIds([...state.expandAccountIds, id]);

      if (
        targetAccount?.children_count &&
        targetAccount?.children_count > 0 &&
        targetAccount?.children?.length === 0
      ) {
        mutations.expandAccount.mutate(
          { id },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: id,
                newUsers: res.users || [],
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
                roles,
              });

              //

              updateState({
                accountData: newAccountData,
              });
            },
            onError: () => {
              changeExpandAccountIds(
                state.expandAccountIds.filter(i => i !== id),
              );
            },
          },
        );
      }
    },
    [
      expandUserRole,
      findAccountById,
      mutations.expandAccount,
      state.expandAccountIds,
      state.accountData,
      roles,
      changeExpandAccountIds,
      updateState,
    ],
  );

  const handleLoadMore = useCallback(
    (data: any) => {
      const {
        id,
        pagination: { total_pages, page } = {
          total_pages: 1,
          page: 1,
        },
      } = data || {};
      if (total_pages && page && total_pages > page) {
        mutations.expandAccount.mutate(
          {
            id,
            page: page + 1,
          },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: data.id,
                newUsers: res.users || [],
                roles,
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
              });

              updateState({
                accountData: newAccountData,
              });
            },
          },
        );
      }
    },
    [mutations.expandAccount, state.accountData, roles, updateState],
  );

  const onSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchValue(value);
  };

  useEffect(() => {
    // when update first level tree, reload tree.
    if (profileData && profileData.length > 0 && profileStatus === 'success') {
      updateState({
        accountData: [...profileData],
        expandAccountIds: [profileData?.[0]?.id],
      });
    }
  }, [profileStatus, profileData]);

  return (
    <Modal
      centered
      width={480}
      title={<Title>{t('business.destinationAccount')}</Title>}
      open={open}
      onCancel={onClose}
      onOk={onClose}
      footer={null}
      closeIcon={
        <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
      }
    >
      <Body>
        <Container className={className}>
          <RRInput
            type='text'
            value={searchValue}
            onChange={onSearch}
            prefixIcon={images.Icon.SearchLoupe}
            className='mb-3'
            placeholder={t('map.filterPlaceholder')}
            showClearButton={true}
            onClear={() => setSearchValue('')}
          />
          <div className='h-full max-h-[80vh] w-[calc(100%+16px)] overflow-auto'>
            <div className='h-full w-[calc(100%-20px)]'>
              {searchValue ? (
                <div className='rounded-md border border-[#d9d9d9]'>
                  <List
                    itemLayout='horizontal'
                    dataSource={filterAccountSearch}
                    locale={{ emptyText: 'Không tìm thấy' }}
                    renderItem={item => (
                      <SearchCollapseItem
                        key={item.id}
                        isActive={selectedId === item.id?.toString()}
                        item={item}
                        onSelectItem={onSelectItem}
                      />
                    )}
                  />
                </div>
              ) : (
                <RRCollapseTable
                  expandIds={state.expandAccountIds}
                  multipleSelectedIds={[selectedId]}
                  onSelectId={onSelectItem}
                  data={filterUser}
                  onExpand={handleExpandAccount}
                  onLoadMore={handleLoadMore}
                />
              )}
            </div>
          </div>
        </Container>
      </Body>
    </Modal>
  );
};

export default AccountDestinationModal;

const Container = tw.div`overflow-auto bg-white-1000 rounded-md relative overflow-x-hidden`;
const Title = tw.span`text-xl leading-[28px]`;
const Body = tw.div`flex flex-col gap-3 py-1`;
