import React, { useCallback, useRef } from 'react';

import VirtualList from 'rc-virtual-list';
import { twMerge } from 'tailwind-merge';

import type { DeviceType } from 'types/device';

import RRScrollView from 'components/RRScrollView/RRScrollView';
import Spinner from 'components/Spinner';

import DeviceCard from './DeviceCard';

interface Props {
  isMap?: boolean;
  loading?: boolean;
  data: DeviceType[];
  multipleImeiSelected?: string[];
  selectedDevice?: DeviceType;
  onSelectedDevice?: (device: DeviceType) => void;
  fetchMore?: () => void;
}

const DeviceList = (props: Props) => {
  const {
    isMap,
    loading,
    data,
    multipleImeiSelected = [],
    selectedDevice,
    onSelectedDevice,
    fetchMore,
  } = props || {};
  const scrollRef = useRef(null);

  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (scrollRef.current) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const height = (scrollRef.current as any).getClientHeight();
      if (
        Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop) -
          height <=
        10
      ) {
        fetchMore?.();
      }
    }
  };

  const handleDeviceSelectClick = useCallback(
    (device: DeviceType) => {
      onSelectedDevice?.(device);
    },
    [onSelectedDevice],
  );

  return (
    <div className='size-full'>
      <RRScrollView
        onScroll={onScroll}
        ref={scrollRef}
        className={isMap ? 'w-[calc(100%+11px)]' : 'w-[calc(100%+16px)]'}
      >
        <div className={isMap ? 'w-[calc(100%-11px)]' : 'w-[calc(100%-16px)]'}>
          <VirtualList
            data={data}
            itemHeight={47}
            itemKey='email'
            onScroll={onScroll}
            style={{ width: '100%', minHeight: '200px' }}
          >
            {(item, index) => {
              const checked =
                selectedDevice?.imei === item.imei ||
                multipleImeiSelected.includes(item.imei);
              return (
                <DeviceCard
                  className={twMerge(
                    checked
                      ? 'box-border rounded-2xl border border-solid border-blue-200'
                      : '',
                    'mb-2 hover:bg-grey-100',
                  )}
                  key={index}
                  device={item}
                  onPress={selectedItem =>
                    handleDeviceSelectClick(selectedItem)
                  }
                />
              );
            }}
          </VirtualList>
          {loading && (
            <div className='flex justify-center p-4'>
              <Spinner />
            </div>
          )}
        </div>
      </RRScrollView>
    </div>
  );
};

export default DeviceList;
