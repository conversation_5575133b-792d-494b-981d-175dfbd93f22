import { useCallback, useMemo } from 'react';

import {
  DeviceOptions,
  DeviceStatus,
  DeviceStatusLabel,
  StatusColorsMapping,
} from 'constants/device';
import dayjs from 'dayjs';
import tw from 'tailwind-styled-components';

import useDeviceAddress from 'features/DeviceBusiness/hooks/useDeviceAddress';
import { getDeviceLabelStatus } from 'features/DeviceBusiness/utils';
import { DeviceType } from 'types/device';

import images from 'assets/images';
import { BodySm, FontBold } from 'assets/styles';
import { Icon } from 'components';

interface DeviceCardProps {
  className?: string;
  onPress?: (item: DeviceType) => void;
  device: DeviceType;
}

const DeviceCategory = DeviceOptions.reduce(
  (acc, cur) => {
    acc[cur.value] = cur.label;
    return acc;
  },
  {} as Record<string, string>,
);

const DeviceCard = ({ className, device, onPress }: DeviceCardProps) => {
  const deviceAddress = useDeviceAddress(device);
  const statusStyle = StatusColorsMapping[device.status];

  const expiredDateColor = useMemo(() => {
    if (!device.expired_at) {
      return undefined;
    }
    const expiredDateJs = dayjs(device.expired_at);
    const diffDays = expiredDateJs.diff(dayjs(), 'day');
    if (diffDays < 0) {
      return 'text-red-200';
    } else if (diffDays > 60) {
      return 'text-green-200';
    }
    return 'text-yellow-300';
  }, [device]);

  const formatInactiveDuration = useCallback((ms?: number | null) => {
    if (!ms || ms <= 0) return '';
    const days = Math.floor(ms / (24 * 60 * 60 * 1000));
    const hours = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    let result = '';
    if (days > 0) result += `${days} ngày`;
    if (hours > 0) result += `${result ? ' ' : ''}${hours} giờ`;
    return result || '0 giờ';
  }, []);

  return (
    <Card onClick={() => onPress?.(device)} className={className}>
      <div className='w-full'>
        <StatusSection>
          <Speed
            bg={statusStyle?.backgroundColor}
            color={statusStyle?.color}
            className={`${BodySm}`}
          >
            {getDeviceLabelStatus(device)}
          </Speed>
          {String(device?.default) === '1' ? (
            <Date className={`${BodySm} text-black-1000`}>
              (Chưa kích hoạt)
            </Date>
          ) : (
            <Date className={`${BodySm} ${expiredDateColor}`}>
              {device.expired_at
                ? `(${dayjs(device.expired_at).format('DD/MM/YYYY')})`
                : '(-)'}
            </Date>
          )}
        </StatusSection>
        <Row>
          <Icon
            width='16px'
            height='16px'
            src={images.Icon.DeviceSignalSquare}
          />
          <div>
            <Title className={`${BodySm} ${FontBold} uppercase`}>{`${
              device.device_plate_number || ''
            } - ${device?.device_name || ''}`}</Title>
            <Info className={`${BodySm}`}>{`${
              DeviceCategory?.[device.device_category] || ''
            } - ${device.imei || ''}`}</Info>
          </div>
        </Row>
        <Row>
          <Icon width='16px' height='16px' src={images.Icon.PinLocation} />
          <Address className={`${BodySm}`}>{String(deviceAddress)}</Address>
        </Row>
      </div>
    </Card>
  );
};

export default DeviceCard;

const Card = tw.div`
  flex items-center justify-between rounded-2xl border border-grey-100 p-3 cursor-pointer
  w-full box-border
`;
const StatusSection = tw.div`flex justify-between`;

const Speed = tw.div<{ bg: string; color: string }>`
  rounded-md px-1.5 py-1
  ${props => props.bg && `${props.bg}`}
  ${props => props.color && `${props.color}`}
`;

const Date = tw.div`text-sm`;
const Row = tw.div`flex items-center gap-2 mt-2`;
const Info = tw.div`text-sm text-gray-600`;
const Title = tw.div``;
const Address = tw.div`text-sm text-gray-600 truncate`;
