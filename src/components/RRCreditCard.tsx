import React from 'react';

import { Image } from 'antd';
import tw from 'tailwind-styled-components';

import images from 'assets/images';

interface RRCreditCardProps {
  cardNumber: string;
  cardHolderName: string;
  cardId: string;
}

const RRCreditCard: React.FC<RRCreditCardProps> = ({
  cardNumber,
  cardHolderName,
  cardId,
}) => {
  return (
    <CardContainer>
      <CardTitle>Thẻ Nhận Dạng Lái Xe</CardTitle>
      <CardNumber>{cardNumber}</CardNumber>
      <CardType>RFID</CardType>
      <WaveImage>
        <Image preview={false} src={images.Profile.WaveIllustration} />
      </WaveImage>
      <CardHolderName>{cardHolderName}</CardHolderName>
      <CardId>{cardId}</CardId>
      <LogoImage>
        <Image preview={false} src={images.Profile.VerticalLogo} />
      </LogoImage>
    </CardContainer>
  );
};

export default RRCreditCard;

const CardContainer = tw.div`relative h-[186px] w-[320px] rounded-lg bg-black-1000 shadow-lg`;
const CardTitle = tw.div`absolute left-5 top-3 z-20 text-[11.16px] font-semibold uppercase text-[#E9E9E9] font-Inter`;
const CardNumber = tw.div`absolute left-5 top-7 z-20 text-[11.16px] font-normal tracking-widest text-[#868686] font-Inter`;
const CardType = tw.div`absolute right-2 top-3 z-20 text-sm font-bold tracking-[.44em] text-brand-300`;
const WaveImage = tw.div`absolute left-0 top-0 size-[200px]`;
const CardHolderName = tw.div`absolute bottom-8 left-5 text-[13.02px] font-bold text-[#E9E9E9] font-Inter`;
const CardId = tw.div`absolute bottom-3 left-5 text-[13.02px] font-normal leading-[19.53px] tracking-wide text-[#868686]`;
const LogoImage = tw.div`absolute bottom-2 right-2`;
