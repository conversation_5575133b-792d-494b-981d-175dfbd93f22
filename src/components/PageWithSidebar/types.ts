export interface MenuItem {
  key?: string;
  label: string;
  route?: string;
  icon?: () => JSX.Element;
  exact?: boolean;
  permissions?: string[];
}

export interface MultipleSidebarMenuItems {
  key?: string;
  icon?: () => JSX.Element;
  menu?: DetailedMenuItem[];
  route?: string;
  label: string;
  permissions?: string[];
  subItems?: DetailedMenuItem[];
}

export interface DetailedMenuItem extends MenuItem {
  component?: React.FunctionComponent;
  rightElement?: React.ReactElement;
  subItems?: DetailedMenuItem[];
}
