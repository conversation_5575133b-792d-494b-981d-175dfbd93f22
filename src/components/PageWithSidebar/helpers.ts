import { safeIncludes, safeStringArrayOperation } from './typeGuards';
import { DetailedMenuItem, MultipleSidebarMenuItems } from './types';

export const generateSubItemKey = (
  parentKey: string,
  subItem: DetailedMenuItem,
  index: number,
): string => {
  const subItemKey = subItem.key || subItem.route || `sub-${index}`;
  return `${parentKey}.${subItemKey}`;
};

export const hasActiveSubItems = (
  item: MultipleSidebarMenuItems,
  selectedKeys: string[],
): boolean => {
  if (!item.subItems?.length) return false;

  const safeSelectedKeys = safeStringArrayOperation(selectedKeys);
  if (safeSelectedKeys.length === 0) return false;

  return item.subItems.some((subItem, index) => {
    const subKey = generateSubItemKey(item.key!, subItem, index);
    return safeIncludes(safeSelectedKeys, subKey);
  });
};

export const isSubItemActive = (
  parentKey: string,
  subItem: DetailedMenuItem,
  index: number,
  selectedKeys: string[],
): boolean => {
  const safeSelectedKeys = safeStringArrayOperation(selectedKeys);
  if (safeSelectedKeys.length === 0) return false;

  const subKey = generateSubItemKey(parentKey, subItem, index);
  return safeIncludes(safeSelectedKeys, subKey);
};

export const getSelectedKeysForSubItem = (
  parentKey: string,
  subItem: DetailedMenuItem,
  index: number,
): string[] => {
  const subKey = generateSubItemKey(parentKey, subItem, index);
  return [parentKey, subKey];
};

export const isRouteMatch = (route: string, currentPath: string): boolean => {
  return route === currentPath;
};

export const extractAllMenuItems = (
  menu: MultipleSidebarMenuItems[],
): DetailedMenuItem[] => {
  const items: DetailedMenuItem[] = [];

  menu.forEach(menuGroup => {
    if (menuGroup.menu) {
      items.push(...menuGroup.menu);
    }
  });

  return items;
};
