import { useCallback, useEffect, useState } from 'react';

import { generateSubItemKey, isRouteMatch } from './helpers';
import { safeIndexOf, safeStringArrayOperation } from './typeGuards';
import { MultipleSidebarMenuItems } from './types';
import { generateActiveMenus } from './utils';

export const useSelectedKeys = (
  menu: MultipleSidebarMenuItems[],
  currentMenu: number,
  updateCurrentMenu: (index: number) => void,
  openKeys: string[],
  setOpenKeys: (keys: string[] | ((prev: string[]) => string[])) => void,
) => {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const updateSelectedKeys = useCallback((keys: string[]) => {
    setSelectedKeys(keys);
  }, []);

  const doUpdateOpenKeys = useCallback(() => {
    const safeSelectedKeys = safeStringArrayOperation(selectedKeys);
    const safeOpenKeys = safeStringArrayOperation(openKeys);

    if (safeSelectedKeys.length === 0) return;

    safeSelectedKeys.forEach(item => {
      const sel = item.split('.');
      sel.pop();
      const newKey = sel.join('.');
      if (newKey && safeIndexOf(safeOpenKeys, newKey) === -1) {
        setOpenKeys(prev => [...safeStringArrayOperation(prev), newKey]);
      }
    });
  }, [selectedKeys, openKeys, setOpenKeys]);

  const doUpdateSelectedKeys = useCallback(() => {
    let selectedMenu = currentMenu;
    const selectedMenuItem = Object.keys(menu).reduce((items, value) => {
      const currMenu = menu[Number(value)].menu;
      if (!currMenu) return items;

      const selected = generateActiveMenus(currMenu, window.location.pathname);
      if (selected.length > 0) {
        selectedMenu = Number(value);
        selected.forEach(item => items.push(item as never));
      }

      currMenu.forEach(menuItem => {
        if (menuItem.subItems) {
          menuItem.subItems.forEach((subItem, index) => {
            if (
              subItem.route &&
              isRouteMatch(subItem.route, window.location.pathname)
            ) {
              selectedMenu = Number(value);

              if (menuItem.key && !items.includes(menuItem.key as never)) {
                items.push(menuItem.key as never);
              }

              const subKey = generateSubItemKey(menuItem.key!, subItem, index);
              if (!items.includes(subKey as never)) {
                items.push(subKey as never);
              }

              if (
                menuItem.key &&
                Array.isArray(openKeys) &&
                !openKeys.includes(menuItem.key)
              ) {
                setOpenKeys(prev => [
                  ...safeStringArrayOperation(prev),
                  menuItem.key!,
                ]);
              }
            }
          });
        }
      });

      return items;
    }, []);

    updateCurrentMenu(selectedMenu);
    setSelectedKeys(selectedMenuItem);
  }, [
    currentMenu,
    window.location.pathname,
    menu,
    updateCurrentMenu,
    openKeys,
    setOpenKeys,
  ]);

  useEffect(() => {
    doUpdateSelectedKeys();
  }, [doUpdateSelectedKeys]);

  useEffect(() => {
    doUpdateOpenKeys();
  }, [doUpdateOpenKeys]);

  return {
    selectedKeys,
    updateSelectedKeys,
    doUpdateSelectedKeys,
    doUpdateOpenKeys,
  };
};

export const useOpenKeys = (initialKeys: string[] = []) => {
  const [openKeys, setOpenKeys] = useState<string[]>(initialKeys);

  return {
    openKeys,
    setOpenKeys,
  };
};

export const useScreenSizeDetection = () => {
  const [isScreenSizeInRange, setIsScreenSizeInRange] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(
      '(min-width: 1280px) and (max-width: 1320px)',
    );

    const handleMediaQueryChange = (event: MediaQueryListEvent) => {
      setIsScreenSizeInRange(event.matches);
    };

    setIsScreenSizeInRange(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleMediaQueryChange);

    return () => {
      mediaQuery.removeEventListener('change', handleMediaQueryChange);
    };
  }, []);

  return isScreenSizeInRange;
};
