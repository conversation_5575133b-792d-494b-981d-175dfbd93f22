export const isValidString = (value: unknown): value is string => {
  return typeof value === 'string' && value.length > 0;
};

export const isValidArray = <T>(value: unknown): value is T[] => {
  return Array.isArray(value);
};

export const isValidStringArray = (value: unknown): value is string[] => {
  return Array.isArray(value) && value.every(item => typeof item === 'string');
};

export const safeArrayOperation = <T>(
  array: T[] | undefined | null,
  defaultValue: T[] = [],
): T[] => {
  return isValidArray(array) ? array : defaultValue;
};

export const safeStringArrayOperation = (
  array: string[] | undefined | null,
  defaultValue: string[] = [],
): string[] => {
  return isValidStringArray(array) ? array : defaultValue;
};

export const safeIncludes = (
  array: string[] | undefined | null,
  searchValue: string,
): boolean => {
  const safeArray = safeStringArrayOperation(array);
  return safeArray.includes(searchValue);
};

export const safeIndexOf = (
  array: string[] | undefined | null,
  searchValue: string,
): number => {
  const safeArray = safeStringArrayOperation(array);
  return safeArray.indexOf(searchValue);
};
