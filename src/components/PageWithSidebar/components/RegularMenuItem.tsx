import React from 'react';

import { Tooltip } from 'antd';
import { twMerge } from 'tailwind-merge';

import { BodyMdExtend, FontMedium } from 'assets/styles';

import { MenuItemText, SidebarMenuItem } from '../styles';
import { DetailedMenuItem, MultipleSidebarMenuItems } from '../types';

interface RegularMenuItemProps {
  item: MultipleSidebarMenuItems;
  collapsed: boolean;
  selectedKeys: string[];
  onToggleOpen: (key: string) => void;
  onItemClick: (route: string) => void;
}

const RegularMenuItem: React.FC<RegularMenuItemProps> = ({
  item,
  collapsed,
  selectedKeys,
  onToggleOpen,
  onItemClick,
}) => {
  const handleToggleOpen = () => {
    if (item.key) {
      onToggleOpen(item.key);
    }
  };

  const handleItemClick = () => {
    if (item.route) {
      onItemClick(item.route);
    }
  };

  const isActive = item.key && selectedKeys.includes(item.key);

  if (collapsed) {
    return (
      <Tooltip
        className='menu-collapse-tooltip'
        title={item.label}
        placement='right'
        arrow={{
          pointAtCenter: true,
          arrowPointAtCenter: true,
        }}
        color='white'
        overlayInnerStyle={{
          padding: '12px 16px',
          borderRadius: 'var(--Tooltip-radiusCorner, 8px)',
          background: 'var(--Tooltip-background, #FFF)',
          boxShadow: '0px 1px 8px 0px rgba(0, 0, 0, 0.20)',
          color: '#000',
          fontSize: '14px',
          fontWeight: '500',
        }}
      >
        <SidebarMenuItem
          collapsed={collapsed}
          key={item.key}
          onClick={handleItemClick}
          className={twMerge(
            'flex items-center justify-center',
            isActive
              ? 'bg-brand-300 text-black-1000 hover:bg-brand-300'
              : 'hover:bg-grey-50',
          )}
        >
          <MenuItemText onClick={handleToggleOpen}>
            <div
              role='button'
              className={twMerge(
                'navio-menu-item-icon flex items-start justify-center',
                isActive ? 'text-black-1000' : 'text-grey-600',
                collapsed ? 'collapsed' : '',
              )}
            >
              {item.icon && <item.icon />}
            </div>
          </MenuItemText>
        </SidebarMenuItem>
      </Tooltip>
    );
  }

  return (
    <SidebarMenuItem
      collapsed={collapsed}
      key={item.key}
      onClick={handleItemClick}
      className={twMerge(
        'flex items-center justify-center',
        isActive
          ? 'bg-brand-300 text-black-1000 hover:bg-brand-300'
          : 'hover:bg-grey-50',
      )}
    >
      <MenuItemText onClick={handleToggleOpen}>
        <div
          role='button'
          className={twMerge(
            'navio-menu-item-icon flex items-start justify-center',
            isActive ? 'text-black-1000' : 'text-grey-600',
            collapsed ? 'collapsed' : '',
          )}
        >
          {item.icon && <item.icon />}
        </div>
        <div className={`${BodyMdExtend} ${FontMedium} flex flex-1`}>
          {item.label}
        </div>
        {(item as DetailedMenuItem)?.rightElement}
      </MenuItemText>
    </SidebarMenuItem>
  );
};

export default RegularMenuItem;
