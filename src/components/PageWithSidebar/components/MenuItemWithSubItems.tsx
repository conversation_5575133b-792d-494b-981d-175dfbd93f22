import React from 'react';

import { Popover } from 'antd';
import { twMerge } from 'tailwind-merge';

import { ChevronRightIcon } from 'assets/icons';
import { BodyMdExtend, FontMedium } from 'assets/styles';

import { hasActiveSubItems } from '../helpers';
import { MenuItemText, SidebarMenuItem } from '../styles';
import { DetailedMenuItem, MultipleSidebarMenuItems } from '../types';
import SubMenuPopover from './SubMenuPopover';

interface MenuItemWithSubItemsProps {
  item: MultipleSidebarMenuItems;
  collapsed: boolean;
  selectedKeys: string[];
  onToggleOpen: (key: string) => void;
  onSubItemClick: (route: string, selectedKeys: string[]) => void;
}

const MenuItemWithSubItems: React.FC<MenuItemWithSubItemsProps> = ({
  item,
  collapsed,
  selectedKeys,
  onToggleOpen,
  onSubItemClick,
}) => {
  const handleToggleOpen = () => {
    if (item.key) {
      onToggleOpen(item.key);
    }
  };

  const isParentActive =
    selectedKeys.includes(item.key!) || hasActiveSubItems(item, selectedKeys);

  return (
    <Popover
      content={
        <SubMenuPopover
          parentKey={item.key!}
          subItems={item.subItems!}
          selectedKeys={selectedKeys}
          onSubItemClick={onSubItemClick}
        />
      }
      trigger='hover'
      placement='rightTop'
      arrow={false}
    >
      <SidebarMenuItem
        collapsed={collapsed}
        key={item.key}
        className={twMerge(
          'flex items-center justify-center',
          isParentActive
            ? 'bg-brand-300 text-black-1000 hover:bg-brand-300'
            : 'hover:bg-grey-50',
        )}
      >
        <span className='flex items-center justify-center'>
          <MenuItemText onClick={handleToggleOpen}>
            <div
              role='button'
              className={twMerge(
                'navio-menu-item-icon flex items-start justify-center',
                isParentActive ? 'text-black-1000' : 'text-grey-600',
                collapsed ? 'collapsed' : '',
              )}
            >
              {item.icon && <item.icon />}
            </div>
            {!collapsed && (
              <>
                <div className={`${BodyMdExtend} ${FontMedium} flex flex-1`}>
                  {item.label}
                </div>
                {(item as DetailedMenuItem)?.rightElement}
              </>
            )}
          </MenuItemText>
          {!collapsed && (
            <div
              className={twMerge(
                'navio-menu-item-icon flex items-start justify-center',
                isParentActive ? 'text-black-1000' : 'text-grey-600',
                collapsed ? 'collapsed' : '',
              )}
            >
              <ChevronRightIcon />
            </div>
          )}
        </span>
      </SidebarMenuItem>
    </Popover>
  );
};

export default MenuItemWithSubItems;
