import React from 'react';

import tw from 'tailwind-styled-components';

import { getSelectedKeysForSubItem, isSubItemActive } from '../helpers';
import { DetailedMenuItem } from '../types';

interface SubRouteProps {
  isActive?: boolean;
}

const SubRoute = tw.div<SubRouteProps>`
  flex h-[40px] items-center cursor-pointer px-3 py-2 rounded-md
  transition-all duration-200 ease-in-out
  position-relative hover:bg-grey-50 font-medium

  ${props =>
    !props.isActive
      ? `
    text-gray-1000
    hover:bg-grey-50
    hover:text-grey-1000
  `
      : ''}

  ${props =>
    props.isActive
      ? `
    bg-brand-300
    text-white
  `
      : ''}
`;

interface SubMenuPopoverProps {
  parentKey: string;
  subItems: DetailedMenuItem[];
  selectedKeys: string[];
  onSubItemClick: (route: string, selectedKeys: string[]) => void;
}

const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  parentKey,
  subItems,
  selectedKeys,
  onSubItemClick,
}) => {
  return (
    <div className='flex flex-col gap-2'>
      {subItems.map((subItem, index) => {
        const isActive = isSubItemActive(
          parentKey,
          subItem,
          index,
          selectedKeys,
        );

        return (
          <SubRoute
            key={subItem.label}
            isActive={isActive}
            onClick={() => {
              const newSelectedKeys = getSelectedKeysForSubItem(
                parentKey,
                subItem,
                index,
              );
              onSubItemClick(subItem.route ?? '', newSelectedKeys);
            }}
          >
            {subItem.label}
          </SubRoute>
        );
      })}
    </div>
  );
};

export default SubMenuPopover;
