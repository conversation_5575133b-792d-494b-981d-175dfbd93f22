import { BodySm, FontSemibold } from 'assets/styles';

import { REGEX_DIVIDER } from '../constants';
import { Divider } from '../styles';
import { MultipleSidebarMenuItems } from '../types';
import MenuItemWithSubItems from './MenuItemWithSubItems';
import RegularMenuItem from './RegularMenuItem';

type MenuItemHandler = {
  openKeys: string[];
  setOpenKeys: (keys: string[]) => void;
  onClickItem: (route: string) => void;
  selectedKeys: string[];
  updateSelectedKeys: (keys: string[]) => void;
};

type MenuMapperFunction = (
  handlers: MenuItemHandler,
  collapsed: boolean,
) => (item: MultipleSidebarMenuItems) => JSX.Element;

const menuItemMapper: MenuMapperFunction =
  (handlers: MenuItemHandler, collapsed: boolean) =>
  (item: MultipleSidebarMenuItems) => {
    const {
      openKeys,
      setOpenKeys,
      onClickItem,
      selectedKeys,
      updateSelectedKeys,
    } = handlers;

    if (REGEX_DIVIDER.test(item.label)) {
      return <Divider key={item.key} />;
    }

    if (item.menu) {
      return (
        <>
          {!collapsed && (
            <div
              key={item.key}
              className={`${BodySm} ${FontSemibold} text-grey-400`}
            >
              {item.label}
            </div>
          )}
          {item.menu.map(menuItemMapper(handlers, collapsed))}
        </>
      );
    }

    const handleToggleOpen = (key: string) => {
      if (openKeys.includes(key)) {
        setOpenKeys(openKeys.filter(k => k !== key));
      } else {
        setOpenKeys([...openKeys, key]);
      }
    };

    const handleSubItemClick = (route: string, newSelectedKeys: string[]) => {
      updateSelectedKeys(newSelectedKeys);
      onClickItem(route);
    };

    if (item.subItems?.length) {
      return (
        <MenuItemWithSubItems
          key={item.key}
          item={item}
          collapsed={collapsed}
          selectedKeys={selectedKeys}
          onToggleOpen={handleToggleOpen}
          onSubItemClick={handleSubItemClick}
        />
      );
    }

    return (
      <RegularMenuItem
        key={item.key}
        item={item}
        collapsed={collapsed}
        selectedKeys={selectedKeys}
        onToggleOpen={handleToggleOpen}
        onItemClick={onClickItem}
      />
    );
  };

export default menuItemMapper;
