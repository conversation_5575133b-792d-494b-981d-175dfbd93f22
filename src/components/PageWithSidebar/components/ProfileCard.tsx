import { useState } from 'react';

import { useProfile } from 'hooks/useProfile';
import { useTranslation } from 'react-i18next';
import { NameInitialsAvatar } from 'react-name-initials-avatar';
import { useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { useProfileMutations } from 'features/Profile/hooks/useProfileMutations';

import images from 'assets/images';
import { BodyMdBase, Colors, FontBold, InputSm } from 'assets/styles';
import { Icon, RRConfirmationModal } from 'components';

// /navio-api/users/sign_out

const ProfileCard = ({ collapsed }: { collapsed: boolean }) => {
  const { signOut } = useProfileMutations();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { profile } = useProfile();
  const [showConfirm, setShowConfirm] = useState(false);

  const handleLogout = () => {
    signOut.mutate();
  };

  return (
    <CardContainer>
      <AvatarSection>
        <div
          className='cursor-pointer'
          role='button'
          onClick={() => navigate('/profile')}
        >
          {profile?.avatar_url ? (
            <Avatar
              src={`https://core.navio.asia/${profile.avatar_url}`}
              alt='profile-picture'
            />
          ) : (
            <NameInitialsAvatar
              bgColor={Colors.brand[300]}
              borderColor={Colors.brand[300]}
              name={profile?.username?.toUpperCase() || ''}
            />
          )}
        </div>

        {!collapsed && (
          <ProfileButton
            className={!collapsed ? 'ml-2 ' : ''}
            onClick={() => navigate('/profile')}
          >
            <InfoSection>
              <DeviceName className={`${BodyMdBase} ${FontBold} text-start`}>
                {profile?.username}
              </DeviceName>
              <Phone className={`${InputSm} text-grey-600`}>
                {profile?.phone_no}
              </Phone>
            </InfoSection>
            <ProfileDetailButton className='ml-auto'>
              <Icon src={images.Icon.ChevronRight} />
            </ProfileDetailButton>
          </ProfileButton>
        )}
      </AvatarSection>
      <LogoutButton
        onClick={() => {
          setShowConfirm(true);
        }}
        className='mt-6 flex w-full items-center justify-center rounded-lg border border-red-200 py-2 text-red-200 hover:bg-red-10'
      >
        <Icon src={images.Icon.SignOut} className={!collapsed ? 'mr-2' : ''} />
        {!collapsed && t('sidebar.logout')}
      </LogoutButton>
      <RRConfirmationModal
        title={t('history.logoutstitle')}
        message={t('history.logoutsContent')}
        onCancel={() => {
          setShowConfirm(false);
        }}
        isSubmitting={signOut.isPending}
        onConfirm={handleLogout}
        visible={showConfirm}
      />
    </CardContainer>
  );
};

export default ProfileCard;

const CardContainer = tw.div`mb-4 rounded-lg`;
const AvatarSection = tw.div`flex items-center justify-center`;
const Avatar = tw.img`rounded-full w-10 h-10`;
const InfoSection = tw.div`flex items-start justify-center flex-col`;
const DeviceName = tw.p`text-black-1000`;
const Phone = tw.p`text-gray-600`;
const ProfileDetailButton = tw.button`ml-auto`;
const LogoutButton = tw.button`mt-6 flex w-full items-center justify-center rounded-lg border border-red-200 py-2 text-red-200`;
const ProfileButton = tw.button`flex flex-row flex-1 items-center justify-start`;
