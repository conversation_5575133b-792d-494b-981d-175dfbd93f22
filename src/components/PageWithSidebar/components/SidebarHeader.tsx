import React from 'react';

import images from 'assets/images';
import { Icon } from 'components';

import { ICON_SIZES } from '../constants';
import { MainLogo, TitleContainer } from '../styles';

interface SidebarHeaderProps {
  collapsed: boolean;
  onCollapseClick: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
  collapsed,
  onCollapseClick,
}) => {
  return (
    <TitleContainer collapsed={collapsed}>
      <MainLogo
        src={collapsed ? images.Login.LogoCollapsed : images.Login.Logo}
        alt='Logo'
      />
      <Icon
        onClick={onCollapseClick}
        {...ICON_SIZES.MENU_ITEM}
        src={
          collapsed ? images.Icon.ExpandSidebar : images.Icon.CollapseSidebar
        }
        className='cursor-pointer'
      />
    </TitleContainer>
  );
};

export default SidebarHeader;
