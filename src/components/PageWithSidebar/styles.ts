import { Menu as AntdMenu } from 'antd';
import tw from 'tailwind-styled-components';

export const PageContainer = tw.div`flex h-screen w-full contain-layout`;

export const MainContainer = tw.div<{ collapsed?: boolean }>`
  flex-col h-screen
  transition-all duration-250
  will-change-[width]
`;

export const MainLogo = tw.img`w-[115px] h-[24px]`;

export const SidebarContainer = tw.div<{ collapsed?: boolean }>`
  px-4
  border-r
  border-grey-100
  h-full
  flex
  flex-col
  min-w-[80px]
  max-w-[240px]
  overflow-hidden
  transition-all
  duration-250
  will-change-[width]
  contain-layout
  ${p =>
    p.collapsed
      ? 'w-[80px] ease-[cubic-bezier(0.4,0,0.2,1)]'
      : 'w-[240px] ease-[cubic-bezier(0.4,0,0.2,1)]'}
`;

export const SidebarMenuItem = tw(AntdMenu.Item)<{ collapsed?: boolean }>`
  !mx-0
  !px-2
  !h-10
  !w-full
  mb-2
  ${p => (p.collapsed ? '!w-full !p-3' : undefined)}
`;

export const MenuItemText = tw.div`flex flex-1 items-center justify-center w-full gap-3`;

export const SidebarMenu = tw(AntdMenu)`!border-e-0`;

export const SidebarMenuContainer = tw.div`w-full h-full`;

export const Divider = tw(AntdMenu.Divider)`h-1 !my-3 !bg-gray-200 `;

export const TitleContainer = tw.div<{ collapsed?: boolean }>`
  w-full flex items-center justify-between pt-6 pb-4
  ${p => (p.collapsed ? 'flex-col gap-4' : 'flex-row')}
`;

export const MenuSliderWrapper = tw.div`
  flex overflow-hidden flex-1 relative h-full w-full
`;
