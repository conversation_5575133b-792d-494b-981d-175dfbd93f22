/* Container wrapper */
.rr-table-wrapper {
  border: 1px solid #e1e3eb !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* Override Ant Design Table styles */
.rr-table-wrapper .rr-table-antd {
  border-radius: 0 !important;
}

.rr-table-wrapper .rr-table-antd .ant-table {
  border-radius: 0 !important;
  border: none !important;
}

.rr-table-wrapper .rr-table-antd .ant-table-container {
  border-radius: 0 !important;
}

.rr-table-wrapper .rr-table-antd .ant-table-content {
  border-radius: 0 !important;
}

/* Header styling */
.rr-table-wrapper .ant-table-thead > tr > th {
  background-color: #e1e3eb !important;
  border-bottom: 1px solid #e1e3eb !important;
  border-right: 1px solid #e1e3eb !important;
  height: 39px !important;
  padding: 0 16px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  color: #54565c;
  position: sticky !important;
  top: 0 !important;
  background: #f0f1f5 !important;
  z-index: 1 !important;
  box-shadow: none !important;
}

.rr-table-wrapper thead.ant-table-thead > tr > th:last-child {
  border-right: none !important;
  border-inline-end: none !important;
}
.rr-table-wrapper thead.ant-table-thead > tr > th:nth-last-child(2) {
  border-left: none !important;
  border-inline-end: none !important;
}

/* gần cuối css */

.ant-table-wrapper .ant-table-bordered .ant-table-cell-scrollbar {
  border-right: none !important;
}

.rr-table-wrapper .ant-table-thead > tr > th:last-child {
  border-inline-end: none !important;
}

/* Body cell styling */
.rr-table-wrapper
  .ant-table-container
  .ant-table-tbody
  > tr.ant-table-measure-row {
  height: 0px !important;
  min-height: 0px !important;
  max-height: 0px !important;
}

.rr-table-wrapper .ant-table-container .ant-table-tbody > tr {
  min-height: 40px !important;
  height: 40px !important;
}
.rr-table-wrapper .ant-table-container .ant-table-tbody > tr > td {
  padding: 8px 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  min-width: 80px;
  border-color: #e1e3eb !important;
}
.rr-table-wrapper .ant-table-tbody > tr > td:last-child {
  border-inline-end: none !important;
}

.rr-table-wrapper .ant-table-tbody > tr:last-child td {
  border-bottom: none !important;
}

.rr-table-wrapper .rr-table-antd .ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* border-inline-start */

.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
  border-inline-start: none !important;
  border-inline-end: none !important;
}

.rr-table-wrapper .rr-table-antd .ant-table-tbody > tr:last-child > td {
  border-bottom: none !important;
}

.rr-table-wrapper .rr-table-antd .ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* Hover effect */
.rr-table-wrapper .rr-table-antd .ant-table-tbody > tr:hover > td {
  background-color: #f0f1f5 !important;
}

.rr-table-wrapper .ant-table-body {
  overflow-y: auto !important;
  overflow-x: auto !important;
}

/* Special styling for device log table */
.device-log-table.rr-table-wrapper .ant-table-tbody > tr > td {
  max-width: fit-content;
}
