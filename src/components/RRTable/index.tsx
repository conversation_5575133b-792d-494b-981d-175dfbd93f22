import React, { useEffect, useMemo, useRef, useState } from 'react';

import { Table } from 'antd';
import { Spin } from 'antd';
import type { ColumnsType } from 'antd/es/table';

import Pagination from 'components/Pagination';

import './table.css';

interface RRTableProps {
  className?: string;
  columns: ColumnsType<Record<string, unknown>>;
  data: Record<string, unknown>[];
  hidePagination?: boolean;
  loading?: boolean; // Loading state
  tableLayout?: 'fixed' | 'auto';
  scroll?: any;
  headerRows?: number;

  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange?: (page: number, pageSize: number) => void;
  onPageSizeChange?: (size: number) => void;
}

const RRTable: React.FC<RRTableProps> = ({
  className,
  tableLayout = 'auto',
  columns,
  data,
  total,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  scroll,
  hidePagination,
  loading = false,
  headerRows = 1,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [calculatedHeight, setCalculatedHeight] = useState<number | null>(null);

  const paddedData = useMemo(() => {
    if (data.length >= pageSize) {
      return data;
    }

    const emptyRowsCount = pageSize - data.length;
    const emptyRows = Array.from({ length: emptyRowsCount }, (_, index) => ({
      key: `empty-row-${index}`,
      isEmpty: true,
    }));

    return [...data, ...emptyRows];
  }, [data, pageSize]);

  useEffect(() => {
    const calculateTableHeight = () => {
      if (!containerRef.current) return;

      setCalculatedHeight(containerRef.current.clientHeight);
    };

    const timer = setTimeout(calculateTableHeight, 50);

    window.addEventListener('resize', calculateTableHeight);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, []);

  const tableHeight = useMemo(() => {
    if (!calculatedHeight) return null;
    return !hidePagination ? calculatedHeight - 64 : calculatedHeight;
  }, [calculatedHeight, hidePagination]);

  return (
    <div className='relative flex size-full flex-col overflow-hidden'>
      <div ref={containerRef} className='z-1 absolute inset-0' />
      {loading || !tableHeight ? (
        <div className='flex h-full items-center justify-center'>
          <Spin />
        </div>
      ) : (
        <>
          <div
            style={{ height: `${tableHeight}px` }}
            className='relative z-10  overflow-hidden'
          >
            <Table
              bordered
              columns={columns}
              dataSource={paddedData}
              pagination={false}
              className={`rr-table-wrapper ${className || ''}`}
              scroll={{ y: tableHeight - (headerRows * 40 + 2), ...scroll }}
              tableLayout={tableLayout}
              loading={loading}
              rowClassName={record => (record.isEmpty ? 'empty-row' : '')}
            />
          </div>
          {!hidePagination && (
            <div className='z-10 mt-4 shrink-0'>
              <Pagination
                page={currentPage}
                pageSize={pageSize}
                total={total}
                onPageChange={onPageChange}
                onChangePerPage={onPageSizeChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RRTable;
