/**
 * Generate màu hex từ text string sử dụng hash algorithm
 */
export const generateColorFromText = (text: string): string => {
  if (!text || text.trim() === '') {
    return '#666666'; // Default gray color
  }

  // Simple hash function
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Convert hash to positive number
  hash = Math.abs(hash);

  // Generate RGB values with good contrast
  const hue = hash % 360;
  const saturation = 65 + (hash % 25); // 65-90%
  const lightness = 45 + (hash % 15); // 45-60%

  return hslToHex(hue, saturation, lightness);
};

/**
 * Convert HSL to HEX color
 */
const hslToHex = (h: number, s: number, l: number): string => {
  const sNorm = s / 100;
  const lNorm = l / 100;

  const c = (1 - Math.abs(2 * lNorm - 1)) * sNorm;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = lNorm - c / 2;

  let r = 0,
    g = 0,
    b = 0;

  if (0 <= h && h < 60) {
    r = c;
    g = x;
    b = 0;
  } else if (60 <= h && h < 120) {
    r = x;
    g = c;
    b = 0;
  } else if (120 <= h && h < 180) {
    r = 0;
    g = c;
    b = x;
  } else if (180 <= h && h < 240) {
    r = 0;
    g = x;
    b = c;
  } else if (240 <= h && h < 300) {
    r = x;
    g = 0;
    b = c;
  } else if (300 <= h && h < 360) {
    r = c;
    g = 0;
    b = x;
  }

  // Convert to 0-255 range and format as hex
  const red = Math.round((r + m) * 255);
  const green = Math.round((g + m) * 255);
  const blue = Math.round((b + m) * 255);

  return `#${red.toString(16).padStart(2, '0')}${green
    .toString(16)
    .padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
};

/**
 * Predefined color palette cho common text values
 */
export const predefinedColors: Record<string, string> = {
  // Status colors
  active: '#10B981',
  inactive: '#6B7280',
  pending: '#F59E0B',
  error: '#EF4444',
  success: '#10B981',
  warning: '#F59E0B',

  // Priority colors
  high: '#EF4444',
  medium: '#F59E0B',
  low: '#10B981',

  // Business colors
  account: '#3B82F6',
  device: '#8B5CF6',
  sim: '#EC4899',
  report: '#06B6D4',
};

/**
 * Get color từ text với fallback sang predefined colors
 */
export const getColorFromText = (text: string): string => {
  const normalizedText = text.toLowerCase().trim();

  // Check predefined colors first
  if (predefinedColors[normalizedText]) {
    return predefinedColors[normalizedText];
  }

  // Generate from text
  return generateColorFromText(text);
};
