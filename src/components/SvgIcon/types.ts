import React from 'react';

export interface SvgIconProps {
  // Icon component từ SVG import
  Icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  // Text để generate màu (optional)
  colorFromText?: string;
  // Custom color (override colorFromText)
  color?: string;
  // Size
  size?: number | string;
  // Custom className
  className?: string;
  // Các props khác cho SVG
  [key: string]: any;
}
