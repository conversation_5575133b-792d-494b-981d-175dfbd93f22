import React from 'react';

import { generateColorFromText } from './utils';

interface SvgIconProps {
  // Icon component từ SVG import
  Icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  // Text để generate màu (optional)
  colorFromText?: string;
  // Custom color (override colorFromText)
  color?: string;
  // Size
  size?: number | string;
  // Custom className
  className?: string;
  // Các props khác cho SVG
  [key: string]: any;
}

const SvgIcon: React.FC<SvgIconProps> = ({
  Icon,
  colorFromText,
  color,
  size = 20,
  className = '',
  ...props
}) => {
  // Determine final color
  const finalColor = React.useMemo(() => {
    if (color) return color;
    if (colorFromText) return generateColorFromText(colorFromText);
    return 'currentColor'; // Use CSS color
  }, [color, colorFromText]);

  // Convert size to CSS value
  const sizeValue = typeof size === 'number' ? `${size}px` : size;

  return (
    <Icon
      className={className}
      style={{
        width: sizeValue,
        height: sizeValue,
        color: finalColor,
        fill: 'currentColor',
        ...props.style,
      }}
      {...props}
    />
  );
};

export default SvgIcon;
