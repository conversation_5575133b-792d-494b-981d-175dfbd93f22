import React from 'react';

import BusinessIcon from 'assets/images/Business.svg?react';
// Import SVG như React components với ?react suffix
import HomeIcon from 'assets/images/Home.svg?react';
import UserIcon from 'assets/images/User-circle.svg?react';
import { SvgIcon } from 'components';

const SvgIconDemo: React.FC = () => {
  const users = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  const statuses = ['active', 'inactive', 'pending', 'error'];

  return (
    <div className='space-y-6 p-6'>
      <div>
        <h3 className='mb-4 text-lg font-semibold'>
          1. Icon với màu từ user names:
        </h3>
        <div className='flex gap-4'>
          {users.map(user => (
            <div key={user} className='flex flex-col items-center gap-2'>
              <SvgIcon Icon={UserIcon} colorFromText={user} size={32} />
              <span className='text-gray-600 text-sm'>{user}</span>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className='mb-4 text-lg font-semibold'>
          2. Icon với predefined status colors:
        </h3>
        <div className='flex gap-4'>
          {statuses.map(status => (
            <div key={status} className='flex flex-col items-center gap-2'>
              <SvgIcon Icon={HomeIcon} colorFromText={status} size={24} />
              <span className='text-gray-600 text-sm capitalize'>{status}</span>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className='mb-4 text-lg font-semibold'>
          3. Icon với custom colors:
        </h3>
        <div className='flex gap-4'>
          <SvgIcon Icon={BusinessIcon} color='#FF5722' size={28} />
          <SvgIcon Icon={BusinessIcon} color='#2196F3' size={28} />
          <SvgIcon Icon={BusinessIcon} color='#4CAF50' size={28} />
          <SvgIcon Icon={BusinessIcon} className='text-purple-500' size={28} />
        </div>
      </div>

      <div>
        <h3 className='mb-4 text-lg font-semibold'>
          4. Icon với different sizes:
        </h3>
        <div className='flex items-center gap-4'>
          <SvgIcon Icon={UserIcon} colorFromText='Small' size={16} />
          <SvgIcon Icon={UserIcon} colorFromText='Medium' size={24} />
          <SvgIcon Icon={UserIcon} colorFromText='Large' size={32} />
          <SvgIcon Icon={UserIcon} colorFromText='XLarge' size={48} />
        </div>
      </div>
    </div>
  );
};

export default SvgIconDemo;
