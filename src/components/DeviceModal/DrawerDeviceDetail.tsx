import { useCallback, useEffect } from 'react';

import { useQuery } from '@tanstack/react-query';
import { Drawer, Tabs } from 'antd';
import dayjs from 'dayjs';
import useDebounceValue from 'hooks/useDebounceValue';
import { FormProvider, useForm } from 'react-hook-form';

import { MODAL_TYPES } from 'features/DeviceBusiness/constants';
import {
  useDeviceDetail,
  useDeviceMutations,
  useDeviceState,
} from 'features/DeviceBusiness/hooks';
import {
  parseDeviceToForm,
  payloadUploadDevice,
} from 'features/DeviceBusiness/utils';
import { t } from 'i18next';
import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn } from 'services/reactQuery';

import images from 'assets/images';
import { FontBold, TitleMd } from 'assets/styles';
import { Icon } from 'components';
import { LinkButton, PrimaryButton } from 'components/Button';

import { DeviceLogModal, TransferDeviceModal } from '.';
import { ConfigForm, DeviceInfoForm, StatusForm } from '../DeviceDetail';

// Define the form data interface
interface DeviceFormData {
  transport_type?: string;
  transport_department_id?: string;
  device_category?: string;
  is_allow_data_transport?: boolean;
  device_name?: string;
  device_plate_number?: string;
  device_ccid?: string;
  device_sim_number?: string;
  max_allowable_ignition_swich_time?: number;
  max_allowable_speed?: number;
  transport_ownership?: string;
  service_package_id?: string;
  activated_at?: string;
  expired_at?: string;
  gprs_interval?: number;
  min_speed?: number;
  timezone?: string;
  sensor_type?: string;
  stop_distance?: number;
  stop_time?: number;
  device_pin?: string;
  device_status?: string;
  device_power?: string;
  [key: string]: any; // For additional fields that might come from deviceDetail
}

const { TabPane } = Tabs;

interface Props {
  visible: boolean;
  onClose: () => void;
  selectedImei: string;
}

const DrawerDeviceDetail = (props: Props) => {
  const { visible, onClose, selectedImei } = props;
  const {
    state,
    openModal,
    closeModal,
    onSelectedMultipleDevice,
    onResetMultipleDevice,
  } = useDeviceState();
  const { modals, multipleSelect } = state || {};

  const { data: deviceDetail, refetch } = useDeviceDetail({
    imei: selectedImei,
  });

  const { updateDevice, transferDevices } = useDeviceMutations();

  const methods = useForm<DeviceFormData>({
    defaultValues: {},
  });
  const { formState, handleSubmit, watch } = methods;

  const watchDeviceSimNumber = watch('device_sim_number') as string;
  const debouncedWatchDeviceSimNumber = useDebounceValue(
    watchDeviceSimNumber,
    200,
  );

  // Todo: inventory, packageFee filter ??? keyword format filter
  const { data: SimData } = useQuery({
    queryKey: ['sim_storages', debouncedWatchDeviceSimNumber],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/sim_storages',
        query: {
          page: '1',
          per_page: '1',
          search_keyword: String(debouncedWatchDeviceSimNumber) || '',
        },
      }),
    }),
    staleTime: 30000,
    select: (data: any) => {
      return {
        data: data.sim_storages || [],
        pagination: data.pagination || {},
      };
    },
    enabled: !!(
      debouncedWatchDeviceSimNumber && debouncedWatchDeviceSimNumber !== '-'
    ),
  });

  const onSubmit = useCallback(
    data => {
      const payload = payloadUploadDevice(data);
      updateDevice.mutate({
        imei: selectedImei,
        deviceInfo: payload,
      });
    },
    [selectedImei, updateDevice],
  );

  const handleTransferMultipleAccount = (imeis: string[], userId: string) => {
    transferDevices.mutate(
      {
        imeis,
        to_user_id: userId,
        note: '',
      },
      {
        onSuccess: () => {
          closeModal(MODAL_TYPES.MOVE_DEVICE);
          onResetMultipleDevice();
        },
      },
    );
  };

  const handleOpenTransferModal = useCallback(() => {
    onSelectedMultipleDevice([deviceDetail]);
    setTimeout(() => {
      openModal(MODAL_TYPES.MOVE_DEVICE);
    }, 0);
  }, [openModal, deviceDetail, selectedImei]);

  useEffect(() => {
    if (deviceDetail) {
      methods.reset(parseDeviceToForm(deviceDetail));
    }
  }, [deviceDetail, methods]);

  useEffect(() => {
    if (visible) {
      refetch();
    }
  }, [visible]);

  useEffect(() => {
    if (SimData?.data?.length > 0) {
      methods.setValue('device_ccid', SimData?.data[0]?.ccid);
    } else {
      methods.setValue('device_ccid', 'Không có dữ liệu');
    }
  }, [SimData, methods]);

  return (
    <>
      <Drawer
        title={
          <div className='flex items-center justify-between'>
            <div className='flex gap-[64px]'>
              <div className={`${TitleMd} ${FontBold}`}>
                {t('deviceDetail.deviceDetails')}
              </div>
              <div className='flex items-center gap-7'>
                <LinkButton
                  onClick={() => openModal(MODAL_TYPES.DEVICE_LOG)}
                  size='small'
                  iconPosition='left'
                  className='h-6'
                  icon={<Icon src={images.Icon.Calendar} />}
                >
                  {t('deviceDetail.deviceLog')}
                </LinkButton>
                <LinkButton
                  onClick={handleOpenTransferModal}
                  size='small'
                  iconPosition='left'
                  className='h-6'
                  icon={<Icon src={images.Icon.ChangeDevice} />}
                >
                  {t('deviceDetail.transferDevice')}
                </LinkButton>
              </div>
            </div>
            <button
              type='button'
              className='flex cursor-pointer items-center gap-2'
              onClick={onClose}
            >
              <Icon className='size-6 rounded-full ' src={images.Icon.X} />
            </button>
          </div>
        }
        width={1000}
        onClose={onClose}
        open={visible}
        closable={false}
        headerStyle={{
          borderBottom: 'none',
          padding: '16px 24px 0px 24px',
        }}
      >
        <div className='h-full'>
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className='relative'>
              <Tabs
                className='size-full'
                style={{ height: '100%' }}
                tabPosition='left'
              >
                <TabPane tab={t('deviceDetail.transportInfo')} key='1'>
                  <DeviceInfoForm />
                </TabPane>
                <TabPane tab={t('deviceDetail.status')} key='2'>
                  <StatusForm device={deviceDetail} />
                </TabPane>
                <TabPane tab={t('deviceDetail.configuration')} key='3'>
                  <ConfigForm />
                </TabPane>
              </Tabs>
              <div className='absolute bottom-[24px] left-[230px] right-[24px]'>
                <PrimaryButton
                  htmlType='submit'
                  className='w-full'
                  disabled={
                    updateDevice.isPending ||
                    !formState.isDirty ||
                    !formState.isValid
                  }
                  loading={updateDevice.isPending}
                >
                  {t('deviceDetail.saveInfo')}
                </PrimaryButton>
              </div>
            </form>
          </FormProvider>
        </div>
      </Drawer>
      <DeviceLogModal
        deviceImei={selectedImei}
        isVisible={modals[MODAL_TYPES.DEVICE_LOG]}
        onClose={() => closeModal(MODAL_TYPES.DEVICE_LOG)}
      />
      <TransferDeviceModal
        isLoading={transferDevices.isPending}
        multipleDeviceSelected={multipleSelect.multipleDevice}
        visible={modals[MODAL_TYPES.MOVE_DEVICE]}
        onSelectedMultipleDevice={onSelectedMultipleDevice}
        onClose={() => closeModal(MODAL_TYPES.MOVE_DEVICE)}
        onSubmit={handleTransferMultipleAccount}
      />
    </>
  );
};

export default DrawerDeviceDetail;
