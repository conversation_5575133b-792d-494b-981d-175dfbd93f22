import React, { useCallback, useEffect, useState } from 'react';

import { Modal, Upload, UploadFile, UploadProps } from 'antd';
import { DeviceOptions } from 'constants/device';
import dayjs from 'dayjs';
import { yupResolver } from 'hooks/useYupResolver';
import { FormProvider, useForm } from 'react-hook-form';
import tw from 'tailwind-styled-components';
import * as XLSX from 'xlsx';

import { useDeviceMutations } from 'features/DeviceBusiness/hooks';
import { createDeviceSchema } from 'features/DeviceBusiness/schema/deviceSchema';
import { AccountBreadCrumbItem } from 'features/DeviceBusiness/types';
import { t } from 'i18next';

import images from 'assets/images';
import {
  Icon,
  RRFieldDatePicker,
  RRFieldInput,
  RRFieldSelect,
} from 'components';
import AccountBreadCrumb from 'components/AccountBreadcrumb';
import { LinkButton, PrimaryButton, SecondaryButton } from 'components/Button';
import { StandardFormats } from 'components/RRCalendar';
import { CollapseItemData } from 'components/RRCollapseTable';
import showToast from 'components/RRToastMessage/Toast';

interface AddDeviceModalProps {
  visible: boolean;
  selectedAccount: CollapseItemData | null;
  accountBreadcrumb: AccountBreadCrumbItem[];
  onClose?: () => void;
}
interface ExcelData {
  imei: string;
  device_name?: string;
  device_sim_number?: string;
}

const DEFAULT_VALUES = {
  activationDate: dayjs(),
  type: '',
  imei: '',
};

const AddDeviceModal: React.FC<AddDeviceModalProps> = ({
  selectedAccount,
  accountBreadcrumb,
  visible,
  onClose,
}) => {
  const [currentFile, setCurrentFile] = useState<UploadFile>();
  const [excelData, setExcelData] = useState<ExcelData[]>([]);

  const methods = useForm({
    resolver: yupResolver(createDeviceSchema),
    defaultValues: DEFAULT_VALUES,
    mode: 'onBlur',
  });

  const { handleSubmit, formState, control, setValue } = methods || {};
  const { errors } = formState || {};

  const { createDevice } = useDeviceMutations();

  const handleRemoveFile = () => {
    setCurrentFile(undefined);
    setExcelData([]);
  };

  const handleChangeFile: UploadProps['onChange'] = useCallback(
    ({ fileList: newFileList }) => {
      if (!newFileList.length) return;

      const file = newFileList[0];
      if (file.size > 25 * 1024 * 1024) {
        showToast('failed', 'File upload phải nhỏ hơn 25MB');
        return;
      }
      if (file.name.split('.').pop() !== 'xlsx') {
        showToast('failed', 'File upload phải là file xlsx');
        return;
      }
      setCurrentFile(file);

      const reader = new FileReader();

      reader.onload = event => {
        const data = event.target?.result;
        if (data) {
          const workbook = XLSX.read(new Uint8Array(data as ArrayBuffer), {
            type: 'array',
          });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          const dataRows = jsonData.slice(1);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const formattedData = dataRows.map(row => {
            return {
              imei: row?.[0],
              device_sim_number: row?.[1],
              device_name: row?.[2],
            };
          });
          const uniqueData = formattedData.filter(
            (item, index, self) =>
              item.imei && index === self.findIndex(t => t.imei === item.imei),
          );
          setExcelData(uniqueData);
          const imeiArray = uniqueData
            .map(item => item.imei)
            .filter(
              (item, index, self) =>
                item && index === self.findIndex(t => t === item),
            );
          setValue('imei', imeiArray.join(';'), {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
          });
        }
      };
      if (file.originFileObj) {
        reader.readAsArrayBuffer(file.originFileObj);
      }
    },
    [currentFile],
  );

  const handleCreateDevice = useCallback(
    data => {
      const listImei = data.imei
        .split(';')
        .filter((item: string) => item.trim() !== '');
      const devices: ExcelData[] = [];
      listImei.forEach((imei: string) => {
        const device = excelData.find(item => item.imei === imei);
        if (device) {
          devices.push({
            imei: imei,
            device_sim_number: device.device_sim_number,
            device_name: device.device_name,
          });
        } else {
          devices.push({
            imei: imei,
            device_sim_number: '',
            device_name: '',
          });
        }
      });
      const payload = {
        devices,
        type: data.type,
        owner_id: selectedAccount?.id?.toString() || '',
      };
      createDevice.mutate(payload, {
        onSuccess: () => {
          onClose?.();
        },
      });
    },
    [currentFile, createDevice, excelData],
  );

  const handleDownloadTemplate = () => {
    // trigger download  file
    const a = document.createElement('a');
    a.href = '/src/assets/files/IMPORT_DEVICE_TEMPLATE.xlsx';
    a.download = 'IMPORT_DEVICE_TEMPLATE.xlsx';
    a.click();
  };

  useEffect(() => {
    if (!visible) {
      methods.reset(DEFAULT_VALUES);
      setCurrentFile(undefined);
    }

    return () => {
      methods.reset(DEFAULT_VALUES);
      setCurrentFile(undefined);
    };
  }, [visible, methods]);

  return (
    <Modal
      centered
      title={<Title>{t('business.title')}</Title>}
      open={visible}
      onCancel={onClose}
      onOk={onClose}
      footer={null}
      closeIcon={
        <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
      }
    >
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(handleCreateDevice)}>
          <Body>
            <AgencyAccountContainer>
              <AccountBreadCrumb breadcrumb={accountBreadcrumb} />
            </AgencyAccountContainer>
            <RRFieldDatePicker
              required
              id='activationDate'
              label='Ngày nhập'
              placeholder={dayjs().format(StandardFormats.USER_DATE + ' HH:mm')}
              dateFormat='DD/MM/YYYY HH:mm'
              className='w-full'
              disabled
              control={control}
              suffixIcon={images.Icon.CalendarSchedule}
            />
            <RRFieldSelect
              required
              id='type'
              label='Loại thiết bị'
              placeholder='Chọn loại thiết bị'
              control={control}
              options={DeviceOptions}
              errors={errors}
            />
            <RRFieldInput
              required
              id='imei'
              label='IMEI'
              control={control}
              placeholder='Nhập hoặc quét QR'
              errors={errors}
              suffixIcon={images.Icon.QrScan}
              disabled={!!currentFile}
            />
            <Row className='flex-row items-center justify-between'>
              <SecondaryButton
                size='small'
                iconPosition='left'
                icon={<Icon src={images.Icon.Excel} />}
                onClick={handleDownloadTemplate}
              >
                Biểu mẫu
              </SecondaryButton>

              <Upload
                name='file'
                beforeUpload={() => false}
                withCredentials={false}
                fileList={[]}
                onChange={handleChangeFile}
                showUploadList={false}
                multiple={false}
                maxCount={1}
              >
                <LinkButton
                  size='small'
                  iconPosition='left'
                  className='h-6'
                  icon={<Icon src={images.Icon.PaperClip} />}
                >
                  {t('business.attach')}
                </LinkButton>
              </Upload>
            </Row>
            {currentFile && (
              <Row className='items-start justify-start'>
                <AttachmentLabel>{`Đính kèm (1)`}</AttachmentLabel>
                <UploadContainer className=''>
                  <ThumbnailFile key={currentFile?.uid}>
                    <Icon src={images.Icon.Excel} />
                    <span className='truncate-1-line'>{currentFile.name}</span>
                    <Icon
                      className='cursor-pointer'
                      src={images.Icon.XClose}
                      onClick={handleRemoveFile}
                    />
                  </ThumbnailFile>
                </UploadContainer>
              </Row>
            )}

            <PrimaryButton
              htmlType='submit'
              disabled={createDevice.isPending || !formState.isValid}
              loading={createDevice.isPending}
            >
              {t('business.confirm')}
            </PrimaryButton>
          </Body>
        </form>
      </FormProvider>
    </Modal>
  );
};
const Title = tw.span`text-xl leading-[28px]`;
const Body = tw.div`flex flex-col gap-3 py-1`;
const Row = tw.div`flex flex-col gap-1`;
const AgencyAccountContainer = tw.div`w-full rounded-xl border-grey-100 border-[1px] border-solid box-border flex flex-row items-center justify-start py-2 px-3 gap-2 text-left text-sm text-text-secondary font-medium`;
const UploadContainer = tw.div`flex flex-1 flex-wrap gap-3 w-full`;
const ThumbnailFile = tw.div`flex flex-row items-center justify-center gap-1 truncate rounded-lg border border-grey-100 px-2 text-sm leading-[24px] text-grey-600 px-2 py-1 hover:bg-grey-50`;
const AttachmentLabel = tw.div`flex flex-row items-center justify-center gap-1 truncate px-2 text-sm leading-[24px] text-grey-600`;

export default AddDeviceModal;
