import React, { useCallback, useEffect, useState } from 'react';

import { Modal, message } from 'antd';
import { format } from 'date-fns';
import dayjs, { Dayjs } from 'dayjs';
import tw from 'tailwind-styled-components';

import { useDeviceLogs } from 'features/DeviceBusiness/hooks';
import { t } from 'i18next';
import { queryClient } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';
import {
  type ExportDeviceLogData,
  exportDeviceLogsToExcel,
  exportDeviceLogsToPDF,
} from 'utils/export';

import images from 'assets/images';
import { BodyMdExtend, FontSemibold } from 'assets/styles';
import { Icon } from 'components';
import { LinkButton, PrimaryButton, SecondaryButton } from 'components/Button';
import { RRSelect } from 'components/FormField';
import RRCalendar from 'components/RRCalendar';
import RRTable from 'components/RRTable';

type ExportType = 'excel' | 'pdf';

interface FilterState {
  startDate: Dayjs;
  endDate: Dayjs;
  status: string | null;
  page: number;
  pageSize: number;
}

const initialFilterState: FilterState = {
  startDate: dayjs().subtract(7, 'day'),
  endDate: dayjs(),
  status: null,
  page: 1,
  pageSize: 20,
};

export const DeviceFormLabel = {
  owner_name: 'Tài khoản',
  transport_department_id: 'Sở giao thông vận tải',
  transport_type: 'Loại hình',
  is_allow_data_transport: 'Truyền dữ liệu về TCĐB',
  device_name: 'Tên thiết bị',
  device_plate_number: 'Biển số',
  imei: 'IMEI',
  device_ccid: 'CCID',
  device_sim_number: 'Số Sim',
  device_category: 'Loại thiết bị',
  service_package: 'Gói dịch vụ',
  activated_at: 'Ngày kích hoạt',
  expired_at: 'Ngày hết hạn',
  firmware: 'Firmware',
  coordinates: 'Tọa độ',
  power: 'Nguồn',
  gsm_signal_strength: 'Chỉ số sóng GSM',
  engine: 'Động cơ',
  ignition_lock: 'Khóa điện',
  not_turn_off_the_ignition_time: 'Thời gian không tắt khóa',
  speed: 'Tốc độ',
  max_allowable_speed: 'Tốc độ giới hạn (km/h)',
  gps_time: 'Thời gian GPS',
  latest_update: 'Cập nhật mới nhất',
  device_pin: 'Mã PIN thiết bị',
  gprs_interval: 'Thời gian chờ GPRS (phút)',
  min_speed: 'Lọc tốc độ tối thiểu(km/h)',
  stop_time: 'Thời gian đỗ xe (giây)',
  timezone: 'Múi giờ',
  sensor_type: 'Tín hiệu động cơ',
  stop_distance: 'Khoảng cách cập nhật vi trí khi dừng(0-100m)',
};

const parseValueTypeDate = (key, value) => {
  if (key === 'activated_at' || key === 'expired_at') {
    return format(new Date(value), 'dd/MM/yyyy HH:mm');
  }
  return value;
};

const columns = [
  {
    title: t('deviceLog.stt'),
    dataIndex: 'key',
    key: 'key',
    height: '46px',
    width: 80,
    headerHeight: '36px',
    render: (text, record, index) => index + 1,
  },
  {
    title: t('deviceLog.account'),
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: t('deviceLog.activity'),
    dataIndex: 'action',
    key: 'action',
  },
  {
    title: t('deviceLog.ipAddress'),
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: t('deviceLog.creationDate'),
    dataIndex: 'created_at',
    key: 'created_at',
    render: text => {
      if (!text) return ''; // Handle undefined or null values
      const date = new Date(text);
      if (isNaN(date.getTime())) return ''; // Handle invalid date values
      return format(date, 'dd/MM/yyyy HH:mm');
    },
  },
  {
    title: t('deviceLog.imei'),
    dataIndex: 'device_imei',
    key: 'device_imei',
  },
  {
    title: 'Mô tả',
    dataIndex: 'device_changes',
    key: 'device_changes',
    render: data => {
      if (!data || Object.keys(data).length === 0) return null;
      return (
        <div>
          {Object.entries(data).map(([key, value]) => {
            const dataKey =
              DeviceFormLabel[key as keyof typeof DeviceFormLabel];
            const newValue = parseValueTypeDate(key, value?.[1]);
            const oldValue = parseValueTypeDate(key, value?.[0]);
            if (!dataKey) return null;
            return (
              <div key={dataKey}>
                <span className='text-grey-600'>{dataKey}:</span>{' '}
                <span>{`${oldValue} -> ${newValue}`}</span>
              </div>
            );
          })}
        </div>
      );
    },
  },
];

interface DeviceLogModalProps {
  deviceImei: string;
  isVisible: boolean;
  onClose: () => void;
}

const DeviceLogModal: React.FC<DeviceLogModalProps> = ({
  deviceImei,
  isVisible,
  onClose,
}) => {
  const [filters, setFilters] = useState<FilterState>(initialFilterState);

  const {
    data: deviceLogs,
    isLoading: logsLoading,
    refetch: refetchLogs,
  } = useDeviceLogs({
    enabled: !!deviceImei && isVisible,
    imei: deviceImei || '',
    params: {
      page: filters.page,
      per_page: filters.pageSize,
      created_at_from: filters.startDate.format('YYYY-MM-DD'),
      created_at_to: filters.endDate.format('YYYY-MM-DD'),
      action_type: filters.status || '',
    },
  });

  const { logs, pagination } = deviceLogs || {};

  const handleRefresh = useCallback(() => {
    setFilters(initialFilterState);
    setTimeout(() => {
      refetchLogs();
    }, 0);
  }, [refetchLogs]);

  const handleChangeDateRange = useCallback(
    (type?: 'start' | 'end', date?: Dayjs) => {
      if (!type || !date) return;
      setFilters(prev => {
        if (type === 'start') {
          return { ...prev, startDate: date };
        } else {
          return { ...prev, endDate: date };
        }
      });
    },
    [],
  );

  const handleChangeStatus = useCallback((status: string) => {
    setFilters(prev => ({ ...prev, status }));
  }, []);

  const handleSearch = useCallback(() => {
    setFilters(prev => ({ ...prev, page: 1 }));
    refetchLogs();
  }, []);

  const handleReset = useCallback(() => {
    setFilters(initialFilterState);
  }, []);

  const handleExport = useCallback(
    (type: ExportType) => () => {
      try {
        if (!logs || logs.length === 0) {
          message.warning('Không có dữ liệu để xuất');
          return;
        }

        const timestamp = format(new Date(), 'dd-MM-yyyy-HHmm');
        const filename = `device-logs-${deviceImei}-${timestamp}.${
          type === 'excel' ? 'xlsx' : 'pdf'
        }`;

        let result;
        if (type === 'excel') {
          result = exportDeviceLogsToExcel(
            logs as ExportDeviceLogData[],
            filename,
          );
        } else {
          result = exportDeviceLogsToPDF(
            logs as ExportDeviceLogData[],
            deviceImei,
            filename,
          );
        }

        if (result.success) {
          message.success(`Xuất ${type.toUpperCase()} thành công`);
        } else {
          message.error(`${type.toUpperCase()} export failed: ${result.error}`);
        }
      } catch (error) {
        message.error(`${type.toUpperCase()} export error: ${error}`);
      }
    },
    [logs, deviceImei],
  );

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
    setTimeout(() => {
      refetchLogs();
    }, 0);
  };

  const onPageSizeChange = (size: number) => {
    setFilters(prev => ({ ...prev, pageSize: size, page: 1 }));
    setTimeout(() => {
      refetchLogs();
    }, 0);
  };

  useEffect(() => {
    if (!isVisible && deviceImei) {
      queryClient.removeQueries({
        queryKey: queryKeys.devices.logs(deviceImei),
      });
      setFilters(initialFilterState);
    }
  }, [isVisible, deviceImei, filters.page, filters.pageSize]);

  return (
    <Modal
      className='login-history-modal'
      centered
      title={t('deviceLog.title')}
      open={isVisible}
      onOk={onClose}
      onCancel={onClose}
      width={920}
      footer={null}
      zIndex={1000}
      closeIcon={
        <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
      }
    >
      <div className='flex h-full flex-col'>
        <Grid className='mb-6 mt-5 grid-cols-5 gap-3'>
          <ColSpan2 className='flex flex-col justify-end'>
            <Label className={`${BodyMdExtend} ${FontSemibold}`}>
              {t('deviceLog.time')}
            </Label>
            <RRCalendar
              startDate={filters.startDate}
              endDate={filters.endDate}
              maxDateTime={new Date()}
              minDateTime={dayjs().subtract(30, 'day').toDate()}
              onSelect={handleChangeDateRange}
            />
          </ColSpan2>
          <ColSpan2 className='flex flex-col justify-end'>
            <RRSelect
              id='status'
              name='status'
              label={t('deviceLog.status')}
              options={[
                { value: 'activate', label: t('deviceLog.activate') },
                { value: 'update', label: t('deviceLog.update') },
                {
                  value: 'transfer',
                  label: t('deviceLog.transferManagement'),
                },
              ]}
              placeholder={t('deviceLog.select')}
              className='navio-form-field m-0 h-[44px]'
              onChange={handleChangeStatus}
              value={filters.status || ''}
            />
          </ColSpan2>
          <ColSpan1 className='flex flex-col justify-end'>
            <Label className={`${BodyMdExtend} ${FontSemibold}`}></Label>
            <Flex className='h-[44px] gap-2'>
              <PrimaryButton
                htmlType='submit'
                onClick={handleSearch}
                className='h-[44px] whitespace-nowrap'
              >
                {t('deviceLog.search')}
              </PrimaryButton>
              <SecondaryButton
                className='h-[44px] whitespace-nowrap'
                onClick={handleReset}
              >
                {t('deviceLog.reset')}
              </SecondaryButton>
            </Flex>
          </ColSpan1>
        </Grid>
        <Flex className='mb-3 justify-between'>
          <div className='text-[20px] font-bold'>{t('deviceLog.allData')}</div>
          <Flex className='gap-3'>
            <LinkButton
              onClick={handleRefresh}
              size='small'
              iconPosition='left'
              className='h-6'
              icon={<Icon src={images.Icon.ArrowRotate} />}
            >
              {t('deviceLog.refreshData')}
            </LinkButton>
            <SecondaryButton
              onClick={handleExport('excel')}
              size='small'
              iconPosition='left'
              icon={<Icon src={images.Icon.FileExcel} />}
            >
              {t('deviceLog.downloadExcel')}
            </SecondaryButton>
            <SecondaryButton
              onClick={handleExport('pdf')}
              size='small'
              iconPosition='left'
              icon={<Icon src={images.Icon.FilePdf} />}
            >
              {t('deviceLog.downloadPDF')}
            </SecondaryButton>
          </Flex>
        </Flex>
        <div className='h-full'>
          <RRTable
            className='device-log-table'
            tableLayout='auto'
            columns={columns}
            data={logs || []}
            total={pagination?.total_count}
            currentPage={filters.page}
            pageSize={filters.pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={onPageSizeChange}
            loading={logsLoading}
          />
        </div>
      </div>
    </Modal>
  );
};

export default DeviceLogModal;

const Label = tw.label`block text-left text-black-1000`;
const Grid = tw.div`grid`;
const ColSpan2 = tw.div`col-span-2`;
const ColSpan1 = tw.div`col-span-1`;
const Flex = tw.div`flex`;
