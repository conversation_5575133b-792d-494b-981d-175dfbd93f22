/* Custom scrollbar styling to prevent overlay */
.scrollbar-container {
  position: relative;
  margin: 0 auto;
}

.scrollbar-container .scrollbar-track {
  position: absolute !important;
  right: 0px;
  top: 0;
  background: transparent !important;
}

.scrollbar-container .scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3) !important;
  border-radius: 3px !important;
}

.padding-scroll-wrapper {
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box !important;
}
.padding-scroll-wrapper .animate-spin {
  margin: 8px auto;
}
