import React, { forwardRef } from 'react';

import { Scrollbars } from 'react-custom-scrollbars';

import './override.css';

interface RRScrollViewProps {
  className?: string;
  children: React.ReactNode;
  scrollbarSize?: number;
  scrollbarTrackStyle?: any;
  onScroll?: (e: React.UIEvent<HTMLElement, UIEvent>) => void;
}

const RRScrollView = forwardRef<Scrollbars, RRScrollViewProps>(
  (
    { className, children, scrollbarSize = 6, scrollbarTrackStyle, onScroll },
    ref,
  ) => {
    return (
      <Scrollbars
        ref={ref}
        onScroll={onScroll}
        className={`scrollbar-container ${className || ''}`}
        renderTrackVertical={({ style, ...props }) => (
          <div
            {...props}
            className='scrollbar-track'
            style={{
              ...style,
              width: scrollbarSize,
              right: 0,
              bottom: 0,
              top: 0,
              borderRadius: 3,
              ...(scrollbarTrackStyle || {}),
            }}
          />
        )}
        renderThumbVertical={({ style, ...props }) => (
          <div
            {...props}
            className='scrollbar-thumb'
            style={{
              ...style,

              borderRadius: 3,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
            }}
          />
        )}
        trackYProps={{
          style: { width: scrollbarSize },
        }}
      >
        <div className='padding-scroll-wrapper'>{children}</div>
      </Scrollbars>
    );
  },
);

export default RRScrollView;
