import React from 'react';

import { MFCircle } from 'react-map4d-map';
import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

import { Colors } from 'assets/styles';

interface RRCircleProps {
  radius?: number;
  fillColor?: string;
  fillOpacity?: number;
  strokeColor?: string;
  strokeWidth?: number;
  center: ILatLng;
}

const RRCircle: React.FC<RRCircleProps> = ({
  radius = 200,
  fillColor = Colors.grey[300],
  fillOpacity = 0.4,
  strokeColor = Colors.grey[300],
  strokeWidth = 1,
  center,
}) => {
  return (
    <MFCircle
      radius={radius}
      fillColor={fillColor}
      fillOpacity={fillOpacity}
      strokeColor={strokeColor}
      strokeWidth={strokeWidth}
      center={center}
    />
  );
};

export default RRCircle;
