import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

import { Popover, Slider, SliderSingleProps } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import tw from 'tailwind-styled-components';

import images from 'assets/images';

dayjs.extend(duration);
const PLAY_SPEEDS = [0.5, 1, 2, 4, 8];

type Props = {
  value: number;
  onChange: Dispatch<SetStateAction<number>>;
  className?: string;
  max?: number;
  min?: number;
  pathLength?: number; // Add pathLength to calculate proper intervals
  playSpeed: number;
  currentPoint: any;
  device: any;
  onPlaySpeedChange: (speed: number) => void;
};
const PlayBlack: React.FC<Props> = ({
  className,
  max = 100,
  min = 0,
  value,
  onChange,
  pathLength = 100,
  playSpeed,
  currentPoint,
  device,
  onPlaySpeedChange,
  ...props
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [speedPopoverOpen, setSpeedPopoverOpen] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startInterval = useCallback(() => {
    intervalRef.current = setInterval(() => {
      onChange(prevValue => {
        const nextValue = prevValue + playSpeed * 0.05;

        if (nextValue >= max) {
          setIsPlaying(false);
          return max;
        }

        return nextValue;
      });
    }, 100);
  }, [playSpeed, max, min, onChange, pathLength]);

  useEffect(() => {
    return () => {
      setIsPlaying(false);
      setSpeedPopoverOpen(false);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isPlaying && intervalRef.current) {
      clearInterval(intervalRef.current);
      startInterval();
    }
  }, [playSpeed, onPlaySpeedChange, isPlaying, startInterval]);

  const pause = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsPlaying(false);
  };

  const play = () => {
    if (!intervalRef.current) {
      startInterval();
    }
    setIsPlaying(true);
  };

  const handleSpeedChange = (speed: number) => {
    setSpeedPopoverOpen(false);
    onPlaySpeedChange(speed);
  };

  const formatterPlayBack: NonNullable<
    SliderSingleProps['tooltip']
  >['formatter'] = (value?: number) => {
    if (!value || !currentPoint) return '0%';

    return dayjs(currentPoint?.timestamp)
      .tz(device?.timezone || 'UTC')
      .format('HH:mm:ss');
  };

  const handlePressPlayPause = () => {
    if (isPlaying) {
      pause();
    } else {
      if (value >= max) {
        onChange(0);
      }
      play();
    }
  };

  return (
    <Container className={className}>
      <img
        src={isPlaying ? images.Icon.PauseCircle : images.Icon.VideoControl}
        className={`cursor-pointer`}
        onClick={handlePressPlayPause}
        title={isPlaying ? 'Pause' : 'Play'}
      />

      {/* Slider */}
      <Slider
        className='flex-1'
        {...props}
        min={min}
        step={0.1}
        max={max}
        value={value}
        onChange={onChange}
        tooltip={{ formatter: formatterPlayBack }}
      />

      {/* Speed Control with Popover */}
      <Popover
        content={
          <SpeedOptionsContainer>
            {PLAY_SPEEDS.map(speed => (
              <SpeedOption
                key={speed}
                onClick={() => handleSpeedChange(speed)}
                className={speed === playSpeed ? 'active' : ''}
              >
                {speed}x
              </SpeedOption>
            ))}
          </SpeedOptionsContainer>
        }
        trigger='click'
        open={speedPopoverOpen}
        onOpenChange={setSpeedPopoverOpen}
        placement='topRight'
      >
        <SpeedControl
          onClick={() => setSpeedPopoverOpen(!speedPopoverOpen)}
          title={`Speed: ${playSpeed}x`}
        >
          <SpeedText>{`${playSpeed}x`}</SpeedText>
        </SpeedControl>
      </Popover>
    </Container>
  );
};

export default PlayBlack;

const Container = tw.div`
  box-border flex flex-1 flex-row items-center rounded-lg shadow-panel px-3 py-2 mb-3
`;

const SpeedControl = tw.div`
  box-border flex size-8 flex-col items-center justify-center rounded-[999px] bg-brand-300 cursor-pointer
  hover:bg-brand-400 transition-colors
`;

const SpeedText = tw.div`
  m-0 text-[13px] font-semibold leading-[normal] text-white
`;

const SpeedOptionsContainer = tw.div`
  flex flex-col gap-1 min-w-[60px]
`;

const SpeedOption = tw.div`
  px-3 py-2 cursor-pointer rounded text-center text-sm font-medium
  hover:bg-brand-50 transition-colors
`;
