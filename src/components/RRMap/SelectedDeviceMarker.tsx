import React from 'react';

import { DeviceStatus } from 'constants/device';
import { MFMarker, MFPolyline } from 'react-map4d-map';
import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

import RRMaker from './RRMaker';
import { NormalizedLatLng } from './utils/mapUtils';

// Extended interface for path data with stop information
interface PathPoint {
  lat: number;
  lng: number;
  device_status?: 'run' | 'stop' | 'lost';
  stopCount?: number;
  timestamp?: number;
  type?: string;
  speed?: number;
  latitude?: number;
  longitude?: number;
  distance?: number;
  locating_information?: string;
  course?: number;
  status?: any;
}

// Component for rendering stop count markers
const StopCountMarker: React.FC<{ point: PathPoint }> = ({ point }) => {
  const stopCountIconView = `
    <div style="
      background-color: #E0E9EF;
      color: #2F6BFF;
      border-radius: 99px;
      min-width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      border: 1px solid #2F6BFF;
      padding: 2px 4px;
      user-select: none;
    ">${point.stopCount}</div>
  `;

  return (
    <MFMarker
      iconView={stopCountIconView}
      position={{ lat: point.lat, lng: point.lng }}
      anchor={[0.5, 0.5]}
      rotation={0}
    />
  );
};

interface SelectedDeviceMarkerProps {
  selectedDevice: any;
  currentMarkerPosition: NormalizedLatLng | null;
  currentMarkerStatus?: any;
  path?: (ILatLng | PathPoint)[];
  smoothedBearing: number;
  selectedPreviewDate?: any;
  currentViewData?: any;
}

const SelectedDeviceMarker: React.FC<SelectedDeviceMarkerProps> = ({
  selectedDevice,
  currentMarkerPosition,
  currentMarkerStatus,
  path,
  smoothedBearing,
  selectedPreviewDate,
  currentViewData,
}) => {
  // Use currentMarkerStatus if provided, fallback to selectedDevice.status
  const markerStatus =
    currentMarkerStatus ?? selectedDevice?.status ?? DeviceStatus.Running;

  if (!selectedDevice || !currentMarkerPosition) return null;

  const stopPoints = (currentViewData?.routes || [])
    .filter(route => route.type === 'stop')
    .map(item => ({
      stopCount: item.stopCount,
      lat: item.start.latitude,
      lng: item.start.longitude,
    }));

  if (path && path?.length > 0) {
    return (
      <>
        <RRMaker
          label={selectedDevice?.device_plate_number || ''}
          labelAnchor={[0.5, 2]}
          rotation={smoothedBearing}
          position={currentMarkerPosition}
          status={markerStatus}
          isAnimating={!!selectedPreviewDate}
        />
        <MFPolyline strokeColor={'#2F6BFF'} strokeWidth={6} path={path} />

        {/* Render stop count markers */}
        {stopPoints.map((point, index) => (
          <StopCountMarker
            key={`stop-${index}-${point.timestamp}`}
            point={point}
          />
        ))}
      </>
    );
  }

  return null;
};

export default SelectedDeviceMarker;
