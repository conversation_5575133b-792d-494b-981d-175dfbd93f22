import React, { useEffect, useMemo, useRef, useState } from 'react';

import { DeviceStatus } from 'constants/device';
import { MFMap, MFPolyline } from 'react-map4d-map';

import DeviceMapActions from 'components/DeviceHistory/DeviceMapActions';

import LostDeviceMarker from './LostDeviceMarker';
import PlayBlack from './PlayBack';
import RRCircle from './RRCircle';
import RRMaker from './RRMaker';
import SelectedDeviceMarker from './SelectedDeviceMarker';
import {
  NormalizedLatLng,
  calculateInterpolatedPosition,
  calculateRotationFromMovement,
  getCurrentPoint,
  normalizeLatLng,
} from './utils/mapUtils';

const RRMap: React.FC<any> = ({
  selectedPreviewDate,
  selectedDevice,
  radius,
  currentViewData,
  currentDeviceItemAction,
  onItemActionClick,
  groupsPoint,
  lostItems,
}) => {
  const [sliderValue, setSliderValue] = useState(0);
  const previousSliderRef = useRef(0);
  const [pos, setPos] = useState<{ lat: number; lng: number } | undefined>(
    undefined,
  );
  const [smoothedBearing, setSmoothedBearing] = useState(0);
  const [playSpeed, setPlaySpeed] = useState(1);
  const [mapInstance, setMapInstance] = useState<any>(null);

  const flatPoints = useMemo(() => {
    return groupsPoint.flatMap((group: any) => group);
  }, [groupsPoint]);

  const currentMarkerPosition = useMemo((): NormalizedLatLng | null => {
    if (!flatPoints || flatPoints.length === 0) {
      return selectedDevice
        ? {
            lat: selectedDevice?.gps?.latitude || 0,
            lng: selectedDevice?.gps?.longitude || 0,
          }
        : null;
    }

    if (selectedPreviewDate && flatPoints.length > 1) {
      return calculateInterpolatedPosition(flatPoints, sliderValue);
    }

    if (flatPoints[0]) {
      return normalizeLatLng(flatPoints[0]);
    }

    return {
      lat: selectedDevice?.gps?.latitude || 0,
      lng: selectedDevice?.gps?.longitude || 0,
    };
  }, [flatPoints, sliderValue, selectedPreviewDate, selectedDevice]);

  // Separate memoization for status to prevent unnecessary re-renders
  const currentPoint = useMemo(() => {
    if (!selectedPreviewDate || !flatPoints || flatPoints.length === 0) {
      return undefined;
    }
    return getCurrentPoint(flatPoints, sliderValue);
  }, [flatPoints, sliderValue, selectedPreviewDate, selectedDevice?.status]);

  const carRotation = useMemo(() => {
    if (!flatPoints || flatPoints.length < 2 || !selectedPreviewDate) return 0;

    const result = calculateRotationFromMovement(
      flatPoints,
      sliderValue,
      previousSliderRef.current,
    );
    previousSliderRef.current = sliderValue; // Update after calculation
    return result;
  }, [flatPoints, sliderValue, selectedPreviewDate]);

  useEffect(() => {
    // Sync rotation immediately with position for smooth movement
    setSmoothedBearing(carRotation);
  }, [carRotation]);

  useEffect(() => {
    const newPos = selectedDevice
      ? {
          lat: selectedDevice?.gps?.latitude || process.env.DEFAULT_LAT,
          lng: selectedDevice?.gps?.longitude || process.env.DEFAULT_LNG,
        }
      : { lat: process.env.DEFAULT_LAT, lng: process.env.DEFAULT_LNG };
    setPos(newPos);
  }, [mapInstance, selectedDevice]);

  useEffect(() => {
    if (selectedPreviewDate && flatPoints && flatPoints.length > 0) {
      setSliderValue(0);
      previousSliderRef.current = 0; // Reset previous slider tracking
    }
  }, [flatPoints, selectedPreviewDate]);

  useEffect(() => {
    if (!mapInstance || !selectedPreviewDate || !currentMarkerPosition) {
      return;
    }
    const timeoutId = setTimeout(() => {
      if (typeof mapInstance.panTo === 'function') {
        mapInstance.panTo({
          lat: currentMarkerPosition.lat,
          lng: currentMarkerPosition.lng,
        });
      }
    }, 150); // 150ms debounce delay

    return () => {
      if (!currentMarkerPosition?.lat || !currentMarkerPosition?.lng) {
        clearTimeout(timeoutId);
      }
    };
  }, [mapInstance, selectedPreviewDate, currentMarkerPosition]);

  const mapOptions = useMemo(() => {
    const baseOptions = {
      controls: true,
    };

    if (pos && pos.lat && pos.lng) {
      return {
        ...baseOptions,
        center: pos,
      };
    }

    return baseOptions;
  }, [currentMarkerPosition, pos]);

  if (!pos || !pos.lat || !pos.lng) return null;

  return (
    <>
      <div style={{ width: '100%', height: '100%' }}>
        <MFMap
          center={pos}
          options={mapOptions}
          zoom={15}
          accessKey={process.env.ACCESS_KEY ?? ''}
          version={'2.3'}
          onMapReady={map => {
            setMapInstance(map);
          }}
        >
          {flatPoints?.length === 0 && (
            <MFPolyline
              strokeColor={'#2F6BFF'}
              strokeOpacity={0}
              strokeWidth={6}
              path={[]}
            />
          )}

          {selectedPreviewDate && (
            <>
              {groupsPoint.map((point, index) => (
                <SelectedDeviceMarker
                  key={`selected-device-${index}`}
                  selectedDevice={selectedDevice}
                  currentMarkerPosition={currentMarkerPosition}
                  currentMarkerStatus={
                    currentPoint?.status || selectedDevice?.status
                  }
                  currentViewData={currentViewData}
                  path={point}
                  smoothedBearing={smoothedBearing}
                  selectedPreviewDate={selectedPreviewDate}
                />
              ))}
              {lostItems.map((item, index) => (
                <LostDeviceMarker key={`lost-device-${index}`} data={item} />
              ))}
            </>
          )}

          {/* {!selectedDevice &&
            (deviceList || [])
              .filter(item => item.gps?.latitude && item.gps?.longitude)
              .map(item => (
                <RRMaker
                  key={item.id}
                  label={item?.device_plate_number || ''}
                  labelAnchor={[0.5, 2]}
                  rotation={item.gps.half_of_course || 0}
                  position={{
                    lat: item.gps?.latitude || 0,
                    lng: item.gps?.longitude || 0,
                  }}
                  status={item.status || DeviceStatus.Running}
                />
              ))} */}
          {!selectedPreviewDate && selectedDevice && (
            <RRMaker
              label={selectedDevice?.device_plate_number || ''}
              labelAnchor={[0.5, 2]}
              rotation={selectedDevice?.gps?.half_of_course || 0}
              position={{
                lat: selectedDevice?.gps?.latitude || 0,
                lng: selectedDevice?.gps?.longitude || 0,
              }}
              status={selectedDevice?.status || DeviceStatus.Running}
            />
          )}
          {!!radius && (
            <RRCircle
              radius={radius * 600}
              fillOpacity={0.4}
              strokeWidth={1}
              center={pos}
            />
          )}
        </MFMap>
      </div>
      {selectedPreviewDate && (
        <PlayBlack
          max={100}
          value={sliderValue}
          onChange={setSliderValue}
          pathLength={flatPoints?.length || 0}
          playSpeed={playSpeed}
          currentPoint={currentPoint}
          device={selectedDevice}
          onPlaySpeedChange={setPlaySpeed}
          className='absolute inset-x-[200px] bottom-0 z-30 mx-auto w-full max-w-[30%] bg-white-1000'
        />
      )}
      {selectedDevice && (
        <div className='absolute bottom-0 right-3 top-3 z-20 flex h-fit w-[360px] flex-col'>
          <DeviceMapActions
            isRealTime={selectedPreviewDate}
            currentPoint={currentPoint}
            device={selectedDevice}
            onItemActionClick={onItemActionClick}
            selectedItem={currentDeviceItemAction}
          />
        </div>
      )}
    </>
  );
};

export default RRMap;
