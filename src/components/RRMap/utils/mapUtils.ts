import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

export interface NormalizedLatLng {
  lat: number;
  lng: number;
}

export const normalizeLatLng = (
  point: ILatLng | null | undefined,
): NormalizedLatLng => {
  if (!point) {
    return { lat: 0, lng: 0 };
  }

  if (Array.isArray(point)) {
    return {
      lat: Number.parseFloat(point[0] as any),
      lng: Number.parseFloat(point[1] as any),
    };
  }
  if ('lat' in point && 'lng' in point) {
    return {
      lat: Number.parseFloat(point.lat as any),
      lng: Number.parseFloat(point.lng as any),
    };
  }
  return {
    lat: Number.parseFloat((point as any).lat || 0),
    lng: Number.parseFloat((point as any).lng || 0),
  };
};

export const calculateBearing = (point1: ILatLng, point2: ILatLng): number => {
  const normalized1 = normalizeLatLng(point1);
  const normalized2 = normalizeLatLng(point2);

  const lat1Rad = (normalized1.lat * Math.PI) / 180;
  const lat2Rad = (normalized2.lat * Math.PI) / 180;
  const deltaLngRad = ((normalized2.lng - normalized1.lng) * Math.PI) / 180;

  const x = Math.sin(deltaLngRad) * Math.cos(lat2Rad);
  const y =
    Math.cos(lat1Rad) * Math.sin(lat2Rad) -
    Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLngRad);

  let bearing = Math.atan2(x, y);
  bearing = (bearing * 180) / Math.PI;
  bearing = (bearing + 360) % 360;

  return bearing;
};

export const calculateDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number,
): number => {
  return Math.sqrt(
    Math.pow((lat2 - lat1) * 111000, 2) +
      Math.pow((lng2 - lng1) * 111000 * Math.cos((lat1 * Math.PI) / 180), 2),
  );
};

export const calculateInterpolatedPosition = (
  path: ILatLng[],
  sliderValue: number,
): NormalizedLatLng | null => {
  if (!path || path.length === 0) return null;

  const maxIndex = path.length - 1;
  const exactIndex = (sliderValue / 100) * maxIndex;
  const currentIndex = Math.floor(exactIndex);
  const nextIndex = Math.min(currentIndex + 1, maxIndex);
  const interpolationFactor = exactIndex - currentIndex;

  const currentPoint = path[currentIndex];
  const nextPoint = path[nextIndex];

  if (!currentPoint || !nextPoint) return null;

  const normalizedCurrent = normalizeLatLng(currentPoint);
  const normalizedNext = normalizeLatLng(nextPoint);

  const interpolatedLat =
    normalizedCurrent.lat +
    (normalizedNext.lat - normalizedCurrent.lat) * interpolationFactor;
  const interpolatedLng =
    normalizedCurrent.lng +
    (normalizedNext.lng - normalizedCurrent.lng) * interpolationFactor;

  return { lat: interpolatedLat, lng: interpolatedLng };
};

export const getCurrentPoint = (path: ILatLng[], sliderValue: number): any => {
  if (!path || path.length === 0) return null;

  const maxIndex = path.length - 1;
  const exactIndex = (sliderValue / 100) * maxIndex;
  const currentIndex = Math.floor(exactIndex);
  const currentPoint = path[currentIndex];

  // Return status from current point without spreading entire object
  return currentPoint;
};

export const calculateRotationFromMovement = (
  path: ILatLng[],
  sliderValue: number,
  previousSliderValue: number = 0,
  minMovementDistance: number = 2,
): number => {
  if (!path || path.length < 2) return 0;

  // Calculate current and previous interpolated positions
  const currentPosition = calculateInterpolatedPosition(path, sliderValue);
  const previousPosition = calculateInterpolatedPosition(
    path,
    previousSliderValue,
  );

  if (!currentPosition || !previousPosition) return 0;

  // Calculate movement distance
  const movementDistance = calculateDistance(
    previousPosition.lat,
    previousPosition.lng,
    currentPosition.lat,
    currentPosition.lng,
  );

  // Only update rotation if there's significant movement
  if (movementDistance > minMovementDistance) {
    return calculateBearing(previousPosition, currentPosition);
  }

  // If movement is too small, look ahead for direction
  const maxIndex = path.length - 1;
  const exactIndex = (sliderValue / 100) * maxIndex;
  const currentIndex = Math.floor(exactIndex);
  const nextPoint = path[Math.min(currentIndex + 1, maxIndex)];

  if (nextPoint && currentIndex < maxIndex) {
    return calculateBearing(currentPosition, normalizeLatLng(nextPoint));
  }

  return 0;
};
