import React, { useMemo } from 'react';

import { MFMarker } from 'react-map4d-map';
import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

import images from 'assets/images';
import { Colors } from 'assets/styles';

export enum MarkerStatus {
  Running = 'running',
  Disconnect = 'disconnect',
  LowGPS = 'low gps',
  Stop = 'stop',
}

type RRMarkerProps = {
  label: string;
  position: ILatLng;
  labelAnchor?: [number, number];
};

const RRLocationPin: React.FC<RRMarkerProps> = ({ position, label = '' }) => {
  const pinIcon = useMemo(() => {
    return { width: 17, height: 24, url: images.Icon.LocationPin };
  }, []);

  const labelPanel = useMemo(() => {
    return `<div style="background-color: ${Colors.white[1000]}; padding: 4px; border-radius: 8px; font-size: 12px; font-weight: bold; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1)">${label}</div>`;
  }, [label]);

  return (
    <>
      <MFMarker icon={pinIcon} position={position} />
      {label && (
        <MFMarker iconView={labelPanel} position={position} anchor={[0, 1.8]} />
      )}
    </>
  );
};

export default RRLocationPin;
