import { useMemo } from 'react';

import { <PERSON><PERSON>ark<PERSON>, MFPoly<PERSON> } from 'react-map4d-map';

import { LostIconMarker } from './MarkerIcon';

// Dash pattern configurations
const DASH_PATTERNS = {
  solid: { type: 'solid' },
  dash: { type: 'segments', dashLength: 0.001, gapLength: 0.0005 }, // ⚠️ Fix: Increased for better visibility
  dot: { type: 'segments', dashLength: 0.0005, gapLength: 0.002 },
  dashDot: { type: 'segments', dashLength: 0.001, gapLength: 0.0005 },
  longDash: { type: 'segments', dashLength: 0.005, gapLength: 0.002 }, // ⚠️ Fix: Much longer for visibility
  custom: { type: 'segments', dashLength: 0.003, gapLength: 0.001 },
} as const;

// Function to interpolate point between two coordinates
const interpolatePoint = (start: any, end: any, ratio: number) => ({
  lat:
    parseFloat(start.lat) +
    (parseFloat(end.lat) - parseFloat(start.lat)) * ratio,
  lng:
    parseFloat(start.lng) +
    (parseFloat(end.lng) - parseFloat(start.lng)) * ratio,
});

// Function to create dash segments from path
const createDashSegments = (path: any[], pattern: any) => {
  if (!path || path.length < 2 || pattern.type === 'solid') {
    return [path];
  }

  const segments: any[][] = [];
  const { dashLength, gapLength } = pattern;

  for (let i = 0; i < path.length - 1; i++) {
    const start = path[i];
    const end = path[i + 1];

    // Calculate total distance of this segment
    const totalDistanceDeg = Math.sqrt(
      Math.pow(end.lat - start.lat, 2) + Math.pow(end.lng - start.lng, 2),
    );

    if (totalDistanceDeg === 0) {
      continue;
    }

    let currentDistance = 0;
    let isDash = true;
    let segmentCount = 0;

    while (currentDistance < totalDistanceDeg && segmentCount < 50) {
      // ⚠️ Fix: Add safety limit
      const segmentLength = isDash ? dashLength : gapLength;
      const nextDistance = Math.min(
        currentDistance + segmentLength,
        totalDistanceDeg,
      );

      if (isDash && nextDistance > currentDistance) {
        // Create dash segment
        const startPoint = interpolatePoint(
          start,
          end,
          currentDistance / totalDistanceDeg,
        );
        const endPoint = interpolatePoint(
          start,
          end,
          nextDistance / totalDistanceDeg,
        );

        segments.push([startPoint, endPoint]);
      }

      currentDistance = nextDistance;
      isDash = !isDash;
      segmentCount++;
    }
  }

  return segments;
};

const DashPolyline = ({ data }) => {
  const currentPattern = DASH_PATTERNS['dash'];

  const path = useMemo(
    () => [
      {
        lat: parseFloat(data?.start?.latitude),
        lng: parseFloat(data?.start?.longitude),
      },
      {
        lat: parseFloat(data?.end?.latitude),
        lng: parseFloat(data?.end?.longitude),
      },
    ],
    [],
  );

  // Create dash segments
  const dashSegments = useMemo(() => {
    if (!path || path.length === 0) {
      return [];
    }

    const segments = createDashSegments(path, currentPattern);
    return segments;
  }, [path, currentPattern]);

  if (!data?.start?.latitude && !data?.start?.longitude) {
    return null;
  }

  return (
    <>
      {/* Dash segments */}
      {dashSegments.map((segment, index) => (
        <MFPolyline
          key={`dash-segment-${index}`}
          strokeColor='#86888F'
          strokeWidth={6}
          strokeOpacity={1.0}
          path={segment}
        />
      ))}

      {/* Markers */}
      <MFMarker
        iconView={LostIconMarker}
        rotation={0}
        position={{
          lat: parseFloat(data?.start?.latitude),
          lng: parseFloat(data?.start?.longitude),
        }}
        anchor={[0.5, 0.5]}
      />
    </>
  );
};

export default DashPolyline;
