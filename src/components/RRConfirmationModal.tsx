import React from 'react';

import { Modal } from 'antd';
import tw from 'tailwind-styled-components';

import { FontBold, FontMedium, TitleMd } from 'assets/styles';
import { PrimaryButton, SecondaryButton } from 'components/Button';

interface RRConfirmationModalProps {
  visible: boolean;
  title: string;
  message: string;
  isSubmitting?: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

const RRConfirmationModal: React.FC<RRConfirmationModalProps> = ({
  visible,
  title,
  message,
  isSubmitting,
  onConfirm,
  onCancel,
  confirmText = 'Xác nhận',
  cancelText = 'Huỷ bỏ',
}) => {
  return (
    <Modal
      open={visible}
      centered
      closable={false}
      title={<Title className={`${TitleMd} ${FontBold}`}>{title}</Title>}
      onCancel={onCancel}
      footer={
        <Footer>
          <SecondaryButton
            className={`${FontMedium} w-full`}
            key='cancel'
            disabled={isSubmitting}
            onClick={onCancel}
          >
            {cancelText}
          </SecondaryButton>
          <PrimaryButton
            className={`${FontMedium} w-full`}
            key='confirm'
            disabled={isSubmitting}
            loading={isSubmitting}
            onClick={onConfirm}
          >
            {confirmText}
          </PrimaryButton>
        </Footer>
      }
    >
      <Body className=''>{message}</Body>
    </Modal>
  );
};

export default RRConfirmationModal;

const Body = tw.div`py-4`;
const Title = tw.div``;
const Footer = tw.div`flex gap-3`;
