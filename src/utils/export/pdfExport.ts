import { format } from 'date-fns';
import { jsPDF } from 'jspdf';

export interface ExportDeviceLogData {
  username: string;
  action: string;
  ip: string;
  created_at: string;
  device_imei: string;
}

// ✅ Helper function để normalize tiếng Việt
const normalizeVietnameseText = (text: string): string => {
  if (!text) return '';

  // Replace Vietnamese characters with closest ASCII equivalents
  const vietnameseMap: { [key: string]: string } = {
    // á, à, ả, ã, ạ, ă, ắ, ằ, ẳ, ẵ, ặ, â, ấ, ầ, ẩ, ẫ, ậ
    áàảãạăắằẳẵặâấầẩẫậ: 'a',
    ÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬ: 'A',
    // é, è, ẻ, ẽ, ẹ, ê, ế, ề, ể, ễ, ệ
    éèẻẽẹêếềểễệ: 'e',
    ÉÈẺẼẸÊẾỀỂỄỆ: 'E',
    // í, ì, ỉ, ĩ, ị
    íìỉĩị: 'i',
    ÍÌỈĨỊ: 'I',
    // ó, ò, ỏ, õ, ọ, ô, ố, ồ, ổ, ỗ, ộ, ơ, ớ, ờ, ở, ỡ, ợ
    óòỏõọôốồổỗộơớờởỡợ: 'o',
    ÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢ: 'O',
    // ú, ù, ủ, ũ, ụ, ư, ứ, ừ, ử, ữ, ự
    úùủũụưứừửữự: 'u',
    ÚÙỦŨỤƯỨỪỬỮỰ: 'U',
    // ý, ỳ, ỷ, ỹ, ỵ
    ýỳỷỹỵ: 'y',
    ÝỲỶỸỴ: 'Y',
    // đ
    đ: 'd',
    Đ: 'D',
  };

  let result = text;

  // Replace each Vietnamese character with ASCII equivalent
  Object.keys(vietnameseMap).forEach(vietnameseChars => {
    const replacement = vietnameseMap[vietnameseChars];
    for (const char of vietnameseChars) {
      result = result.replace(new RegExp(char, 'g'), replacement);
    }
  });

  return result;
};

// ✅ Safe text function cho jsPDF
const addSafeText = (
  doc: jsPDF,
  text: string,
  x: number,
  y: number,
  options?: any,
) => {
  try {
    // Try original text first
    doc.text(text, x, y, options);
  } catch (error) {
    // If fails, use normalized text
    console.warn('Using normalized text for:', text);
    const normalizedText = normalizeVietnameseText(text);
    doc.text(normalizedText, x, y, options);
  }
};

export const exportDeviceLogsToPDF = (
  logs: ExportDeviceLogData[],
  deviceImei?: string,
  filename?: string,
) => {
  try {
    console.log('🔧 Creating FIXED Excel-style PDF...');

    // Tạo PDF document A4 landscape
    const doc = new jsPDF('landscape', 'mm', 'a4');
    const pageWidth = doc.internal.pageSize.width; // ~297mm
    const pageHeight = doc.internal.pageSize.height; // ~210mm

    console.log('📄 Page dimensions:', pageWidth, 'x', pageHeight);

    // ============ HEADER SECTION ============
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    addSafeText(doc, 'DEVICE LOGS REPORT', pageWidth / 2, 20, {
      align: 'center',
    });

    let currentY = 35;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);

    if (deviceImei) {
      addSafeText(doc, `Device IMEI: ${deviceImei}`, 20, currentY);
      currentY += 7;
    }

    addSafeText(
      doc,
      `Export Date: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`,
      20,
      currentY,
    );
    addSafeText(doc, `Total Records: ${logs.length}`, 150, currentY);
    currentY += 15;

    // ============ TABLE SETUP ============
    const startX = 20;
    const startY = currentY;
    const colWidths = [25, 45, 60, 35, 45, 45]; // Total: 255mm (fits in 297mm)
    const tableWidth = colWidths.reduce((sum, width) => sum + width, 0);
    const rowHeight = 7;
    const headerHeight = 8;

    console.log('📊 Table dimensions:', tableWidth, 'mm wide');

    // ============ TABLE HEADER ============
    let y = startY;

    // Header background (Excel blue)
    doc.setFillColor(54, 96, 146);
    doc.rect(startX, y, tableWidth, headerHeight, 'F');

    // Header borders
    doc.setDrawColor(0, 0, 0);
    doc.setLineWidth(0.3);
    doc.rect(startX, y, tableWidth, headerHeight);

    // Header column separators
    let x = startX;
    for (let i = 0; i < colWidths.length - 1; i++) {
      x += colWidths[i];
      doc.line(x, y, x, y + headerHeight);
    }

    // Header text
    doc.setTextColor(255, 255, 255);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(9);

    const headers = [
      'STT',
      'Tai khoan',
      'Hoat dong',
      'Dia chi IP',
      'Ngay tao',
      'IMEI',
    ];
    x = startX;

    headers.forEach((header, index) => {
      const centerX = x + colWidths[index] / 2;
      addSafeText(doc, header, centerX, y + 5.5, { align: 'center' });
      x += colWidths[index];
    });

    y += headerHeight;

    // ============ TABLE DATA ============
    doc.setTextColor(0, 0, 0);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);

    logs.forEach((log, index) => {
      // Check for new page
      if (y > pageHeight - 30) {
        doc.addPage();
        y = 30;
      }

      // Alternating row background
      if (index % 2 === 1) {
        doc.setFillColor(245, 245, 245);
        doc.rect(startX, y, tableWidth, rowHeight, 'F');
      }

      // Row border
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.2);
      doc.rect(startX, y, tableWidth, rowHeight);

      // Column separators
      x = startX;
      for (let i = 0; i < colWidths.length - 1; i++) {
        x += colWidths[i];
        doc.line(x, y, x, y + rowHeight);
      }

      // Cell data
      const rowData = [
        (index + 1).toString(),
        (log.username || 'N/A').substring(0, 20), // Limit text length
        (log.action || 'N/A').substring(0, 30),
        log.ip || 'N/A',
        log.created_at
          ? format(new Date(log.created_at), 'dd/MM/yy HH:mm')
          : 'N/A',
        (log.device_imei || 'N/A').substring(0, 20),
      ];

      x = startX;
      rowData.forEach((data, colIndex) => {
        const textY = y + 4.5;

        if (colIndex === 0 || colIndex === 3 || colIndex === 4) {
          // Center align: STT, IP, Date
          const centerX = x + colWidths[colIndex] / 2;
          addSafeText(doc, data, centerX, textY, { align: 'center' });
        } else {
          // Left align: Username, Action, IMEI
          addSafeText(doc, data, x + 2, textY);
        }

        x += colWidths[colIndex];
      });

      y += rowHeight;
    });

    // ============ FOOTER ============
    const totalPages = (doc?.internal as any)?.getNumberOfPages() || 1;
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(7);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(100, 100, 100);

      addSafeText(doc, `Trang ${i}/${totalPages}`, 20, pageHeight - 10);

      addSafeText(
        doc,
        `Generated: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`,
        pageWidth - 60,
        pageHeight - 10,
      );
    }

    console.log('💾 Saving FIXED PDF...');

    const defaultFilename = `device-logs-fixed-${format(
      new Date(),
      'dd-MM-yyyy-HHmm',
    )}.pdf`;
    const exportFilename = filename || defaultFilename;

    doc.save(exportFilename);

    console.log('✅ FIXED PDF saved successfully:', exportFilename);

    return {
      success: true,
      filename: exportFilename,
    };
  } catch (error) {
    console.error('❌ Error exporting FIXED PDF:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};
