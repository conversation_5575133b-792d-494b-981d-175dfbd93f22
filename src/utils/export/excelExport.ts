import { format } from 'date-fns';
import { utils, writeFile } from 'xlsx';

export interface ExportDeviceLogData {
  username: string;
  action: string;
  ip: string;
  created_at: string;
  device_imei: string;
}

export const exportDeviceLogsToExcel = (
  logs: ExportDeviceLogData[],
  filename?: string,
) => {
  try {
    const excelData = logs.map((log, index) => ({
      STT: index + 1,
      'Tài khoản': log.username || '',
      'Hoạt động': log.action || '',
      'Địa chỉ IP': log.ip || '',
      'Ngày tạo': log.created_at
        ? format(new Date(log.created_at), 'dd/MM/yyyy HH:mm')
        : '',
      IMEI: log.device_imei || '',
    }));

    const worksheet = utils.json_to_sheet(excelData);

    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, '<PERSON>ce Logs');

    const colWidths = [
      { wch: 5 }, // STT
      { wch: 20 }, // T<PERSON><PERSON> kho<PERSON>n
      { wch: 25 }, // Hoạt động
      { wch: 15 }, // Địa chỉ IP
      { wch: 18 }, // Ngày tạo
      { wch: 20 }, // IMEI
    ];
    worksheet['!cols'] = colWidths;

    const defaultFilename = `device-logs-${format(
      new Date(),
      'dd-MM-yyyy-HHmm',
    )}.xlsx`;
    const exportFilename = filename || defaultFilename;

    writeFile(workbook, exportFilename);

    return {
      success: true,
      filename: exportFilename,
    };
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};
