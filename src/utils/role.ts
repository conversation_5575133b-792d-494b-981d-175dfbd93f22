import type { Account, AccountRole } from 'features/Profile/types';

import { CollapseItemData } from 'components/RRCollapseTable/types';

export const findRole = (
  roles: AccountRole[],
  level: CollapseItemData | Account,
): string => {
  if (!roles || roles?.length === 0) return '';

  // Handle Account type
  if ('role_type' in level) {
    return level.role_type || '';
  }

  // Handle CollapseItemData type
  return (
    roles.find(role => role.id?.toString() === level?.role_id?.toString())
      ?.role_type || ''
  );
};

export const getLevelAccount = (account: CollapseItemData | Account) => {
  if (account.role_type === 'end_user') {
    return 2;
  }
  if (account.parent_info) {
    return 1;
  }
  return 0;
};

export const addIsRoleType = (
  account: Account | null,
  roles: AccountRole[],
) => {
  if (!account) return {};
  const mapObjRole = {};
  roles.forEach(item => {
    if (item.role_type === account.role_type) {
      mapObjRole[`is_${item.role_type}`] = true;
    } else {
      mapObjRole[`is_${item.role_type}`] = false;
    }
  });
  return mapObjRole;
};
