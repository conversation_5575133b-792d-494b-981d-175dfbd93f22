import type { AccountRole } from 'features/Profile/types';

import { CollapseItemData } from 'components/RRCollapseTable';

export function classNames(...classes: unknown[]): string {
  return classes.filter(Boolean).join(' ');
}

export const filterAccountByRole = (payload: {
  accounts: CollapseItemData[];
  roles: AccountRole[];
  roleType: string[];
}) => {
  const { accounts, roles, roleType } = payload || {};
  const filterRoles = roles.filter(role => roleType.includes(role.role_type));

  const getAccounts = (
    data: CollapseItemData[],
    filterRoles: AccountRole[],
  ) => {
    return [...data].filter(account => {
      if (account?.children && account.children.length > 0) {
        account.children = getAccounts(account.children, filterRoles);
      }
      return filterRoles.find(
        role => role.id?.toString() === account.role_id?.toString(),
      );
    });
  };

  const newAccounts = getAccounts(accounts, filterRoles);

  for (let i = 0; i < newAccounts.length; i++) {
    if (newAccounts[i].children) {
      newAccounts[i].children = getAccounts(
        newAccounts[i].children!,
        filterRoles,
      );
    }
  }

  return newAccounts;
};

export const uppercaseString = (str: string) => {
  // uppercase all string
  if (!str) return '';
  return str.toUpperCase();
};

export function convertMeterToKm(meters = 0, hasUnit = false) {
  const formattedKm = parseFloat((Number(meters || 0) / 1000).toFixed(2));
  return hasUnit ? `${formattedKm.toLocaleString('en-US')} km` : formattedKm;
}
