import axios from 'axios';

import { TOKEN_KEY, getCookie, removeUserLoginCookie } from 'services/cookies';

import { ApiCallParams, ApiResponse, AppError } from './types';
import { getDomain, getEndpoint } from './utils';

const DOMAIN = getDomain();

export const callApi = async ({
  method,
  route,
  data,
  config,
  headersConfig,
}: ApiCallParams): Promise<ApiResponse> => {
  const { apiURL } = getEndpoint(route, DOMAIN);
  const token = getCookie(TOKEN_KEY);

  try {
    const response = await axios({
      method,
      url: apiURL,
      data,
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        ...headersConfig,
      },
      ...config,
    });

    return {
      success: true,
      request: { method, route, data, config },
      response,
    };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    if (error.status === 401) {
      if (route !== '/users/sign_in') {
        removeUserLoginCookie();
        window.location.href = '/login';
      }
    }

    return {
      success: false,
      request: { method, route, data, config },
      response: {},
      error: error as AppError,
    };
  }
};
