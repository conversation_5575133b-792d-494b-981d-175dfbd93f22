import { AxiosRequestConfig } from 'axios';

export interface ApiResponse {
  success: boolean;
  request: any;
  response: any;
  error?: any;
}

export interface AppError {
  message: string;
  code?: number;
}

export interface ApiCallParams {
  method:
    | 'get'
    | 'post'
    | 'put'
    | 'delete'
    | 'patch'
    | 'GET'
    | 'POST'
    | 'PUT'
    | 'DELETE'
    | 'PATCH';
  route: string;
  data?: any;
  params?: any; // Added missing params property
  config?: AxiosRequestConfig;
  headersConfig?: any;
}
