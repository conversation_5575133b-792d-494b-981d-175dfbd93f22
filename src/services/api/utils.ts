import qs from 'query-string';

import {
  DeploymentApiUrls,
  DeploymentDomains,
  ENVIRONMENTS,
} from './constants';

const inLocation = (paths: string[]) => {
  return paths.includes(location.host);
};

const findUrlByDomain = (OutputPaths: { [key: string]: string }) => {
  const envKeys = Object.keys(ENVIRONMENTS);
  let URL: string | undefined = undefined;
  envKeys.every(env => {
    if (inLocation(DeploymentDomains[ENVIRONMENTS[env]])) {
      URL = OutputPaths[ENVIRONMENTS[env]];
      return false;
    }
    return true;
  });
  return URL;
};

export const getRemoteURL = () => {
  return (
    findUrlByDomain(DeploymentApiUrls) ?? `${process.env.REMOTE_URL}/navio-api`
  );
};

const REMOTE_URL = getRemoteURL();

export const getDomain = () => {
  if (location.hostname === 'localhost') {
    return process.env.REMOTE_URL || '';
  }

  return REMOTE_URL || '';
};

export const getEndpoint = (route: string, domain: string) => {
  const endpoint = `${REMOTE_URL || domain}${route}`;

  return {
    apiURL: endpoint,
  };
};

export const qsStringifyUrl = (
  object: qs.ParsedUrl,
  options?: qs.StringifyOptions,
) => {
  const filteredQuery = Object.fromEntries(
    Object.entries(object.query).filter(([_, value]) => value !== ''),
  );
  const filteredObject = {
    ...object,
    query: filteredQuery,
  };
  return qs.stringifyUrl(filteredObject, {
    arrayFormat: 'comma',
    ...(options || {}),
  });
};
