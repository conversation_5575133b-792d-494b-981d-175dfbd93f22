// ====================================================================
// Query Keys Factory
// ====================================================================

export const queryKeys = {
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters: string) =>
      [...queryKeys.users.lists(), { filters }] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
  },
  roles: {
    all: ['roles'] as const,
    lists: () => [...queryKeys.roles.all, 'list'] as const,
    list: (filters: string) =>
      [...queryKeys.roles.lists(), { filters }] as const,
    details: () => [...queryKeys.roles.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.roles.details(), id] as const,
  },
  profile: {
    current: ['profile', 'current'] as const,
    detail: (id: string) => ['profile', 'detail', id] as const,
    deviceSelect: () => ['profile', 'deviceSelect'] as const,
    driverInfo: (imei: string) => ['profile', 'driverInfo', imei] as const,
  },
  devices: {
    all: ['devices'] as const,
    lists: () => [...queryKeys.devices.all, 'list'] as const,
    list: (filters: any) =>
      [...queryKeys.devices.lists(), { filters }] as const,
    detail: (id: string) => ['detail', id] as const,
    logs: (imei: string) => [...queryKeys.devices.all, 'logs', imei] as const,
    countByStatus: (filters: any) =>
      [...queryKeys.devices.all, 'countByStatus', filters] as const,
    byUser: (userId: string) =>
      [...queryKeys.devices.all, 'byUser', userId] as const,
    address: (imei: string) => ['address', imei] as const,
    selectors: () => [...queryKeys.devices.all, 'selectors'] as const,
  },
  sims: {
    all: ['sims'] as const,
    lists: () => [...queryKeys.sims.all, 'list'] as const,
    list: (filters: any) => [...queryKeys.sims.lists(), { filters }] as const,
    detail: (id: string) => ['sim', id] as const,
    servicePackages: () => [...queryKeys.sims.all, 'servicePackage'] as const,
  },

  accounts: {
    all: ['accounts'] as const,
    lists: () => [...queryKeys.accounts.all, 'list'] as const,
    list: (filters: string) =>
      [...queryKeys.accounts.lists(), { filters }] as const,
    detail: (id: string) => ['account', id] as const,
    child: () => [...queryKeys.accounts.all, 'child'] as const,
    childList: (parentId: string) =>
      [...queryKeys.accounts.child(), parentId] as const,
    search: ({ searchTerm, userId }: { searchTerm: string; userId: string }) =>
      [...queryKeys.accounts.all, 'search', { searchTerm, userId }] as const,
  },
  map: {
    all: ['map'] as const,
    geocode: () => [...queryKeys.map.all, 'geocode'] as const,
    geocodeReverse: (lat: string, lng: string) =>
      [...queryKeys.map.geocode(), 'reverse', { lat, lng }] as const,
    commands: (imei: string) =>
      [...queryKeys.map.all, 'commands', imei] as const,
    commandSamples: (imei: string) =>
      [...queryKeys.map.all, 'commandSamples', imei] as const,
    deviceListRealtime: (params: any) =>
      [...queryKeys.map.all, 'deviceListRealtime', params] as const,
    historyData: (imei: string, startDate: string, endDate: string) =>
      [...queryKeys.map.all, 'historyData', imei, startDate, endDate] as const,
    overviewData: (imei: string, startDate: string, endDate: string) =>
      [...queryKeys.map.all, 'overviewData', imei, startDate, endDate] as const,
    geofences: (imei: string) =>
      [...queryKeys.map.all, 'geofences', imei] as const,
    deviceList: (params: any) =>
      [...queryKeys.map.all, 'deviceList', params] as const,
  },
};
