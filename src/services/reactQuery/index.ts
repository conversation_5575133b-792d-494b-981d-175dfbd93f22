/**
 * React Query Configuration & Setup với Global Error Handling
 */
import { DefaultOptions, QueryClient } from '@tanstack/react-query';

import showToast from 'components/RRToastMessage/Toast';

import { callApi } from '../api/api';
import { ApiCallParams, ApiResponse } from '../api/types';

export const getErrorMsg = (error: any, defaultMsg = 'Đã có lỗi xảy ra') => {
  const { response, message } = error || {};

  if (response?.data?.message) {
    return response.data.message;
  }

  if (typeof response?.data?.errors === 'string') {
    return response.data || defaultMsg;
  }
  if (response?.data?.errors?.length > 0) {
    return response.data.errors?.[0] || defaultMsg;
  }

  const objectErrors = response?.data?.errors || response?.data || {};

  // response data is object key
  if (Object.entries(objectErrors || {}).length > 0) {
    const firstItem = Object.entries(objectErrors)[0];
    if (firstItem[0] === 'error') {
      return firstItem[1];
    }
    return `${firstItem[0]}: ${firstItem[1]}`;
  }

  return message || defaultMsg;
};

// ====================================================================
// Global Error Handler
// ====================================================================

const handleGlobalError = (error: any) => {
  // 401 - Unauthorized: Redirect to login
  if (error?.status === 401) {
    showToast('failed', 'Phiên đăng nhập đã hết hạn');
    // TODO: Add logout logic
    return;
  }

  // 403 - Forbidden
  if (error?.status === 403) {
    showToast('failed', 'Không có quyền thực hiện thao tác này');
    return;
  }

  // 404 - Not Found
  if (error?.status === 404) {
    showToast('failed', 'Không tìm thấy dữ liệu');
    return;
  }

  // 500+ - Server Error
  if (error?.status >= 500) {
    showToast('failed', 'Lỗi hệ thống, vui lòng thử lại sau');
    return;
  }

  // Network Error
  if (!error.response && error.message === 'Network Error') {
    showToast('failed', 'Lỗi mạng, vui lòng kiểm tra kết nối');
    return;
  }

  // Default error message
  const message = getErrorMsg(error);
  showToast('failed', message);
};

// ====================================================================
// Query Client Configuration
// ====================================================================

const queryConfig: DefaultOptions = {
  queries: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: true,
  },
  mutations: {
    retry: false,
    // ✅ Global error handler cho tất cả mutations
    onError: handleGlobalError,
  },
};

export const queryClient = new QueryClient({
  defaultOptions: queryConfig,
});

// ====================================================================
// Custom Hooks với Common Error Handling
// ====================================================================

/**
 * Custom mutation hook với error handling tùy chọn
 */
export const createMutationWithErrorHandling = <TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: {
    skipGlobalError?: boolean;
    customErrorHandler?: (error: any) => void;
  },
) => {
  return {
    mutationFn,
    onError: (error: any) => {
      if (options?.customErrorHandler) {
        options.customErrorHandler(error);
      } else if (!options?.skipGlobalError) {
        handleGlobalError(error);
      }
    },
  };
};

// ====================================================================
// API Functions
// ====================================================================

export const createApiQueryFn = <TData = any>(config: ApiCallParams) => {
  return async (): Promise<TData> => {
    const response: ApiResponse = await callApi(config);

    if (!response.success) {
      throw response.error;
    }

    return response.response.data;
  };
};

export const createApiMutationFn = <TData = any, TVariables = any>(
  configFn: (variables: TVariables) => ApiCallParams,
) => {
  return async (variables: TVariables): Promise<TData> => {
    const config = configFn(variables);
    const response: ApiResponse = await callApi(config);

    if (!response.success) {
      throw response.error;
    }

    return response.response.data;
  };
};

// ====================================================================
// Utility Functions
// ====================================================================

export const getErrorMessage = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'Đã có lỗi xảy ra';
};

export default queryClient;
