import Cookies from 'js-cookie';

export const FCM_TOKEN_KEY = 'fcm_token';
export const USER_ID_KEY = 'user_id';
export const TOKEN_KEY = 'token';

export const setCookie = (key: string, value: string) => {
  Cookies.set(key, value, {
    expires: 7,
    sameSite: 'strict',
    secure: true,
    // httpOnly: process.env.NODE_ENV === 'production',
  });
};

export const getCookie = (key: string) => {
  return Cookies.get(key);
};

export const removeCookie = (key: string) => {
  Cookies.remove(key, {
    sameSite: 'strict',
    secure: true,
    // httpOnly: process.env.NODE_ENV === 'production',
  });
};

export const setUserLoginCookie = ({ token, userId }) => {
  setCookie(TOKEN_KEY, token);
  setCookie(USER_ID_KEY, userId);
};

export const removeUserLoginCookie = () => {
  removeCookie(TOKEN_KEY);
  removeCookie(USER_ID_KEY);
};
