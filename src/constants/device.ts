export enum DeviceStatus {
  // Running
  Running = 'running',
  Run = 'run',
  // Bad GPS
  SlowConnection = 'bad_gps_signal',
  // Lost or Disconnect
  LostConnection = 'lost_connection',
  Disconnect = 'disconnect',
  LostGPS = 'lost',
  // Stopped
  Stopped = 'stop',
}

export const StatusColorsMapping = {
  [DeviceStatus.Running]: {
    backgroundColor: 'bg-green-10',
    color: 'text-green-200',
  },
  [DeviceStatus.Run]: {
    backgroundColor: 'bg-green-10',
    color: 'text-green-200',
  },
  [DeviceStatus.Stopped]: {
    backgroundColor: 'bg-yellow-10',
    color: 'text-yellow-300',
  },
  [DeviceStatus.LostConnection]: {
    backgroundColor: 'bg-red-10',
    color: 'text-red-200',
  },
  [DeviceStatus.Disconnect]: {
    backgroundColor: 'bg-red-10',
    color: 'text-red-200',
  },
  [DeviceStatus.SlowConnection]: {
    backgroundColor: 'bg-grey-50',
    color: 'text-black-1000',
  },
  [DeviceStatus.LostGPS]: {
    backgroundColor: 'bg-grey-50',
    color: 'text-black-1000',
  },
};

export const DeviceStatusLabel = {
  [DeviceStatus.Running]: 'Đang chạy',
  [DeviceStatus.Run]: 'Đang chạy',
  [DeviceStatus.LostConnection]: 'Mất kết nối',
  [DeviceStatus.SlowConnection]: 'GPS Kém',
  [DeviceStatus.Stopped]: 'Đang dừng',
  [DeviceStatus.Disconnect]: 'Mất kết nối',
  [DeviceStatus.LostGPS]: 'GPS Kém',
};

// Options
export const DeviceOptions = [
  { value: 'compass_pro', label: 'Compass Pro' },
  { value: 'sd_02', label: 'SD-02' },
];

export const VehicleTypeOptions = [
  {
    value: 'non_commercial_transport',
    label: 'Không kinh doanh vận tải',
  },
  {
    value: 'specialized_vehicle',
    label: 'Ô tô chuyên dụng',
  },
  {
    value: 'motorcycle',
    label: 'Xe mô tô',
  },
  {
    value: 'tractor_trailer',
    label: 'Ô tô kéo rơ moóc',
  },
  {
    value: 'towing_vehicle',
    label: 'Ô tô kéo xe khác',
  },
  {
    value: 'car',
    label: 'Ô tô con',
  },
  {
    value: 'concrete_mixer',
    label: 'Ô tô trộn vữa, trộn bê tông, xi téc',
  },
  {
    value: 'passenger_car',
    label: 'Xe ô tô con',
  },
  {
    value: 'passenger_under_30',
    label: 'Xe ô tô chở người đến 30 chỗ',
  },
  {
    value: 'truck_under_3_5t',
    label: 'Ô tô tải có trọng tải đến 3,5 tấn',
  },
  {
    value: 'passenger_over_30',
    label: 'Xe ô tô chở người trên 30 chỗ',
  },
  {
    value: 'truck_over_3_5t',
    label: 'Ô tô tải có trọng tải trên 3,5 tấn',
  },
  {
    value: 'bus',
    label: 'Ô tô buýt',
  },
  {
    value: 'semi_trailer',
    label: 'Ô tô đầu kéo kéo sơ mi rơ moóc',
  },
];

export const ServicePackageOptions = [{ value: 'car', label: 'Ô tô' }];

export const DepartmentOfTransportOptions = [
  // Thành phố trực thuộc trung ương
  { value: 'ha_noi', label: 'Hà Nội' },
  { value: 'tp_ho_chi_minh', label: 'TP Hồ Chí Minh' },
  { value: 'da_nang', label: 'Đà Nẵng' },
  { value: 'hai_phong', label: 'Hải Phòng' },
  { value: 'can_tho', label: 'Cần Thơ' },

  // Các tỉnh (theo thứ tự alphabetical)
  { value: 'an_giang', label: 'An Giang' },
  { value: 'ba_ria_vung_tau', label: 'Bà Rịa - Vũng Tàu' },
  { value: 'bac_giang', label: 'Bắc Giang' },
  { value: 'bac_kan', label: 'Bắc Kạn' },
  { value: 'bac_lieu', label: 'Bạc Liêu' },
  { value: 'bac_ninh', label: 'Bắc Ninh' },
  { value: 'ben_tre', label: 'Bến Tre' },
  { value: 'binh_dinh', label: 'Bình Định' },
  { value: 'binh_duong', label: 'Bình Dương' },
  { value: 'binh_phuoc', label: 'Bình Phước' },
  { value: 'binh_thuan', label: 'Bình Thuận' },
  { value: 'ca_mau', label: 'Cà Mau' },
  { value: 'cao_bang', label: 'Cao Bằng' },
  { value: 'dak_lak', label: 'Đắk Lắk' },
  { value: 'dak_nong', label: 'Đắk Nông' },
  { value: 'dien_bien', label: 'Điện Biên' },
  { value: 'dong_nai', label: 'Đồng Nai' },
  { value: 'dong_thap', label: 'Đồng Tháp' },
  { value: 'gia_lai', label: 'Gia Lai' },
  { value: 'ha_giang', label: 'Hà Giang' },
  { value: 'ha_nam', label: 'Hà Nam' },
  { value: 'ha_tinh', label: 'Hà Tĩnh' },
  { value: 'hai_duong', label: 'Hải Dương' },
  { value: 'hau_giang', label: 'Hậu Giang' },
  { value: 'hoa_binh', label: 'Hòa Bình' },
  { value: 'hung_yen', label: 'Hưng Yên' },
  { value: 'khanh_hoa', label: 'Khánh Hòa' },
  { value: 'kien_giang', label: 'Kiên Giang' },
  { value: 'kon_tum', label: 'Kon Tum' },
  { value: 'lai_chau', label: 'Lai Châu' },
  { value: 'lam_dong', label: 'Lâm Đồng' },
  { value: 'lang_son', label: 'Lạng Sơn' },
  { value: 'lao_cai', label: 'Lào Cai' },
  { value: 'long_an', label: 'Long An' },
  { value: 'nam_dinh', label: 'Nam Định' },
  { value: 'nghe_an', label: 'Nghệ An' },
  { value: 'ninh_binh', label: 'Ninh Bình' },
  { value: 'ninh_thuan', label: 'Ninh Thuận' },
  { value: 'phu_tho', label: 'Phú Thọ' },
  { value: 'phu_yen', label: 'Phú Yên' },
  { value: 'quang_binh', label: 'Quảng Bình' },
  { value: 'quang_nam', label: 'Quảng Nam' },
  { value: 'quang_ngai', label: 'Quảng Ngãi' },
  { value: 'quang_ninh', label: 'Quảng Ninh' },
  { value: 'quang_tri', label: 'Quảng Trị' },
  { value: 'soc_trang', label: 'Sóc Trăng' },
  { value: 'son_la', label: 'Sơn La' },
  { value: 'tay_ninh', label: 'Tây Ninh' },
  { value: 'thai_binh', label: 'Thái Bình' },
  { value: 'thai_nguyen', label: 'Thái Nguyên' },
  { value: 'thanh_hoa', label: 'Thanh Hóa' },
  { value: 'thua_thien_hue', label: 'Thừa Thiên Huế' },
  { value: 'tien_giang', label: 'Tiền Giang' },
  { value: 'tra_vinh', label: 'Trà Vinh' },
  { value: 'tuyen_quang', label: 'Tuyên Quang' },
  { value: 'vinh_long', label: 'Vĩnh Long' },
  { value: 'vinh_phuc', label: 'Vĩnh Phúc' },
  { value: 'yen_bai', label: 'Yên Bái' },
];

export const DeviceStatusOption = [
  { value: 'run', label: DeviceStatusLabel[DeviceStatus.Running] },
  {
    value: 'lost_connection',
    label: DeviceStatusLabel[DeviceStatus.LostConnection],
  },
  {
    value: 'bad_gps_signal',
    label: DeviceStatusLabel[DeviceStatus.SlowConnection],
  },
  { value: 'stop', label: DeviceStatusLabel[DeviceStatus.Stopped] },
];

export const EngineSignalOptions = [
  {
    value: 'electric',
    label: 'Khoá điện',
  },
  {
    value: 'mechanical',
    label: 'Động cơ',
  },
];

export const TimezoneOptions = [
  // GMT-12 to GMT-11
  { label: '(GMT-12:00) International Date Line West', value: 'Etc/GMT+12' },
  { label: '(GMT-11:00) Midway Island, Samoa', value: 'Pacific/Midway' },
  { label: '(GMT-11:00) American Samoa', value: 'Pacific/Pago_Pago' },

  // GMT-10
  { label: '(GMT-10:00) Hawaii', value: 'Pacific/Honolulu' },
  { label: '(GMT-10:00) Cook Islands', value: 'Pacific/Rarotonga' },

  // GMT-09:30
  { label: '(GMT-09:30) Marquesas Islands', value: 'Pacific/Marquesas' },

  // GMT-09
  { label: '(GMT-09:00) Alaska', value: 'America/Anchorage' },
  { label: '(GMT-09:00) Gambier Islands', value: 'Pacific/Gambier' },

  // GMT-08
  {
    label: '(GMT-08:00) Pacific Time (US & Canada)',
    value: 'America/Los_Angeles',
  },
  { label: '(GMT-08:00) Tijuana, Baja California', value: 'America/Tijuana' },
  { label: '(GMT-08:00) Vancouver', value: 'America/Vancouver' },

  // GMT-07
  { label: '(GMT-07:00) Mountain Time (US & Canada)', value: 'America/Denver' },
  { label: '(GMT-07:00) Arizona', value: 'America/Phoenix' },
  {
    label: '(GMT-07:00) Chihuahua, La Paz, Mazatlan',
    value: 'America/Chihuahua',
  },

  // GMT-06
  { label: '(GMT-06:00) Central Time (US & Canada)', value: 'America/Chicago' },
  {
    label: '(GMT-06:00) Guadalajara, Mexico City, Monterrey',
    value: 'America/Mexico_City',
  },
  { label: '(GMT-06:00) Saskatchewan', value: 'America/Regina' },
  { label: '(GMT-06:00) Central America', value: 'America/Guatemala' },

  // GMT-05
  {
    label: '(GMT-05:00) Eastern Time (US & Canada)',
    value: 'America/New_York',
  },
  {
    label: '(GMT-05:00) Indiana (East)',
    value: 'America/Indiana/Indianapolis',
  },
  { label: '(GMT-05:00) Bogota, Lima, Quito', value: 'America/Lima' },
  { label: '(GMT-05:00) Cuba', value: 'America/Havana' },

  // GMT-04
  { label: '(GMT-04:00) Atlantic Time (Canada)', value: 'America/Halifax' },
  { label: '(GMT-04:00) Caracas', value: 'America/Caracas' },
  { label: '(GMT-04:00) Santiago', value: 'America/Santiago' },
  { label: '(GMT-04:00) La Paz', value: 'America/La_Paz' },

  // GMT-03:30
  { label: '(GMT-03:30) Newfoundland', value: 'America/St_Johns' },

  // GMT-03
  { label: '(GMT-03:00) Brasilia', value: 'America/Sao_Paulo' },
  {
    label: '(GMT-03:00) Buenos Aires, Georgetown',
    value: 'America/Argentina/Buenos_Aires',
  },
  { label: '(GMT-03:00) Greenland', value: 'America/Godthab' },
  { label: '(GMT-03:00) Montevideo', value: 'America/Montevideo' },

  // GMT-02
  { label: '(GMT-02:00) Mid-Atlantic', value: 'America/Noronha' },
  { label: '(GMT-02:00) South Georgia', value: 'Atlantic/South_Georgia' },

  // GMT-01
  { label: '(GMT-01:00) Azores', value: 'Atlantic/Azores' },
  { label: '(GMT-01:00) Cape Verde Is.', value: 'Atlantic/Cape_Verde' },

  // GMT+00
  { label: '(GMT+00:00) Greenwich Mean Time', value: 'GMT' },
  {
    label: '(GMT+00:00) London, Edinburgh, Cardiff, Dublin',
    value: 'Europe/London',
  },
  {
    label: '(GMT+00:00) Casablanca, Monrovia, Reykjavik',
    value: 'Africa/Casablanca',
  },

  // GMT+01
  {
    label: '(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna',
    value: 'Europe/Berlin',
  },
  {
    label: '(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague',
    value: 'Europe/Belgrade',
  },
  {
    label: '(GMT+01:00) Brussels, Copenhagen, Madrid, Paris',
    value: 'Europe/Paris',
  },
  { label: '(GMT+01:00) West Central Africa', value: 'Africa/Lagos' },

  // GMT+02
  { label: '(GMT+02:00) Athens, Bucharest, Istanbul', value: 'Europe/Athens' },
  { label: '(GMT+02:00) Cairo', value: 'Africa/Cairo' },
  {
    label: '(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius',
    value: 'Europe/Helsinki',
  },
  { label: '(GMT+02:00) Jerusalem', value: 'Asia/Jerusalem' },
  { label: '(GMT+02:00) Harare, Pretoria', value: 'Africa/Johannesburg' },

  // GMT+03
  {
    label: '(GMT+03:00) Moscow, St. Petersburg, Volgograd',
    value: 'Europe/Moscow',
  },
  { label: '(GMT+03:00) Kuwait, Riyadh', value: 'Asia/Kuwait' },
  { label: '(GMT+03:00) Baghdad', value: 'Asia/Baghdad' },
  { label: '(GMT+03:00) Nairobi', value: 'Africa/Nairobi' },

  // GMT+03:30
  { label: '(GMT+03:30) Tehran', value: 'Asia/Tehran' },

  // GMT+04
  { label: '(GMT+04:00) Abu Dhabi, Muscat', value: 'Asia/Dubai' },
  { label: '(GMT+04:00) Baku', value: 'Asia/Baku' },
  { label: '(GMT+04:00) Yerevan', value: 'Asia/Yerevan' },
  { label: '(GMT+04:00) Tbilisi', value: 'Asia/Tbilisi' },

  // GMT+04:30
  { label: '(GMT+04:30) Kabul', value: 'Asia/Kabul' },

  // GMT+05
  { label: '(GMT+05:00) Ekaterinburg', value: 'Asia/Yekaterinburg' },
  { label: '(GMT+05:00) Islamabad, Karachi, Tashkent', value: 'Asia/Karachi' },

  // GMT+05:30
  {
    label: '(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi',
    value: 'Asia/Kolkata',
  },
  { label: '(GMT+05:30) Sri Jayawardenepura', value: 'Asia/Colombo' },

  // GMT+05:45
  { label: '(GMT+05:45) Kathmandu', value: 'Asia/Kathmandu' },

  // GMT+06
  { label: '(GMT+06:00) Almaty, Novosibirsk', value: 'Asia/Almaty' },
  { label: '(GMT+06:00) Astana, Dhaka', value: 'Asia/Dhaka' },

  // GMT+06:30
  { label: '(GMT+06:30) Yangon (Rangoon)', value: 'Asia/Yangon' },

  // GMT+07
  { label: '(GMT+07:00) Bangkok, Hanoi, Jakarta', value: 'Asia/Bangkok' },
  { label: '(GMT+07:00) Vietnam', value: 'Asia/Ho_Chi_Minh' },
  { label: '(GMT+07:00) Krasnoyarsk', value: 'Asia/Krasnoyarsk' },
  { label: '(GMT+07:00) Cambodia', value: 'Asia/Phnom_Penh' },
  { label: '(GMT+07:00) Laos', value: 'Asia/Vientiane' },

  // GMT+08
  {
    label: '(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi',
    value: 'Asia/Shanghai',
  },
  { label: '(GMT+08:00) Kuala Lumpur, Singapore', value: 'Asia/Singapore' },
  { label: '(GMT+08:00) Irkutsk, Ulaan Bataar', value: 'Asia/Irkutsk' },
  { label: '(GMT+08:00) Perth', value: 'Australia/Perth' },
  { label: '(GMT+08:00) Taipei', value: 'Asia/Taipei' },
  { label: '(GMT+08:00) Manila', value: 'Asia/Manila' },

  // GMT+08:45
  { label: '(GMT+08:45) Eucla', value: 'Australia/Eucla' },

  // GMT+09
  { label: '(GMT+09:00) Osaka, Sapporo, Tokyo', value: 'Asia/Tokyo' },
  { label: '(GMT+09:00) Seoul', value: 'Asia/Seoul' },
  { label: '(GMT+09:00) Yakutsk', value: 'Asia/Yakutsk' },

  // GMT+09:30
  { label: '(GMT+09:30) Adelaide', value: 'Australia/Adelaide' },
  { label: '(GMT+09:30) Darwin', value: 'Australia/Darwin' },

  // GMT+10
  { label: '(GMT+10:00) Eastern Australia', value: 'Australia/Sydney' },
  { label: '(GMT+10:00) Melbourne', value: 'Australia/Melbourne' },
  { label: '(GMT+10:00) Brisbane', value: 'Australia/Brisbane' },
  { label: '(GMT+10:00) Hobart', value: 'Australia/Hobart' },
  { label: '(GMT+10:00) Vladivostok', value: 'Asia/Vladivostok' },
  { label: '(GMT+10:00) Guam, Port Moresby', value: 'Pacific/Guam' },

  // GMT+10:30
  { label: '(GMT+10:30) Lord Howe Island', value: 'Australia/Lord_Howe' },

  // GMT+11
  {
    label: '(GMT+11:00) Magadan, Solomon Is., New Caledonia',
    value: 'Pacific/Noumea',
  },
  { label: '(GMT+11:00) Norfolk Island', value: 'Pacific/Norfolk' },

  // GMT+12
  { label: '(GMT+12:00) Auckland, Wellington', value: 'Pacific/Auckland' },
  { label: '(GMT+12:00) Fiji, Kamchatka, Marshall Is.', value: 'Pacific/Fiji' },

  // GMT+12:45
  { label: '(GMT+12:45) Chatham Islands', value: 'Pacific/Chatham' },

  // GMT+13
  { label: "(GMT+13:00) Nuku'alofa", value: 'Pacific/Tongatapu' },
  { label: '(GMT+13:00) Samoa', value: 'Pacific/Apia' },

  // GMT+14
  { label: '(GMT+14:00) Line Islands', value: 'Pacific/Kiritimati' },
];
