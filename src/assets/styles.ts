// Font Weight
export const FontRegular = `font-normal`;
export const FontMedium = `font-medium`;
export const FontSemibold = `font-semibold`;
export const FontBold = `font-bold`;

// Font Size Presets
export const H1 = `text-[64px] leading-[80px]`;
export const H2 = `text-[48px] leading-[56px]`;
export const H3 = `text-[40px] leading-[48px]`;
export const H4 = `text-[32px] leading-10`;
export const TitleLg = `text-2xl`;
export const TitleMd = `text-xl`;
export const BodyXl = `text-[18px] leading-[26px]`;
export const BodyLg = `text-base`;
export const BodyMdExtend = `text-[14px] leading-6`;
export const BodyMdBase = `text-[14px] leading-5`;
export const BodySm = `text-xs`;
export const ButtonSm = `text-[14px] leading-6 ${FontMedium}`;
export const ButtonBase = `text-[16px] leading-6 ${FontMedium}`;
export const InputSm = `text-[12px] leading-6 ${FontRegular}`;
export const InputBase = `text-[14px] leading-6 ${FontRegular}`;

export const Colors = {
  transparent: 'transparent',
  brand: {
    950: '#787B05',
    900: '#858506',
    800: '#919107',
    700: '#A1A107',
    600: '#B2B208',
    500: '#C4C409',
    400: '#D9D90A',
    300: '#F0F00B',
    200: '#FAFA05',
    100: '#FFFF00',
    50: '#FFFF8F',
  },
  brandAlphaPending: {
    alpha30: 'rgba(240, 240, 8, 0.3)', // 30%
    alpha20: 'rgba(240, 240, 8, 0.2)', // 20%
    alpha10: 'rgba(240, 240, 8, 0.1)', // 10%
  },
  grey: {
    950: '#09090A',
    900: '#16171A',
    800: '#252529',
    700: '#3D3E42',
    600: '#54565C',
    500: '#6E6F75',
    400: '#86888F',
    300: '#A2A3A8',
    200: '#BABCC2',
    100: '#E1E3EB',
    50: '#F0F1F5',
  },
  black: {
    1000: '#000000',
    900: 'rgba(0, 0, 0, 0.9)', // 90%
    800: 'rgba(0, 0, 0, 0.8)', // 80%
    700: 'rgba(0, 0, 0, 0.7)', // 70%
    600: 'rgba(0, 0, 0, 0.6)', // 60%
    500: 'rgba(0, 0, 0, 0.5)', // 50%
    400: 'rgba(0, 0, 0, 0.4)', // 40%
    300: 'rgba(0, 0, 0, 0.3)', // 30%
    200: 'rgba(0, 0, 0, 0.2)', // 20%
    100: 'rgba(0, 0, 0, 0.1)', // 10%
    50: 'rgba(0, 0, 0, 0.05)', // 5%
  },
  white: {
    1000: '#ffffff',
    500: '#FCFCFC',
  },
  red: {
    300: '#CC2525',
    200: '#E52929',
    100: '#FF2E2E',
    10: '#FFE5E5',
    alpha20: 'rgba(229, 41, 41, 0.2)', // 20%
    alpha10: 'rgba(229, 41, 41, 0.1)', // 10%
  },
  green: {
    300: '#0D7D3E',
    200: '#00964B',
    100: '#00B058',
    50: '#30B42D',
    10: '#DCFAEB',
    alpha20: 'rgba(0, 150, 72, 0.2)', // 20%
    alpha10: 'rgba(0, 150, 72, 0.1)', // 10%
  },
  blue: {
    300: '#2A60E5',
    200: '#2F6BFF',
    100: '#5EBDFF',
    50: '#00A1E4',
    10: '#E0E9FF',
    alpha20: 'rgba(39, 107, 255, 0.2)', // 20%
    alpha10: 'rgba(39, 107, 255, 0.1)', // 10%
  },
  yellow: {
    300: '#CC9D00',
    200: '#E5B100',
    100: '#FFC500',
    10: '#FFF8E0',
    alpha20: 'rgba(229, 177, 0, 0.2)', // 20%
    alpha10: 'rgba(229, 177, 0, 0.1)', // 10%
  },
  orange: {
    300: '#CC7029',
    200: '#E57E2E',
    100: '#FFC033',
    10: '#FFEE80',
    alpha20: 'rgba(229, 126, 46, 0.2)', // 20%
    alpha10: 'rgba(229, 126, 46, 0.1)', // 10%
  },
};

export const antdThemeConfig = {
  components: {
    Menu: {
      itemHoverBg: Colors.brand[300],
      itemSelectedBg: Colors.brand[300],
      itemSelectedColor: Colors.black[1000],
    },
    Input: {
      activeBg: Colors.grey[50],
      activeShadow: 'none',
      hoverBg: Colors.grey[50],
    },
    Slider: {
      controlSize: 30,
      dotActiveBorderColor: Colors.white[1000],
      dotBorderColor: Colors.white[1000],
    },
    Tabs: {
      inkBarColor: Colors.white[1000],
      colorBorder: Colors.white[1000],
      colorBgContainer: Colors.grey[50],
      itemHoverColor: Colors.black[1000],
    },
    Select: {
      activeBorderColor: Colors.grey[50],
      hoverBorderColor: Colors.grey[50],
      clearBg: Colors.grey[50],
      optionActiveBg: Colors.grey[50],
      optionSelectedBg: Colors.grey[50],
      selectorBg: Colors.grey[50],
    },
    DatePicker: {
      activeBorderColor: Colors.grey[50],
      hoverBorderColor: Colors.grey[50],
    },
    Radio: {
      buttonBg: Colors.grey[50],
      buttonSolidCheckedActiveBg: Colors.white[1000],
      buttonSolidCheckedColor: Colors.grey[50],
      buttonSolidCheckedBg: Colors.white[1000],
      colorBorder: Colors.grey[50],
      colorBgContainer: Colors.grey[50],
      colorPrimary: Colors.grey[50],
      colorPrimaryHover: Colors.white[1000],
      buttonSolidCheckedHoverBg: Colors.white[1000],
      colorPrimaryActive: Colors.grey[50],
    },
    Pagination: {
      itemActiveBg: Colors.brand[300],
      itemBg: Colors.white[1000],
      colorPrimary: Colors.black[1000],
      colorPrimaryHover: 'none',
      colorBgTextActive: Colors.brand[300],
      colorBgContainer: Colors.brand[300],
      lineWidth: 0,
    },
    Calendar: {
      colorTextDisabled: Colors.grey[200],
    },
  },
  token: {
    fontFamily: 'Inter',
  },
};
