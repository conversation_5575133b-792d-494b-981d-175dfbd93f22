const Icon = () => {
  return (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.84754 4.0275C7.84754 2.83921 8.81047 1.875 10 1.875C11.1896 1.875 12.1525 2.83921 12.1525 4.0275V4.31538C13.8987 4.89926 15.1567 6.54795 15.1567 8.49V10.8208C15.1567 11.0966 15.2666 11.3618 15.462 11.5572L15.9962 12.0914C16.4257 12.521 16.6675 13.1041 16.6675 13.7117C16.6675 14.9268 15.6827 15.9117 14.4675 15.9117H12.5148C12.3568 17.1599 11.2913 18.125 10 18.125C8.70876 18.125 7.64291 17.1597 7.48517 15.9117H5.53337C4.3182 15.9117 3.33337 14.9268 3.33337 13.7117C3.33337 13.1041 3.57519 12.521 4.00477 12.0914L4.53893 11.5572C4.73435 11.3618 4.84421 11.0966 4.84421 10.8208V8.49C4.84421 6.54759 6.10228 4.8997 7.84754 4.31569V4.0275ZM9.09754 4.09159C9.14651 4.08998 9.19568 4.08917 9.24504 4.08917H10.7559C10.805 4.08917 10.8538 4.08997 10.9025 4.09156V4.0275C10.9025 3.52912 10.4988 3.125 10 3.125C9.50127 3.125 9.09754 3.52912 9.09754 4.0275V4.09159ZM8.75549 15.9117C8.89805 16.4654 9.40099 16.875 10 16.875C10.5988 16.875 11.1017 16.4658 11.2445 15.9117H8.75549ZM11.8775 14.6617H5.53337C5.00855 14.6617 4.58337 14.2365 4.58337 13.7117C4.58337 13.4359 4.69323 13.1707 4.88865 12.9753L5.42282 12.4411C5.85239 12.0115 6.09421 11.4284 6.09421 10.8208V8.49C6.09421 6.74943 7.50513 5.33917 9.24504 5.33917H10.7559C12.4964 5.33917 13.9067 6.75009 13.9067 8.49V10.8208C13.9067 11.4284 14.1485 12.0115 14.5781 12.4411L15.1123 12.9753C15.3077 13.1707 15.4175 13.4359 15.4175 13.7117C15.4175 14.2365 14.9924 14.6617 14.4675 14.6617H11.9426C11.9318 14.6611 11.921 14.6608 11.91 14.6608C11.8991 14.6608 11.8883 14.6611 11.8775 14.6617Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Icon;
