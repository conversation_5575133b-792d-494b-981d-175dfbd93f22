const Icon = () => {
  return (
    <svg
      width='20'
      height='18'
      viewBox='0 0 20 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.12056 8.55806C1.36464 8.31398 1.76036 8.31398 2.00444 8.55806L3.4375 9.99112L4.87056 8.55806C5.11464 8.31398 5.51036 8.31398 5.75444 8.55806C5.99852 8.80214 5.99852 9.19786 5.75444 9.44194L3.87944 11.3169C3.63536 11.561 3.23964 11.561 2.99556 11.3169L1.12056 9.44194C0.876481 9.19786 0.876481 8.80214 1.12056 8.55806Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M3.4375 8.375C3.78268 8.375 4.0625 8.65482 4.0625 9C4.0625 9.52632 4.12768 10.0393 4.24249 10.5393C4.31972 10.8757 4.10961 11.2111 3.77319 11.2883C3.43676 11.3656 3.10142 11.1554 3.02418 10.819C2.89065 10.2374 2.8125 9.63035 2.8125 9C2.8125 8.65482 3.09232 8.375 3.4375 8.375Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.8125 9C2.8125 4.51232 6.44982 0.875 10.9375 0.875C15.4252 0.875 19.0625 4.51232 19.0625 9C19.0625 9.34518 18.7827 9.625 18.4375 9.625C18.0923 9.625 17.8125 9.34518 17.8125 9C17.8125 5.20268 14.7348 2.125 10.9375 2.125C7.14018 2.125 4.0625 5.20268 4.0625 9C4.0625 9.34518 3.78268 9.625 3.4375 9.625C3.09232 9.625 2.8125 9.34518 2.8125 9Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.4375 8.375C18.7827 8.375 19.0625 8.65482 19.0625 9C19.0625 13.4877 15.4252 17.125 10.9375 17.125C10.5923 17.125 10.3125 16.8452 10.3125 16.5C10.3125 16.1548 10.5923 15.875 10.9375 15.875C14.7348 15.875 17.8125 12.7973 17.8125 9C17.8125 8.65482 18.0923 8.375 18.4375 8.375Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.43645 12.7839C4.71918 12.5859 5.10891 12.6546 5.30693 12.9373C6.55127 14.7139 8.60741 15.875 10.9375 15.875C11.2827 15.875 11.5625 16.1548 11.5625 16.5C11.5625 16.8452 11.2827 17.125 10.9375 17.125C8.18092 17.125 5.7504 15.7494 4.28307 13.6544C4.08505 13.3716 4.15372 12.9819 4.43645 12.7839Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.7033 5.02917C11.0485 5.02917 11.3283 5.30899 11.3283 5.65417V9.22628L14.0663 10.8554C14.3629 11.0319 14.4603 11.4155 14.2838 11.7121C14.1073 12.0087 13.7237 12.1061 13.4271 11.9296L10.3837 10.1188C10.1944 10.0061 10.0783 9.80202 10.0783 9.58167V5.65417C10.0783 5.30899 10.3582 5.02917 10.7033 5.02917Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Icon;
