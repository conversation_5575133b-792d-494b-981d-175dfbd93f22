const Icon = () => {
  return (
    <svg
      width='18'
      height='16'
      viewBox='0 0 18 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.5 0.71667C11.8452 0.71667 12.125 0.996492 12.125 1.34167V12.5667C12.125 12.9118 11.8452 13.1917 11.5 13.1917C11.1548 13.1917 10.875 12.9118 10.875 12.5667V1.34167C10.875 0.996492 11.1548 0.71667 11.5 0.71667Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.5 2.19167C6.84518 2.19167 7.125 2.47149 7.125 2.81667V14.6667C7.125 15.0118 6.84518 15.2917 6.5 15.2917C6.15482 15.2917 5.875 15.0118 5.875 14.6667V2.81667C5.875 2.47149 6.15482 2.19167 6.5 2.19167Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.4336 1.22159C11.5427 0.894119 11.8967 0.717121 12.2241 0.826255L16.1275 2.12709C16.4549 2.23622 16.6319 2.59016 16.5228 2.91763C16.4137 3.2451 16.0597 3.4221 15.7323 3.31297L11.8289 2.01213C11.5015 1.903 11.3245 1.54906 11.4336 1.22159Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.3369 2.5228C15.4458 2.19525 15.7999 2.01817 16.1275 2.12709C16.7243 2.32553 17.1249 2.88303 17.1249 3.51084C17.1249 3.85602 16.8451 4.13584 16.4999 4.13584C16.1548 4.13584 15.8749 3.85602 15.8749 3.51084C15.8749 3.42032 15.8171 3.34119 15.7323 3.31297C15.4047 3.20405 15.2279 2.85034 15.3369 2.5228Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M16.5 2.88582C16.8452 2.88582 17.1249 3.16566 17.1249 3.51084L17.125 13.515C17.125 13.8602 16.8452 14.14 16.5 14.14C16.1548 14.14 15.875 13.8602 15.875 13.515L15.8749 3.51084C15.8749 3.16566 16.1548 2.88582 16.5 2.88582Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M16.5002 12.89C16.8454 12.89 17.125 13.1698 17.125 13.515C17.125 14.5982 15.9849 15.3048 15.0145 14.8188C14.7059 14.6643 14.5809 14.2888 14.7355 13.9801C14.8901 13.6715 15.2656 13.5466 15.5742 13.7011C15.7122 13.7702 15.875 13.6701 15.875 13.515C15.875 13.1698 16.155 12.89 16.5002 12.89Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.6868 12.4563C11.8412 12.1476 12.2166 12.0224 12.5254 12.1768L15.5742 13.7011C15.8829 13.8555 16.0076 14.2308 15.8532 14.5395C15.6988 14.8482 15.3232 14.9732 15.0145 14.8188L11.9663 13.2948C11.6576 13.1405 11.5325 12.7651 11.6868 12.4563Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.4751 12.177C11.1204 11.8538 11.88 11.8536 12.5254 12.1768C12.834 12.3314 12.9592 12.7071 12.8046 13.0157C12.65 13.3244 12.275 13.4494 11.9663 13.2948C11.6733 13.1481 11.3279 13.1479 11.0349 13.2947C10.7262 13.4492 10.3507 13.3244 10.1961 13.0157C10.0416 12.7071 10.1664 12.3316 10.4751 12.177Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.314 12.4563C11.4684 12.765 11.3436 13.1403 11.0349 13.2947L7.52541 15.0498C7.21669 15.2042 6.84126 15.0791 6.68686 14.7704C6.53246 14.4617 6.65757 14.0862 6.96629 13.9318L10.4751 12.177C10.7838 12.0226 11.1596 12.1475 11.314 12.4563Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.19615 14.2109C5.35073 13.9023 5.72624 13.7774 6.03487 13.932C6.32786 14.0788 6.67329 14.0786 6.96629 13.9318C7.27492 13.7772 7.65003 13.9023 7.80462 14.2109C7.9592 14.5196 7.83404 14.8952 7.52541 15.0498C6.88007 15.3731 6.1204 15.3729 5.47506 15.0497C5.16643 14.8951 5.04156 14.5196 5.19615 14.2109Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.40198 12.3147C1.55632 12.006 1.93173 11.8808 2.24048 12.0351L6.03487 13.932C6.34362 14.0864 6.46857 14.4616 6.31423 14.7703C6.15989 15.0791 5.78381 15.204 5.47506 15.0497L1.68156 13.1532C1.37281 12.9989 1.24764 12.6235 1.40198 12.3147Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.5 11.2233C1.84518 11.2233 2.125 11.5031 2.125 11.8483C2.125 11.9271 2.16883 11.9992 2.24048 12.0351C2.54903 12.1899 2.67425 12.5658 2.51952 12.8743C2.36479 13.1829 1.98922 13.3076 1.68067 13.1528C1.18731 12.9054 0.875 12.4012 0.875 11.8483C0.875 11.5031 1.15482 11.2233 1.5 11.2233Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.5 1.77416C1.84518 1.77416 2.125 2.05399 2.125 2.39916V11.8483C2.125 12.1935 1.84518 12.4733 1.5 12.4733C1.15482 12.4733 0.875 12.1935 0.875 11.8483V2.39916C0.875 2.05399 1.15482 1.77416 1.5 1.77416Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.39875 2.20115C2.26479 2.15643 2.125 2.25625 2.125 2.39916C2.125 2.74434 1.84518 3.02415 1.5 3.02415C1.15482 3.02415 0.875 2.74434 0.875 2.39916C0.875 1.40375 1.85021 0.700206 2.79458 1.01548C3.122 1.12479 3.29881 1.47882 3.1895 1.80624C3.0802 2.13365 2.72616 2.31046 2.39875 2.20115Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.00366 1.41075C2.11279 1.08328 2.46711 0.906353 2.79458 1.01548L6.17003 2.14041C6.4975 2.24954 6.6745 2.60348 6.56537 2.93095C6.45624 3.25842 6.10231 3.43543 5.77483 3.32629L2.39875 2.20115C2.07128 2.09202 1.89453 1.73823 2.00366 1.41075Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.37973 2.53569C5.48888 2.20823 5.84257 2.03126 6.17003 2.14041C6.38424 2.21181 6.61497 2.21181 6.82918 2.14041C7.15664 2.03125 7.51059 2.20823 7.61975 2.53569C7.7289 2.86315 7.55193 3.2171 7.22446 3.32626C6.75367 3.48319 6.24563 3.48323 5.77483 3.32629C5.44737 3.21714 5.27057 2.86315 5.37973 2.53569Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.5653 1.22073C11.6744 1.5482 11.4974 1.90214 11.17 2.01127L7.22446 3.32626C6.89699 3.43539 6.54272 3.25841 6.43359 2.93093C6.32446 2.60346 6.50171 2.24954 6.82918 2.14041L10.7748 0.82539C11.1022 0.716256 11.4562 0.893254 11.5653 1.22073Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.8289 2.01213C11.616 1.94097 11.3841 1.9405 11.169 2.0117C10.8413 2.12013 10.4877 1.94238 10.3793 1.61468C10.2709 1.28698 10.4486 0.933414 10.7763 0.824977C11.2462 0.669502 11.7538 0.669086 12.2241 0.826255C12.5515 0.935649 12.729 1.28986 12.6196 1.61725C12.5102 1.94463 12.1563 2.12153 11.8289 2.01213Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Icon;
