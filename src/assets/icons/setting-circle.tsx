const Icon = () => {
  return (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.1027 10.861C15.1886 11.2043 14.9799 11.5523 14.6366 11.6381L14.6087 11.6451C14.2654 11.731 13.9175 11.5222 13.8316 11.1789C13.7458 10.8356 13.9545 10.4877 14.2978 10.4018L14.3256 10.3949C14.669 10.309 15.0169 10.5177 15.1027 10.861Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.1285 9.54657C15.4824 9.54657 15.7693 9.83346 15.7693 10.1874C15.7693 10.8736 15.3023 11.4717 14.6366 11.6381C14.2933 11.724 13.9453 11.5153 13.8595 11.172C13.7736 10.8287 13.9823 10.4807 14.3256 10.3949C14.4209 10.371 14.4877 10.2855 14.4877 10.1874C14.4877 9.83346 14.7746 9.54657 15.1285 9.54657Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.1285 9.17153C15.4823 9.17153 15.7692 9.45842 15.7692 9.81232L15.7693 10.1874C15.7693 10.5413 15.4823 10.8282 15.1285 10.8282C14.7746 10.8282 14.4877 10.5413 14.4877 10.1874L14.4877 9.81232C14.4877 9.45842 14.7746 9.17153 15.1285 9.17153Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.8595 8.82754C13.9453 8.48421 14.2932 8.27548 14.6365 8.36133C15.3023 8.52778 15.7692 9.1261 15.7692 9.81232C15.7692 10.1662 15.4824 10.453 15.1285 10.453C14.7746 10.453 14.4877 10.1662 14.4877 9.81232C14.4877 9.71414 14.4209 9.62843 14.3257 9.60462C13.9823 9.51877 13.7736 9.17086 13.8595 8.82754Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.8313 8.82153C13.9167 8.47808 14.2643 8.26886 14.6077 8.35422L14.6356 8.36116C14.9791 8.44652 15.1883 8.79414 15.1029 9.13759C15.0176 9.48103 14.67 9.69025 14.3265 9.60489L14.2986 9.59795C13.9552 9.51259 13.7459 9.16497 13.8313 8.82153Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.5651 7.82916C13.8921 7.69383 14.2669 7.84921 14.4023 8.1762C14.439 8.265 14.5157 8.33113 14.6089 8.35448C14.9522 8.44047 15.1607 8.78847 15.0748 9.13176C14.9888 9.47504 14.6408 9.68363 14.2975 9.59764C13.8105 9.47566 13.4101 9.13016 13.2181 8.66628C13.0828 8.33929 13.2381 7.9645 13.5651 7.82916Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.2028 7.02495C14.5062 7.20707 14.6046 7.6007 14.4225 7.90413C14.373 7.98653 14.3655 8.08741 14.4023 8.1762C14.5376 8.5032 14.3822 8.87807 14.0552 9.0134C13.7282 9.14873 13.3534 8.99328 13.2181 8.66628C13.0261 8.20241 13.0653 7.67508 13.3236 7.24462C13.5058 6.94118 13.8994 6.84284 14.2028 7.02495Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.2142 7.00516C14.5178 7.18711 14.6164 7.58068 14.4344 7.88421L14.4225 7.90413C14.2405 8.20767 13.8472 8.30589 13.5436 8.12394C13.2401 7.94199 13.1417 7.54816 13.3236 7.24462L13.3352 7.22532C13.5171 6.92178 13.9107 6.82321 14.2142 7.00516Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.3065 6.05229C13.557 5.80238 13.9628 5.80292 14.2127 6.05349C14.6991 6.54125 14.7899 7.29805 14.4326 7.88704C14.2491 8.18962 13.855 8.2861 13.5524 8.10255C13.2499 7.919 13.1534 7.52492 13.3369 7.22234C13.3883 7.13759 13.3753 7.02868 13.3053 6.95849C13.0554 6.70792 13.0559 6.3022 13.3065 6.05229Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.0407 5.78776C13.2909 5.53753 13.6966 5.53753 13.9469 5.78778L14.2127 6.05349C14.4629 6.30373 14.4623 6.70895 14.212 6.95919C13.9618 7.20942 13.5561 7.20942 13.3058 6.95917L13.0407 6.69397C12.7904 6.44372 12.7904 6.038 13.0407 5.78776Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.1199 5.56291C12.7084 5.20986 13.4616 5.30253 13.9469 5.78778C14.1971 6.03802 14.197 6.44381 13.9468 6.69405C13.6965 6.94429 13.2909 6.94421 13.0407 6.69397C12.9712 6.62455 12.8634 6.61135 12.7792 6.66186C12.4758 6.84392 12.0822 6.74551 11.9001 6.44205C11.718 6.13858 11.8164 5.74497 12.1199 5.56291Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.9991 5.78268C13.1812 6.08614 13.0827 6.47977 12.7792 6.66186L12.7547 6.67665C12.4512 6.85874 12.0576 6.76035 11.8755 6.45689C11.6934 6.15344 11.7918 5.75983 12.0953 5.57774L12.1199 5.56291C12.4234 5.38082 12.817 5.47923 12.9991 5.78268Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.9744 5.79753C13.1565 6.10099 13.0581 6.49458 12.7547 6.67665C12.3242 6.93491 11.7968 6.97395 11.333 6.78186C11.006 6.64644 10.8508 6.27161 10.9862 5.94465C11.1216 5.61769 11.4964 5.46242 11.8234 5.59784C11.9121 5.63457 12.0129 5.62713 12.0953 5.57774C12.3987 5.39567 12.7924 5.49406 12.9744 5.79753Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.8682 4.92506C11.2115 4.8392 11.5594 5.04791 11.6453 5.39123C11.6686 5.48437 11.7347 5.5611 11.8234 5.59784C12.1504 5.73326 12.3056 6.10795 12.1702 6.43491C12.0347 6.76187 11.66 6.91728 11.333 6.78186C10.8692 6.58977 10.5238 6.18915 10.402 5.70216C10.3162 5.35883 10.5249 5.01092 10.8682 4.92506Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.8613 4.89733C11.2046 4.81144 11.5525 5.02013 11.6384 5.36344L11.6453 5.39123C11.7312 5.73454 11.5226 6.08254 11.1793 6.16842C10.836 6.25431 10.4879 6.04547 10.402 5.70216L10.3952 5.67446C10.3093 5.33115 10.518 4.98321 10.8613 4.89733Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.54678 4.87149C9.54678 4.5176 9.83366 4.23071 10.1876 4.23071C10.8738 4.23071 11.4719 4.69771 11.6384 5.36344C11.7243 5.70676 11.5155 6.05468 11.1722 6.14053C10.8289 6.22638 10.481 6.01779 10.3952 5.67446C10.3714 5.57923 10.2857 5.51228 10.1876 5.51228C9.83366 5.51228 9.54678 5.22539 9.54678 4.87149Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.17174 4.87149C9.17174 4.5176 9.45862 4.23071 9.81252 4.23071H10.1876C10.5414 4.23071 10.8284 4.5176 10.8284 4.87149C10.8284 5.22539 10.5414 5.51228 10.1876 5.51228H9.81252C9.45862 5.51228 9.17174 5.22539 9.17174 4.87149Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.36164 5.36346C8.5281 4.69772 9.1263 4.23071 9.81252 4.23071C10.1664 4.23071 10.4533 4.5176 10.4533 4.87149C10.4533 5.22539 10.1664 5.51228 9.81252 5.51228C9.71434 5.51228 9.62873 5.57909 9.60492 5.67433C9.51908 6.01765 9.17117 6.22638 8.82785 6.14054C8.48452 6.05469 8.2758 5.70678 8.36164 5.36346Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.13868 4.8973C9.48201 4.98313 9.69075 5.331 9.60492 5.67433L9.59794 5.70229C9.51211 6.04562 9.16422 6.25436 8.82089 6.16853C8.47756 6.08271 8.26882 5.73481 8.35464 5.39148L8.36164 5.36346C8.44747 5.02013 8.79535 4.81147 9.13868 4.8973Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.13197 4.92533C9.47525 5.01133 9.68394 5.359 9.59794 5.70229C9.47594 6.18925 9.13034 6.58995 8.6665 6.78193C8.33951 6.91727 7.96472 6.7619 7.82938 6.4349C7.69405 6.10791 7.84941 5.73311 8.17641 5.59778C8.2652 5.56103 8.33129 5.48469 8.35464 5.39148C8.44065 5.04819 8.78869 4.83933 9.13197 4.92533Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.02515 5.79726C7.20725 5.49381 7.60086 5.39544 7.90431 5.57754C7.98671 5.62699 8.08762 5.63453 8.17641 5.59778C8.5034 5.46244 8.87822 5.61776 9.01356 5.94476C9.14889 6.27175 8.99349 6.6466 8.6665 6.78193C8.20265 6.97391 7.67532 6.93474 7.24487 6.67642C6.94142 6.49432 6.84305 6.10071 7.02515 5.79726Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.00586 5.78523C7.18815 5.4819 7.58182 5.38378 7.88515 5.56607L7.90431 5.57754C8.20764 5.75983 8.30616 6.15379 8.12387 6.45712C7.94158 6.76046 7.5482 6.85872 7.24487 6.67642L7.22502 6.66454C6.92169 6.48224 6.82357 6.08857 7.00586 5.78523Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.0538 5.78732C6.54158 5.30079 7.29843 5.20999 7.88744 5.56735C8.19 5.75092 8.28646 6.14501 8.1029 6.44757C7.91933 6.75013 7.52525 6.8466 7.22269 6.66303C7.13793 6.61161 7.02902 6.62467 6.95883 6.69468C6.70827 6.9446 6.30255 6.94408 6.05263 6.69352C5.80272 6.44296 5.80324 6.03724 6.0538 5.78732Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.95939 5.78798C7.20963 6.03822 7.20907 6.44444 6.95883 6.69468L6.6942 6.95938C6.44396 7.20962 6.03824 7.20962 5.788 6.95938C5.53776 6.70914 5.53776 6.30342 5.788 6.05318L6.05319 5.78798C6.30343 5.53774 6.70915 5.53774 6.95939 5.78798Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.69424 6.05315C6.94445 6.30342 6.94446 6.70917 6.6942 6.95938C6.6248 7.02876 6.61147 7.13643 6.66195 7.22058C6.84402 7.52405 6.7456 7.91765 6.44213 8.09971C6.13866 8.28178 5.74506 8.18336 5.563 7.87989C5.20996 7.29144 5.3027 6.53836 5.788 6.05318C6.03827 5.80297 6.44403 5.80288 6.69424 6.05315Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.78286 7.00069C6.08633 6.81863 6.47989 6.91711 6.66195 7.22058L6.67658 7.24487C6.85864 7.54834 6.76022 7.94194 6.45675 8.124C6.15328 8.30606 5.75968 8.20763 5.57762 7.90416L5.563 7.87989C5.38094 7.57642 5.47939 7.18275 5.78286 7.00069Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.79738 7.02515C6.10082 6.84304 6.49448 6.94142 6.67658 7.24487C6.9349 7.67531 6.97398 8.20268 6.78201 8.66653C6.64667 8.99353 6.27188 9.1489 5.94489 9.01357C5.6179 8.87823 5.46253 8.50344 5.59786 8.17645C5.63461 8.08766 5.62707 7.98656 5.57762 7.90416C5.39552 7.60072 5.49393 7.20725 5.79738 7.02515Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.43491 7.82937C6.7619 7.96471 6.91734 8.33954 6.78201 8.66653C6.59003 9.13039 6.1896 9.47583 5.70264 9.59783C5.35936 9.68383 5.01135 9.47526 4.92535 9.13198C4.83935 8.78869 5.04791 8.44069 5.3912 8.35468C5.48441 8.33133 5.56111 8.26524 5.59786 8.17645C5.73319 7.84945 6.10792 7.69404 6.43491 7.82937Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.16867 8.82093C6.25448 9.16426 6.04597 9.51202 5.70264 9.59783L5.67454 9.60491C5.33121 9.69072 4.98333 9.48195 4.89752 9.13862C4.81171 8.79529 5.02048 8.4474 5.36381 8.36159L5.3912 8.35468C5.73453 8.26888 6.08287 8.47759 6.16867 8.82093Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.14076 8.82774C6.2266 9.17107 6.01787 9.51907 5.67454 9.60491C5.57929 9.62873 5.5125 9.71422 5.5125 9.81236C5.5125 10.1663 5.22562 10.4531 4.87173 10.4531C4.51783 10.4531 4.23095 10.1663 4.23095 9.81236C4.23095 9.12613 4.69811 8.52804 5.36381 8.36159C5.70713 8.27575 6.05492 8.48442 6.14076 8.82774Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.87173 9.17174C5.22562 9.17174 5.5125 9.45847 5.5125 9.81236V10.1876C5.5125 10.5415 5.22562 10.8284 4.87173 10.8284C4.51783 10.8284 4.23095 10.5415 4.23095 10.1876V9.81236C4.23095 9.45847 4.51783 9.17174 4.87173 9.17174Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.87173 9.54678C5.22562 9.54678 5.5125 9.83374 5.5125 10.1876C5.5125 10.2859 5.5793 10.3712 5.67436 10.395C6.01769 10.4809 6.22641 10.8288 6.14056 11.1721C6.05472 11.5154 5.70681 11.7241 5.36348 11.6383C4.69772 11.4718 4.23095 10.8737 4.23095 10.1876C4.23095 9.83374 4.51783 9.54678 4.87173 9.54678Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.89735 10.8612C4.98323 10.5179 5.33105 10.3091 5.67436 10.395L5.70212 10.402C6.04543 10.4879 6.25412 10.8358 6.16823 11.1791C6.08235 11.5224 5.73441 11.7311 5.3911 11.6452L5.36348 11.6383C5.02017 11.5524 4.81146 11.2045 4.89735 10.8612Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.92494 10.8682C5.01074 10.5249 5.35879 10.3162 5.70212 10.402C6.18916 10.5237 6.58957 10.8689 6.78183 11.3326C6.91736 11.6595 6.76221 12.0344 6.4353 12.1699C6.10839 12.3055 5.7335 12.1503 5.59797 11.8234C5.56117 11.7346 5.48433 11.6685 5.3911 11.6452C5.04777 11.5595 4.83915 11.2116 4.92494 10.8682Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.94445 10.9861C6.27137 10.8506 6.6463 11.0057 6.78183 11.3326C6.97408 11.7964 6.93522 12.3238 6.67715 12.7544C6.49522 13.0579 6.10167 13.1565 5.79812 12.9746C5.49457 12.7927 5.39597 12.3991 5.57789 12.0956C5.62729 12.0131 5.63478 11.9122 5.59797 11.8234C5.46245 11.4965 5.61754 11.1216 5.94445 10.9861Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.45733 11.8754C6.76077 12.0576 6.85926 12.4509 6.67715 12.7544L6.66224 12.7792C6.48014 13.0827 6.08652 13.181 5.78308 12.9989C5.47964 12.8168 5.38128 12.4232 5.56339 12.1198L5.57789 12.0956C5.76 11.7921 6.15389 11.6933 6.45733 11.8754Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.44248 11.9C6.74594 12.0821 6.84431 12.4758 6.66224 12.7792C6.61174 12.8634 6.62504 12.9711 6.69446 13.0405C6.94469 13.2908 6.94468 13.6965 6.69443 13.9467C6.44419 14.1969 6.03847 14.1969 5.78824 13.9467C5.30303 13.4614 5.21035 12.7082 5.56339 12.1198C5.74545 11.8163 6.13901 11.7179 6.44248 11.9Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.78822 13.0406C6.03847 12.7903 6.44423 12.7903 6.69446 13.0405L6.95965 13.3058C7.20988 13.5561 7.20987 13.9618 6.95962 14.212C6.70937 14.4623 6.30365 14.4623 6.05342 14.212L5.78824 13.9467C5.538 13.6964 5.53797 13.2908 5.78822 13.0406Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.05351 13.3056C6.30379 13.0554 6.70946 13.0556 6.95965 13.3058C7.02897 13.3752 7.13666 13.3884 7.22074 13.3379C7.52421 13.1559 7.91781 13.2543 8.09987 13.5578C8.28193 13.8612 8.18351 14.2548 7.88004 14.4369C7.29163 14.7899 6.53854 14.6973 6.05342 14.212C5.80323 13.9617 5.80322 13.5558 6.05351 13.3056Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.12398 13.5427C8.30634 13.846 8.20831 14.2397 7.90503 14.4221L7.88004 14.4369C7.57676 14.6193 7.1836 14.5211 7.00124 14.2178C6.81888 13.9145 6.91745 13.5203 7.22074 13.3379L7.24464 13.3237C7.54792 13.1414 7.94162 13.2394 8.12398 13.5427Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.1767 14.402C8.0879 14.3652 7.98743 14.3726 7.90503 14.4221C7.60159 14.6042 7.20749 14.5059 7.02538 14.2025C6.84326 13.8991 6.9412 13.5059 7.24464 13.3237C7.67509 13.0654 8.20289 13.0259 8.66676 13.2178C8.99376 13.3532 9.14913 13.728 9.01381 14.0549C8.87848 14.3819 8.50369 14.5373 8.1767 14.402Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.82969 13.5649C7.96502 13.2379 8.33977 13.0825 8.66676 13.2178C9.13063 13.4098 9.47616 13.8102 9.59816 14.2972C9.68416 14.6404 9.47559 14.9884 9.13231 15.0745C8.78902 15.1605 8.44102 14.9519 8.35502 14.6086C8.33166 14.5154 8.26549 14.4387 8.1767 14.402C7.8497 14.2667 7.69436 13.8919 7.82969 13.5649Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.82114 13.8313C9.16446 13.7455 9.5123 13.9538 9.59816 14.2972L9.60521 14.3254C9.69107 14.6687 9.48235 15.0166 9.13903 15.1025C8.79572 15.1883 8.4478 14.9796 8.36194 14.6363L8.35502 14.6086C8.26916 14.2653 8.47782 13.9172 8.82114 13.8313Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.82817 13.8591C9.17149 13.7732 9.51937 13.982 9.60521 14.3254C9.62902 14.4206 9.71463 14.4873 9.81276 14.4873C10.1667 14.4873 10.4535 14.7742 10.4535 15.1281C10.4535 15.482 10.1667 15.7689 9.81276 15.7689C9.12652 15.7689 8.52838 15.302 8.36194 14.6363C8.2761 14.293 8.48484 13.9449 8.82817 13.8591Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.17195 15.1282C9.17195 14.7743 9.45887 14.4873 9.81276 14.4873L10.1878 14.4874C10.5417 14.4874 10.8286 14.7743 10.8286 15.1282C10.8286 15.4821 10.5417 15.769 10.1878 15.769L9.81276 15.7689C9.45887 15.7689 9.17195 15.4821 9.17195 15.1282Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.1724 13.8593C11.5157 13.9451 11.7244 14.293 11.6386 14.6364C11.4721 15.3021 10.8739 15.769 10.1878 15.769C9.83394 15.769 9.54709 15.4821 9.54709 15.1282C9.54709 14.7743 9.83394 14.4874 10.1878 14.4874C10.286 14.4874 10.3715 14.4206 10.3953 14.3255C10.4812 13.9822 10.8291 13.7734 11.1724 13.8593Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.1792 13.8317C11.5225 13.9175 11.7313 14.2654 11.6455 14.6088L11.6386 14.6364C11.5528 14.9797 11.2049 15.1885 10.8615 15.1027C10.5182 15.0169 10.3095 14.6688 10.3953 14.3255L10.4021 14.298C10.488 13.9547 10.8358 13.7459 11.1792 13.8317Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.1701 13.5647C12.3057 13.8916 12.1505 14.2665 11.8236 14.4021C11.7348 14.4388 11.6688 14.5155 11.6455 14.6088C11.5597 14.9521 11.2117 15.1608 10.8684 15.075C10.5251 14.9892 10.3163 14.6414 10.4021 14.298C10.5239 13.811 10.8691 13.4104 11.3328 13.2182C11.6597 13.0827 12.0346 13.2378 12.1701 13.5647Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.0957 14.4221C12.0133 14.3727 11.9123 14.3653 11.8236 14.4021C11.4967 14.5376 11.1218 14.3824 10.9863 14.0555C10.8507 13.7286 11.0059 13.3537 11.3328 13.2182C11.7966 13.0259 12.324 13.0648 12.7545 13.3228C13.0581 13.5048 13.1567 13.8983 12.9747 14.2019C12.7928 14.5054 12.3993 14.604 12.0957 14.4221Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.8756 13.543C12.0576 13.2395 12.451 13.1409 12.7545 13.3228L12.7793 13.3377C13.0828 13.5197 13.1813 13.9132 12.9994 14.2168C12.8174 14.5203 12.4238 14.6188 12.1203 14.4368L12.0957 14.4221C11.7922 14.2401 11.6936 13.8465 11.8756 13.543Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.947 13.3055C14.1972 13.5558 14.1972 13.9615 13.947 14.2117C13.4617 14.6969 12.7087 14.7898 12.1203 14.4368C11.8168 14.2548 11.7182 13.8609 11.9003 13.5575C12.0824 13.254 12.4758 13.1556 12.7793 13.3377C12.8635 13.3882 12.9714 13.3749 13.0408 13.3055C13.291 13.0552 13.6968 13.0553 13.947 13.3055Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.2122 13.0404C14.4625 13.2906 14.4624 13.6964 14.2122 13.9466L13.947 14.2117C13.6967 14.4619 13.291 14.462 13.0407 14.2118C12.7905 13.9615 12.7905 13.5557 13.0408 13.3055L13.306 13.0404C13.5563 12.7901 13.962 12.7901 14.2122 13.0404Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.5582 11.9C13.8616 11.7179 14.2552 11.8164 14.4373 12.1198C14.7903 12.7083 14.6975 13.4615 14.2122 13.9466C13.9619 14.1968 13.5562 14.1967 13.306 13.9464C13.0558 13.6961 13.0558 13.2906 13.306 13.0404C13.3754 12.971 13.3888 12.8632 13.3383 12.7791C13.1563 12.4757 13.2547 12.0821 13.5582 11.9Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.5434 11.8756C13.8469 11.6935 14.2405 11.792 14.4226 12.0955L14.4373 12.1198C14.6193 12.4233 14.5208 12.817 14.2174 12.999C13.9139 13.1811 13.5204 13.0826 13.3383 12.7791L13.3236 12.7547C13.1415 12.4512 13.24 12.0576 13.5434 11.8756Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.0553 10.9861C14.3823 11.1215 14.5377 11.4963 14.4024 11.8233C14.3656 11.9121 14.3731 12.0131 14.4226 12.0955C14.6047 12.3989 14.5063 12.7925 14.2028 12.9745C13.8994 13.1566 13.5057 13.0582 13.3236 12.7547C13.0653 12.3243 13.0263 11.797 13.2182 11.3332C13.3536 11.0062 13.7284 10.8508 14.0553 10.9861Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.0748 10.8676C15.1608 11.2109 14.952 11.5591 14.6087 11.6451C14.5155 11.6685 14.4391 11.7345 14.4024 11.8233C14.267 12.1503 13.8922 12.3056 13.5652 12.1702C13.2383 12.0349 13.0829 11.6602 13.2182 11.3332C13.4102 10.8693 13.8109 10.5238 14.2978 10.4018C14.6411 10.3158 14.9888 10.5244 15.0748 10.8676Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M17.6926 9.35926C18.0465 9.35926 18.3334 9.64615 18.3334 10C18.3334 14.6024 14.6024 18.3334 10 18.3334C9.64614 18.3334 9.35926 18.0465 9.35926 17.6926C9.35926 17.3387 9.64614 17.0519 10 17.0519C13.8946 17.0519 17.0518 13.8947 17.0518 10C17.0518 9.64615 17.3387 9.35926 17.6926 9.35926Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.30747 9.35926C2.66136 9.35926 2.94824 9.64615 2.94824 10C2.94824 13.8947 6.10545 17.0519 10 17.0519C10.3539 17.0519 10.6408 17.3387 10.6408 17.6926C10.6408 18.0465 10.3539 18.3334 10 18.3334C5.39767 18.3334 1.66669 14.6024 1.66669 10C1.66669 9.64615 1.95357 9.35926 2.30747 9.35926Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 2.94831C6.10543 2.94831 2.94824 6.10544 2.94824 10C2.94824 10.3539 2.66136 10.6409 2.30747 10.6409C1.95357 10.6409 1.66669 10.3539 1.66669 10C1.66669 5.39765 5.39764 1.66675 10 1.66675C10.3539 1.66675 10.6408 1.95364 10.6408 2.30753C10.6408 2.66142 10.3539 2.94831 10 2.94831Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.35926 2.30753C9.35926 1.95364 9.64611 1.66675 10 1.66675C14.6024 1.66675 18.3334 5.39765 18.3334 10C18.3334 10.3539 18.0465 10.6409 17.6926 10.6409C17.3387 10.6409 17.0518 10.3539 17.0518 10C17.0518 6.10544 13.8946 2.94831 10 2.94831C9.64611 2.94831 9.35926 2.66142 9.35926 2.30753Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.2821 9.35926C11.636 9.35926 11.9229 9.64615 11.9229 10C11.9229 11.062 11.062 11.9229 10 11.9229C9.64614 11.9229 9.35926 11.636 9.35926 11.2821C9.35926 10.9282 9.64614 10.6414 10 10.6414C10.3542 10.6414 10.6413 10.3542 10.6413 10C10.6413 9.64615 10.9282 9.35926 11.2821 9.35926Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.71796 9.35926C9.07185 9.35926 9.35874 9.64615 9.35874 10C9.35874 10.3542 9.64585 10.6414 10 10.6414C10.3539 10.6414 10.6408 10.9282 10.6408 11.2821C10.6408 11.636 10.3539 11.9229 10 11.9229C8.93807 11.9229 8.07718 11.062 8.07718 10C8.07718 9.64615 8.36407 9.35926 8.71796 9.35926Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 9.35864C9.64586 9.35864 9.35874 9.64586 9.35874 10C9.35874 10.3539 9.07185 10.6407 8.71796 10.6407C8.36407 10.6407 8.07718 10.3539 8.07718 10C8.07718 8.93807 8.93808 8.07707 10 8.07707C10.3539 8.07707 10.6408 8.36396 10.6408 8.71785C10.6408 9.07175 10.3539 9.35864 10 9.35864Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.35926 8.71785C9.35926 8.36396 9.64616 8.07707 10 8.07707C11.062 8.07707 11.9229 8.93807 11.9229 10C11.9229 10.3539 11.636 10.6407 11.2821 10.6407C10.9282 10.6407 10.6413 10.3539 10.6413 10C10.6413 9.64585 10.3542 9.35864 10 9.35864C9.64616 9.35864 9.35926 9.07175 9.35926 8.71785Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Icon;
