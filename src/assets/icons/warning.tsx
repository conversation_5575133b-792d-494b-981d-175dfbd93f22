const Icon = () => {
  return (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.09221 5.69022L5.553 10.2035C5.37133 10.5262 5.37451 10.9212 5.56146 11.241C5.7484 11.5607 6.09096 11.7572 6.46139 11.7572H11.5393C11.9098 11.7572 12.2523 11.5607 12.4393 11.241C12.6262 10.9212 12.6295 10.5263 12.4478 10.2036L9.90852 5.69022C9.72381 5.36234 9.3767 5.15949 9.00037 5.15949C8.62416 5.15949 8.27697 5.36253 8.09221 5.69022ZM7.00315 5.07667C7.40938 4.3556 8.17274 3.90949 9.00037 3.90949C9.82799 3.90949 10.5914 4.3556 10.9976 5.07667L13.5371 9.59043C13.5371 9.59039 13.5371 9.59046 13.5371 9.59043C13.9366 10.3002 13.9295 11.1688 13.5184 11.8719C13.1072 12.5751 12.3538 13.0072 11.5393 13.0072H6.46139C5.64692 13.0072 4.89353 12.5751 4.48238 11.8719C4.07127 11.1688 4.06411 10.3003 4.46358 9.59053C4.46356 9.59056 4.4636 9.5905 4.46358 9.59053L7.00315 5.07667Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.09221 5.69022L5.553 10.2035C5.37133 10.5262 5.37451 10.9212 5.56146 11.241C5.7484 11.5607 6.09096 11.7572 6.46139 11.7572H11.5393C11.9098 11.7572 12.2523 11.5607 12.4393 11.241C12.6262 10.9212 12.6295 10.5263 12.4478 10.2036L9.90852 5.69022C9.72381 5.36234 9.3767 5.15949 9.00037 5.15949C8.62416 5.15949 8.27697 5.36253 8.09221 5.69022ZM7.00315 5.07667C7.40938 4.3556 8.17274 3.90949 9.00037 3.90949C9.82799 3.90949 10.5914 4.3556 10.9976 5.07667L13.5371 9.59043C13.5371 9.59039 13.5371 9.59046 13.5371 9.59043C13.9366 10.3002 13.9295 11.1688 13.5184 11.8719C13.1072 12.5751 12.3538 13.0072 11.5393 13.0072H6.46139C5.64692 13.0072 4.89353 12.5751 4.48238 11.8719C4.07127 11.1688 4.06411 10.3003 4.46358 9.59053C4.46356 9.59056 4.4636 9.5905 4.46358 9.59053L7.00315 5.07667Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.99995 6.40913C9.34513 6.40913 9.62495 6.68895 9.62495 7.03413V8.56394C9.62495 8.90911 9.34513 9.18894 8.99995 9.18894C8.65477 9.18894 8.37495 8.90911 8.37495 8.56394V7.03413C8.37495 6.68895 8.65477 6.40913 8.99995 6.40913Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.99995 2.12183C5.20127 2.12183 2.12183 5.20127 2.12183 8.99995C2.12183 12.7986 5.20127 15.8781 8.99995 15.8781C12.7986 15.8781 15.8781 12.7986 15.8781 8.99995C15.8781 5.20127 12.7986 2.12183 8.99995 2.12183ZM8.99995 17.1281C4.51091 17.1281 0.871826 13.489 0.871826 8.99995C0.871826 4.51091 4.51091 0.871826 8.99995 0.871826C13.489 0.871826 17.1281 4.51091 17.1281 8.99995C17.1281 13.489 13.489 17.1281 8.99995 17.1281Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.29908 10.3263C8.29908 9.93926 8.61287 9.62547 8.99995 9.62547C9.38703 9.62547 9.70082 9.93926 9.70082 10.3263C9.70082 10.7134 9.38703 11.0272 8.99995 11.0272C8.61287 11.0272 8.29908 10.7134 8.29908 10.3263Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Icon;
