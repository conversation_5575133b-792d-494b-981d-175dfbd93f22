const Icon = () => {
  return (
    <svg
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14 7.95833C13.8852 7.95833 13.7917 8.05185 13.7917 8.16667V12.3333C13.7917 12.4482 13.8852 12.5417 14 12.5417H14.8333C15.409 12.5417 15.875 12.0757 15.875 11.5V9C15.875 8.42435 15.409 7.95833 14.8333 7.95833H14ZM12.5417 8.16667C12.5417 7.36149 13.1948 6.70833 14 6.70833H14.8333C16.0993 6.70833 17.125 7.73399 17.125 9V11.5C17.125 12.766 16.0993 13.7917 14.8333 13.7917H14C13.1948 13.7917 12.5417 13.1385 12.5417 12.3333V8.16667Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M3.16667 7.95833C2.59101 7.95833 2.125 8.42435 2.125 9V11.5C2.125 12.0757 2.59101 12.5417 3.16667 12.5417H4C4.11482 12.5417 4.20833 12.4482 4.20833 12.3333V8.16667C4.20833 8.05185 4.11482 7.95833 4 7.95833H3.16667ZM0.875 9C0.875 7.73399 1.90066 6.70833 3.16667 6.70833H4C4.80518 6.70833 5.45833 7.36149 5.45833 8.16667V12.3333C5.45833 13.1385 4.80518 13.7917 4 13.7917H3.16667C1.90066 13.7917 0.875 12.766 0.875 11.5V9Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.95833 6.91667C2.95833 3.57982 5.66316 0.875 9 0.875C12.3368 0.875 15.0417 3.57982 15.0417 6.91667V7.33333C15.0417 7.67851 14.7618 7.95833 14.4167 7.95833C14.0715 7.95833 13.7917 7.67851 13.7917 7.33333V6.91667C13.7917 4.27018 11.6465 2.125 9 2.125C6.35351 2.125 4.20833 4.27018 4.20833 6.91667V7.33333C4.20833 7.67851 3.92851 7.95833 3.58333 7.95833C3.23816 7.95833 2.95833 7.67851 2.95833 7.33333V6.91667Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.47917 15.25C8.24934 15.25 8.0625 15.4368 8.0625 15.6667C8.0625 15.8965 8.24934 16.0833 8.47917 16.0833H9.52083C9.75066 16.0833 9.9375 15.8965 9.9375 15.6667C9.9375 15.4368 9.75066 15.25 9.52083 15.25H8.47917ZM6.8125 15.6667C6.8125 14.7465 7.55899 14 8.47917 14H9.52083C10.441 14 11.1875 14.7465 11.1875 15.6667C11.1875 16.5868 10.441 17.3333 9.52083 17.3333H8.47917C7.55899 17.3333 6.8125 16.5868 6.8125 15.6667Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14 12.5417C14.3452 12.5417 14.625 12.8215 14.625 13.1667V14C14.625 15.266 13.5993 16.2917 12.3333 16.2917H10.5625C10.2173 16.2917 9.9375 16.0118 9.9375 15.6667C9.9375 15.3215 10.2173 15.0417 10.5625 15.0417H12.3333C12.909 15.0417 13.375 14.5757 13.375 14V13.1667C13.375 12.8215 13.6548 12.5417 14 12.5417Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Icon;
