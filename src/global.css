@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 900;
  src: url('assets/fonts/Inter-Black.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  src: url('assets/fonts/Inter-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 700;
  src: url('assets/fonts/Inter-BoldItalic.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 800;
  src: url('assets/fonts/Inter-ExtraBold.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 200;
  src: url('assets/fonts/Inter-ExtraLight.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 400;
  src: url('assets/fonts/Inter-Italic.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  src: url('assets/fonts/Inter-Light.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  src: url('assets/fonts/Inter-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 500;
  src: url('assets/fonts/Inter-MediumItalic.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  src: url('assets/fonts/Inter-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  src: url('assets/fonts/Inter-SemiBold.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 600;
  src: url('assets/fonts/Inter-SemiBoldItalic.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100;
  src: url('assets/fonts/Inter-Thin.ttf') format('truetype');
}

body {
  font-family: 'Inter', sans-serif !important;
}

.truncate-1-line {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.truncate-2-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.truncate-3-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

/* antd modal */
.ant-modal-close {
  border-radius: 9999px !important;
  right: 24px !important;
  top: 20px !important;
  width: 28px !important;
  height: 28px !important;
  background: #f0f1f5 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.ant-modal-close.bg-white {
  background: white !important;
}

.ant-modal-close:hover {
  background: #e1e3eb !important;
}

.ant-modal-close:hover {
  border-radius: 9999px !important;
}

.ant-modal-close:active {
  border-radius: 9999px !important;
}
.ant-modal-header .ant-modal-title {
  font-size: 20px;
  font-weight: 700;
  color: black;
}

.navio-menu-item-icon svg {
  width: 20px !important;
  height: 20px !important;
}
