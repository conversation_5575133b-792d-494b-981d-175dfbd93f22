import { useCallback } from 'react';

import { useQueryClient } from '@tanstack/react-query';

import { NOTIFICATIONS_QUERY_KEY } from 'features/Notification/hooks/useNotiData';
import { ReportData } from 'features/Notification/types';

interface NotificationsData {
  unreadNotifications: ReportData[];
  readNotifications: ReportData[];
  page: number;
  total_pages: number;
  total_count: number;
}

/**
 * Hook để handle Firebase notifications
 * Tương tự như dispatch(newNotification()) trong Redux
 */
export const useFirebaseNotification = () => {
  const queryClient = useQueryClient();

  const addNotification = useCallback(
    (notification: { id: string; content: string }) => {
      const newNotification: ReportData = {
        id: notification.id,
        type: 'unread',
        content: notification.content,
      };

      // Optimistically update unread notifications
      queryClient.setQueryData(
        [NOTIFICATIONS_QUERY_KEY, 1, false],
        (old: NotificationsData | undefined) => {
          if (!old) {
            return {
              unreadNotifications: [newNotification],
              readNotifications: [],
              page: 1,
              total_pages: 1,
              total_count: 1,
            };
          }

          return {
            ...old,
            unreadNotifications: [newNotification, ...old.unreadNotifications],
            total_count: old.total_count + 1,
          };
        },
      );

      // Invalidate to trigger refetch and sync with server
      queryClient.invalidateQueries({
        queryKey: [NOTIFICATIONS_QUERY_KEY],
        refetchType: 'none', // Don't refetch immediately, just mark as stale
      });
    },
    [queryClient],
  );

  return {
    addNotification,
  };
};
