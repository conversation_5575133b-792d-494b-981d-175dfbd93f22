import { useEffect, useMemo } from 'react';

import { Badge } from 'antd';
import { useProfile } from 'hooks';
import { useTranslation } from 'react-i18next';

import AccountBusiness from 'features/AccountBusiness';
import Contact from 'features/Contact';
import DeviceBusiness from 'features/DeviceBusiness';
import History from 'features/History';
import Map from 'features/Map';
import NotificationDevice from 'features/Notification';
import { useAllNotifications } from 'features/Notification/hooks/useNotiData';
import Overview from 'features/Overview';
import { Account } from 'features/Profile/types';
import Report from 'features/Report';
import ReportIssue from 'features/ReportIssue';
import SimBusiness from 'features/SimBusiness';
import { TFunction } from 'i18next';

import {
  ClockWiseIcon,
  HomeIcon,
  MapIcon,
  NotificationIcon,
  ReportIcon,
  SettingCircleIcon,
  SupportIcon,
  WarningIcon,
} from 'assets/icons';
import { Colors } from 'assets/styles';
import PageWithSidebar from 'components/PageWithSidebar';
import { MultipleSidebarMenuItems } from 'components/PageWithSidebar/types';
import Spinner from 'components/Spinner';
import SwitchRoute, { PageRoute } from 'components/SwitchRoute';

const AuthenticatedRoute = () => {
  const { t } = useTranslation();

  const { totalUnread, refetch: refetchNotifications } = useAllNotifications(1);
  const { profile, isLoading: isLoadingProfile } = useProfile();

  const menu = useMemo(() => {
    return getMenuItems(t, totalUnread, profile as Account);
  }, [t, totalUnread, profile]);

  const authenticatedRoute: PageRoute[] = useMemo(
    () => [
      {
        path: '/*',
        component: () => <PageWithSidebar menu={menu} />,
      },
    ],
    [menu],
  );

  // Fetch notifications on mount
  useEffect(() => {
    refetchNotifications();
  }, [refetchNotifications]);

  // Wait for profile and permissions to load before rendering routes
  if (isLoadingProfile) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          width: '100vw',
        }}
      >
        <Spinner />
      </div>
    );
  }

  return <SwitchRoute routes={authenticatedRoute} />;
};

export default AuthenticatedRoute;

const getMenuItems = (
  t: TFunction,
  unread: number,
  profile: Account,
): MultipleSidebarMenuItems[] => {
  const isDistributor = profile ? profile?.role_type === 'distributor' : true;
  return [
    {
      label: t('sidebar.controller'),
      menu: [
        {
          icon: HomeIcon,
          label: t('sidebar.overview'),
          route: '/overview',
          exact: true,
          component: Overview,
        },
        {
          icon: MapIcon,
          label: t('sidebar.map'),
          route: '/map',
          component: Map,
        },
        ...(isDistributor
          ? [
              {
                icon: SettingCircleIcon,
                label: t('sidebar.business'),
                route: '/business',
                subItems: [
                  {
                    label: 'Quản lý tài khoản',
                    route: '/business/accounts',
                    component: AccountBusiness,
                  },
                  {
                    label: 'Quản lý thiết bị',
                    route: '/business/devices',
                    component: DeviceBusiness,
                  },
                  {
                    label: 'Kho Sim',
                    route: '/business/sim',
                    component: SimBusiness,
                  },
                ],
              },
            ]
          : []),
        {
          icon: ReportIcon,
          label: t('sidebar.report'),
          route: '/report',
          component: Report,
        },
      ],
    },
    { label: '---' },
    {
      label: t('sidebar.setting'),

      menu: [
        {
          icon: NotificationIcon,
          label: t('sidebar.noti'),
          route: '/notification',
          component: NotificationDevice,
          rightElement: (
            <Badge
              count={unread}
              styles={{
                indicator: {
                  borderColor: Colors.red[200],
                  backgroundColor: Colors.red[200],
                },
              }}
            />
          ),
        },
        {
          icon: ClockWiseIcon,
          label: t('sidebar.history'),
          route: '/history',
          component: History,
        },
        {
          icon: WarningIcon,
          label: t('sidebar.reportIssue'),
          route: '/issue-report',
          component: ReportIssue,
        },
        {
          icon: SupportIcon,
          label: t('sidebar.contact'),
          route: '/support',
          component: Contact,
        },
      ],
    },
  ];
};
