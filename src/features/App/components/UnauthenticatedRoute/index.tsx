import React from 'react';

import Login from 'features/Login';
import ResetPassword from 'features/ResetPassword';

import SwitchRoute, { PageRoute } from 'components/SwitchRoute';

import { UnauthenticatedRouteMap } from './constants';

const UnauthenticatedRoute = () => {
  const unauthenticatedRoute: PageRoute[] = [
    {
      path: UnauthenticatedRouteMap.SignIn,
      component: Login,
      exact: true,
    },
    {
      path: UnauthenticatedRouteMap.Resetpassword,
      component: ResetPassword,
    },
  ];

  return <SwitchRoute routes={unauthenticatedRoute} defaultRoute='/login' />;
};

export default UnauthenticatedRoute;
