import { useCallback, useEffect, useMemo, useState } from 'react';

import { Unsubscribe, onMessage } from 'firebase/messaging';
import { isNil } from 'lodash';

import { useLoginData } from 'features/Login/hooks/useLoginData';
import { TOKEN_KEY, getCookie } from 'services/cookies';
import { getMessagingToken, messaging } from 'services/firebase';

import AuthenticatedRoute from './components/AuthenticatedRoute';
import UnauthenticatedRoute from './components/UnauthenticatedRoute';
import { useFirebaseNotification } from './hooks/useFirebaseNotification';

const App = () => {
  const { addNotification } = useFirebaseNotification();
  const { token } = useLoginData();
  const cookiesToken = getCookie(TOKEN_KEY);

  const localToken = useMemo(() => {
    return cookiesToken || token;
  }, [token, cookiesToken]);

  const [isAuthenticated, setIsAuthenticated] = useState<boolean>();

  const checkCookies = useCallback(() => {
    if (localToken) {
      setIsAuthenticated(true);
    } else {
      setIsAuthenticated(false);
    }
  }, [localToken]);

  useEffect(() => {
    checkCookies();
  }, [checkCookies]);

  // Initialize messaging token when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      getMessagingToken();
    }
  }, [isAuthenticated]);

  // Listen for foreground messages with proper cleanup
  useEffect(() => {
    if (!messaging || !isAuthenticated) return;

    const unsubscribe: Unsubscribe = onMessage(messaging, payload => {
      console.log({ payload });
      addNotification({
        id: payload.messageId || Date.now().toString(),
        content:
          payload.notification?.title ?? payload.notification?.body ?? '',
      });
    });

    return () => {
      unsubscribe();
    };
  }, [addNotification, isAuthenticated]);

  if (isNil(isAuthenticated)) return null;

  return (
    <>{isAuthenticated ? <AuthenticatedRoute /> : <UnauthenticatedRoute />}</>
  );
};

export default App;
