import { useMutation } from '@tanstack/react-query';

import { createApiMutationFn } from 'services/reactQuery';

import showToast from 'components/RRToastMessage/Toast';

export interface ResetPasswordForm {
  password: string;
  password_confirmation: string;
}

interface ResetPasswordResponse {
  message: string;
  success: boolean;
}

export const useResetPassword = () => {
  return useMutation<ResetPasswordResponse, Error, ResetPasswordForm>({
    mutationFn: createApiMutationFn<ResetPasswordResponse, ResetPasswordForm>(
      data => ({
        method: 'PATCH',
        route: '/users/password',
        data: {
          password_confirmation: data.password_confirmation,
          password: data.password,
        },
      }),
    ),
    onSuccess: () => {
      showToast('success', 'Đặt lại mật khẩu thành công');
    },
  });
};
