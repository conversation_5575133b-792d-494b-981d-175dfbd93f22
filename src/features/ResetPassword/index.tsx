import { useEffect, useState } from 'react';

import { yupResolver } from 'hooks/useYupResolver';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import tw from 'tailwind-styled-components';

import AppDownloadBanner from 'features/Login/components/AppDownloadBanner';

import images from 'assets/images';
import { RRFieldPassword } from 'components';
import { PrimaryButton, SecondaryButton } from 'components/Button';

import { useResetPassword } from './hooks/useResetMutations';
import { resetPasswordSchema } from './schema';
import { ResetPasswordForm } from './types';

interface LoginProps {}

const Login = ({}: LoginProps) => {
  const { t } = useTranslation();
  const [resetSuccess, setResetSuccess] = useState(false);

  // Use react-query mutation instead of redux
  const resetPasswordMutation = useResetPassword();

  const {
    control,
    handleSubmit,
    formState,
    reset,
    formState: { errors },
  } = useForm<ResetPasswordForm>({
    mode: 'onBlur',
    resolver: yupResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      password_confirmation: '',
    },
  });

  const onSubmit = (data: ResetPasswordForm) => {
    resetPasswordMutation.mutate(data, {
      onSuccess: () => {
        setResetSuccess(true);
      },
    });
  };

  useEffect(() => {
    // enter key press
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleSubmit(onSubmit)();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <Container>
      <WelComeSection
        style={{
          backgroundImage: `url(${images.Login.SignInWelcome})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <AppDownloadBanner />
      </WelComeSection>
      <div className='flex w-1/2 flex-col items-center justify-center'>
        <div className='items-center` mb-10 flex flex-col'>
          <h1 className='text-2xl font-bold text-black-1000'>
            Đặt lại mật khẩu
          </h1>
        </div>
        <form
          className='flex w-full max-w-[360px] flex-col gap-3'
          onSubmit={handleSubmit(onSubmit)}
          autoComplete='off'
        >
          <RRFieldPassword
            control={control}
            label='Nhập mật khẩu mới'
            id='password'
            name='password'
            placeholder='Nhập mật khẩu mới'
            prefixIcon={images.Icon.Lock}
            errors={errors}
            autoComplete='new-password'
          />
          <RRFieldPassword
            control={control}
            label='Nhập lại mật khẩu mới'
            id='password_confirmation'
            name='password_confirmation'
            placeholder='Nhập lại mật khẩu mới'
            prefixIcon={images.Icon.Lock}
            errors={errors}
            autoComplete='new-password-confirm'
          />
          <PrimaryButton
            htmlType='submit'
            className='mt-7'
            disabled={resetPasswordMutation.isPending || !formState.isValid}
            loading={resetPasswordMutation.isPending}
          >
            {t('login.login')}
          </PrimaryButton>
          <SecondaryButton
            htmlType='button'
            onClick={() => {
              window.location.href = '/overview';
            }}
            disabled={!resetSuccess}
          >
            Về trang chủ
          </SecondaryButton>
        </form>
      </div>
    </Container>
  );
};

export default Login;

const Container = tw.div`flex h-screen`;
const WelComeSection = tw.div`relative w-1/2 flex-wrap items-center justify-center`;
