import * as yup from 'yup';

// Base validation schemas
export const baseValidations = {
  username: yup.string().required('<PERSON><PERSON><PERSON> khoản là bắt buộc'),

  password: yup
    .string()
    .required('<PERSON><PERSON><PERSON> khẩu là bắt buộc')
    .min(6, '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự'),

  passwordConfirmation: yup
    .string()
    .required('Xác nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], '<PERSON>ật khẩu xác nhận không khớp'),
};

// Create Account Schema (Normal User)
export const resetPasswordSchema = yup.object({
  username: baseValidations.username,
  password: baseValidations.password,
  password_confirmation: baseValidations.passwordConfirmation,
});

export type LoginForm = yup.InferType<typeof resetPasswordSchema>;
