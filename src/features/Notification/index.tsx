/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from 'react';

import { Button, List } from 'antd';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyLg, FontMedium, FontRegular, H4 } from 'assets/styles';
import { Icon, RRConfirmationModal } from 'components';
import { SecondaryButton } from 'components/Button';
import EmptyPanel from 'components/EmptyPanel';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import HeadlineBox from './HeadlineBox';
import NotificationOptionSetting from './NotificationOptionSetting';
import { useAllNotifications } from './hooks/useNotiData';
import { useNotiMutations } from './hooks/useNotiMutations';
import { ReportData } from './types';

interface NotificationDeviceProps {}

const NotificationItem = ({ isReaded, onClickItem, content }) => {
  return (
    <div
      className={`relative box-border flex w-full flex-col items-start justify-start rounded-xl px-3 text-left text-sm font-normal text-black-1000 hover:bg-grey-50 ${
        isReaded ? 'opacity-50' : ''
      }`}
      onClick={onClickItem}
    >
      <div className='flex w-full flex-row items-center justify-center border-b border-solid border-grey-100 px-0 py-3'>
        <div className='flex-1 cursor-pointer leading-[24px]'>{content}</div>
      </div>
    </div>
  );
};

const NotificationDevice: React.FC<NotificationDeviceProps> = () => {
  const { profile } = useProfile();
  const [currentPage, setCurrentPage] = useState(1);

  // React Query hooks
  const { unreadNotifications, readNotifications, isLoading, refetch } =
    useAllNotifications(currentPage);

  const { deleteAllRead, markAsRead, markAllAsRead } = useNotiMutations();

  const [selectedReport, setSelectedReport] = useState<ReportData | undefined>(
    undefined,
  );
  const [readIdx, setReadIdx] = useState<number[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showNotificationOptions, setShowNotificationOptions] =
    useState<boolean>(false);

  const markToReadAll = () => {
    const unreadIds = unreadNotifications.map(notification => notification.id);
    markAllAsRead({ ids: unreadIds });
  };

  const handleReportItemClick = useCallback(
    (item: ReportData) => {
      setSelectedReport(item);
      setReadIdx(prev => {
        const newReadIdx = [...prev, +item.id];
        return newReadIdx;
      });
      markAsRead({ ids: [item.id] });
    },
    [markAsRead],
  );

  const handleConfirm = () => {
    if (selectedReport) {
      deleteAllRead({
        readIds: [selectedReport.id],
      });
    }
    setIsModalVisible(false);
    setSelectedReport(undefined);
  };

  const deleteReadAll = () => {
    const readIds = readNotifications.map(notification => notification.id);
    deleteAllRead({ readIds });
  };

  // Refetch when page changes
  useEffect(() => {
    refetch();
  }, [currentPage, refetch]);

  return (
    <Container>
      <MainContainer className='relative flex h-full w-1/3 flex-col'>
        <Header>
          <OverviewSection className='flex-1'>
            <GreetingText className={BodyLg}>
              Xin chào {profile?.full_name}
            </GreetingText>
            <div className='flex flex-1 flex-row justify-between'>
              <OverviewText className={H4}>{t('sidebar.noti')}</OverviewText>
              <SecondaryButton
                size='small'
                iconPosition='left'
                className='border-none'
                icon={<Icon src={images.Icon.Setting} alt='Settings' />}
                onClick={() => setShowNotificationOptions(true)}
              />
            </div>
          </OverviewSection>
        </Header>
        <div className='flex h-full flex-1 flex-col'>
          <div className='h-full max-h-[40%]'>
            <HeadlineBox
              className='mt-6'
              title='Chưa đọc (0)'
              leftText='Đọc tất cả'
              prefixIcon={images.Icon.MailClose}
              onClick={markToReadAll}
            />
            {unreadNotifications?.length === 0 ? (
              <div className='flex h-[240px] items-center justify-center'>
                <EmptyPanel />
              </div>
            ) : (
              <RRScrollView className='ml-[-12px] h-[calc(100%-48px)] w-[calc(100%+24px)]'>
                <div className='size-full overflow-x-hidden '>
                  <List
                    className='h-full'
                    dataSource={unreadNotifications}
                    loading={isLoading}
                    renderItem={item => {
                      return (
                        <NotificationItem
                          key={item.id}
                          isReaded={false}
                          onClickItem={() => handleReportItemClick(item)}
                          content={item.content}
                        />
                      );
                    }}
                  />
                </div>
              </RRScrollView>
            )}
          </div>
          <div className='h-full'>
            <HeadlineBox
              className='mt-6'
              title='Đã đọc (0)'
              leftText='Xóa tất cả'
              prefixIcon={images.Icon.MailOpen}
              onClick={deleteReadAll}
            />
            {readNotifications?.length === 0 ? (
              <div className='flex h-[240px] items-center justify-center'>
                <EmptyPanel />
              </div>
            ) : (
              <RRScrollView className='ml-[-12px] h-[calc(100%-48px)] w-[calc(100%+24px)]'>
                <div className='size-full overflow-x-hidden '>
                  <List
                    className='h-full'
                    dataSource={readNotifications}
                    loading={isLoading}
                    renderItem={item => {
                      return (
                        <NotificationItem
                          key={item.id}
                          isReaded={true}
                          onClickItem={() => handleReportItemClick(item)}
                          content={item.content}
                        />
                      );
                    }}
                  />
                </div>
              </RRScrollView>
            )}
          </div>
        </div>
      </MainContainer>
      <RightContainer>
        {!selectedReport && (
          <div className='flex flex-col items-center justify-center gap-6'>
            <img
              className='size-40'
              src={images.Icon.WaitingMap}
              alt='Waiting'
            />
            <div className='font-medium leading-[24px] text-grey-600'>
              Chọn thông báo để xem chi tiết trên bản đồ
            </div>
          </div>
        )}
        {selectedReport && (
          <div className='absolute top-6 mx-4 flex flex-row items-center gap-4 rounded-xl border border-grey-100 bg-white-1000 px-4 py-3'>
            <div
              className={`flex-1 text-black-1000 ${FontMedium} ${FontRegular}`}
            >
              {selectedReport.content}
            </div>
            <Button
              shape='circle'
              icon={<Icon src={images.Icon.Trash} />}
              onClick={() => setIsModalVisible(true)}
              className='border-none bg-red-10 shadow-none hover:bg-red-alpha20'
            />
          </div>
        )}
      </RightContainer>
      {showNotificationOptions && (
        <NotificationOptionSetting
          visible={showNotificationOptions}
          onClose={() => setShowNotificationOptions(false)}
        />
      )}

      <RRConfirmationModal
        title={t('notification.confirmDeleteTitle')}
        message={t('notification.confirmDeleteMessage')}
        onCancel={() => setIsModalVisible(false)}
        onConfirm={handleConfirm}
        visible={isModalVisible}
      />
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex size-full flex-1 flex-col items-center justify-center border-l border-solid border-grey-100 p-3 text-base font-medium text-grey-600`;
const MainContainer = tw.div`p-6`;
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col gap-2`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;

export default NotificationDevice;
