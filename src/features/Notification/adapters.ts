/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReportData } from './types';

export const transformNotifications = (
  notifications: any[],
  isRead: boolean,
) => {
  const unreadDataList: ReportData[] = [];
  const readDataList: ReportData[] = [];

  notifications.forEach((notification, index) => {
    const formattedNotification: ReportData = {
      id: notification.id,
      content: `${notification.title} / ${notification.content} (${new Date(
        notification.created_at,
      ).toLocaleString()})`,
      type: isRead ? 'read' : 'unread',
    };

    if (formattedNotification.type === 'unread') {
      unreadDataList.push(formattedNotification);
    } else {
      readDataList.push(formattedNotification);
    }
  });

  return { unreadDataList, readDataList };
};
