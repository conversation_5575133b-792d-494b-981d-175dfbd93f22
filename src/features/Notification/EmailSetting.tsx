import React, { useEffect, useState } from 'react';

import { Modal } from 'antd';
import dayjs from 'dayjs';
import { useForm } from 'react-hook-form';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { Colors } from 'assets/styles';
import { RRFieldInput } from 'components';

export interface EmailSettingForm {
  email: string;
  otp: string;
}
const COUNT_DOWN_SECOND_RESEND_CODE = 120;
const EmailSetting: React.FC<{
  visible?: boolean;
  onClose?: () => void;
  onFinished?: (email: string) => void;
}> = ({ visible, onClose, onFinished }) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
    resetField,
    getValues,
    reset,
  } = useForm<EmailSettingForm>({});
  const [isSubmited, setIsSubmited] = useState(false);
  const [showCountDown, setShowCountDown] = useState<number>(0);
  const handleSubmitForm = (data: EmailSettingForm) => {
    if (data.email?.trim()?.length) {
      setIsSubmited(true);
      setShowCountDown(prev =>
        prev > 0 ? prev : COUNT_DOWN_SECOND_RESEND_CODE,
      );
    }
    if (data.otp?.trim().length) {
      onFinished?.(data.email);
    }
    resetField('otp');
  };
  const handleResendCode = () => {
    if (showCountDown === 0) {
      setShowCountDown(COUNT_DOWN_SECOND_RESEND_CODE);
    }
  };
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showCountDown > 0) {
      timer = setInterval(() => {
        setShowCountDown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [showCountDown]);

  return (
    <Modal
      styles={{ header: { fontSize: 20 } }}
      open={visible}
      title={t('notification.emailSettingHeader')}
      onCancel={onClose}
      afterClose={() => {
        reset();
        setIsSubmited(false);
      }}
      footer={null}
      centered
    >
      <EmailForm onSubmit={handleSubmit(handleSubmitForm)}>
        <Container>
          <Content>
            <RRFieldInput
              id='email'
              control={control}
              label=''
              prefixIcon={images.Icon.MailClose}
              className='flex w-full flex-1'
              placeholder={t('notification.emailInputPlaceholder')}
              errors={errors}
            />
            {isSubmited && (
              <RRFieldInput
                id='otp'
                control={control}
                label=''
                prefixIcon={images.Icon.Lock}
                className='mt-4 flex w-full flex-1'
                placeholder={t('login.otpPlaceholder')}
                errors={errors}
              />
            )}
          </Content>
          <ConfirmButton
            type='submit'
            style={{
              backgroundColor: Colors.brand[300],
              color: Colors.black[1000],
              flex: 1,
              display: 'flex',
              borderRadius: 8,
              paddingTop: 8,
              paddingBottom: 8,
              fontSize: 14,
              textAlign: 'center',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {t('notification.confirmButton')}
          </ConfirmButton>
          {isSubmited && (
            <ResendButton onClick={handleResendCode}>
              {`${t('notification.resendLabel')}(${dayjs
                .duration(showCountDown, 'second')
                .format('mm:ss')})`}
            </ResendButton>
          )}
        </Container>
      </EmailForm>
    </Modal>
  );
};
const EmailForm = tw.form`flex flex-1`;
const ConfirmButton = tw.button``;
const Container = tw.div`bg-red-1000 flex flex-1 flex-col`;
const Content = tw.div`flex flex-1 flex-col py-3`;
const ResendButton = tw.div`mt-4 text-center leading-[24px] text-grey-600`;
export default EmailSetting;
