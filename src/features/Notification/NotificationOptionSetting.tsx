import React, { useState } from 'react';

import { Drawer } from 'antd';
import { xor } from 'lodash';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdExtend, FontBold, TitleMd } from 'assets/styles';
import { Icon } from 'components';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import './Notification.css';

interface NotificationOptionSettingProps {
  visible: boolean;
  onClose: () => void;
}

const commandList = [
  'Kết nối thiết bị',
  'Mở khóa trái phép',
  'Khởi động lại thiết bị',
  'Mở nắp thiết bị',
  'Kết nối thiết bị ngoại vi',
  'Tín hiệu vệ tinh',
  '<PERSON> chuyển',
  'V<PERSON><PERSON>t quá tốc độ',
  '<PERSON>ùng địa lý',
  '<PERSON><PERSON><PERSON><PERSON> điện',
  'Phò<PERSON> vệ',
  '<PERSON><PERSON> lắ<PERSON>, va chạm',
  'SOS',
  'Cảnh báo đóng/mở cửa',
];

const NotificationOptionSetting: React.FC<NotificationOptionSettingProps> = ({
  visible,
  onClose,
}) => {
  const [selectedKey, setSelectedKey] = useState<string[]>([]);

  return (
    <Container>
      <Drawer
        className={`notification-option no-transition`}
        closable={false}
        open={visible}
      >
        <Content>
          <div className='flex flex-1 flex-col border-r border-grey-100 px-4 py-6'>
            <Header>
              <div
                className={`${BodyMdExtend} ${FontBold} ${TitleMd} leading-7`}
              >
                {t('notification.optionSettingLabel')}
              </div>
              <div className='ant-modal-close bg-white'>
                <Icon src={images.Icon.X} onClick={onClose} />
              </div>
            </Header>
            <RRScrollView>
              {commandList.map(item => {
                const checked = selectedKey.includes(item);
                return (
                  <div
                    key={item}
                    role='button'
                    className='flex cursor-pointer flex-row items-center justify-start self-stretch  border-b-[0.5px] border-solid border-grey-100 px-2 py-3 hover:bg-grey-50'
                    onClick={() => {
                      setSelectedKey(pre => xor(pre, [item]));
                    }}
                  >
                    <span className='relative w-full font-medium leading-[24px]'>
                      {item}
                    </span>
                    {checked && <Icon src={images.Icon.CheckMark} />}
                  </div>
                );
              })}
            </RRScrollView>
          </div>
        </Content>
      </Drawer>
    </Container>
  );
};

export default NotificationOptionSetting;

const Container = tw.div`flex h-screen `;
const Content = tw.div`flex h-full overflow-y-hidden`;
const Header = tw.div`flex justify-between px-2 mb-2`;
