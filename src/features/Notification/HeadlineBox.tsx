import { FC } from 'react';

import tw from 'tailwind-styled-components';

import { LinkButton } from '../../components/Button';
import Icon from '../../components/Icon';

export type HeadlineBoxProps = {
  title: string;
  leftText?: string;
  prefixIcon?: string;
  className?: string;
  onClick?: () => void;
};
const HeadlineBox: FC<HeadlineBoxProps> = ({
  title,
  leftText,
  prefixIcon,
  className,
  onClick,
}) => {
  return (
    <Container className={className}>
      <div className='flex flex-row items-center justify-start gap-2'>
        {prefixIcon ? <Icon src={prefixIcon} className='size-5' /> : null}
        <div className='flex flex-row items-center justify-start'>
          <div className='relative font-medium leading-[24px]'>{title}</div>
        </div>
      </div>
      <LinkButton onClick={onClick} size='small' className='h-6 text-end'>
        {leftText}
      </LinkButton>
    </Container>
  );
};
const Container = tw.div`relative box-border flex w-full flex-row items-center justify-between text-left text-sm font-medium text-grey-400`;
export default HeadlineBox;
