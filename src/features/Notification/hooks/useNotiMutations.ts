import { useMutation, useQueryClient } from '@tanstack/react-query';

import { callApi } from 'services/api/api';

import { ReportData } from '../types';
import { NOTIFICATIONS_QUERY_KEY } from './useNotiData';

interface NotificationsData {
  unreadNotifications: ReportData[];
  readNotifications: ReportData[];
  page: number;
  total_pages: number;
  total_count: number;
}

// Mutation để xóa tất cả notifications đã đọc
export const useDeleteAllRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ readIds }: { readIds: string[] }) => {
      const response = await callApi({
        method: 'delete',
        route: '/notifications/delete-all',
        data: { ids: readIds },
      });

      if (!response.success) {
        throw new Error('Failed to delete read notifications');
      }

      return response;
    },
    onMutate: async ({ readIds }) => {
      // Cancel ongoing queries
      await queryClient.cancelQueries({ queryKey: [NOTIFICATIONS_QUERY_KEY] });

      // Snapshot previous value
      const previousData = queryClient.getQueriesData({
        queryKey: [NOTIFICATIONS_QUERY_KEY],
      });

      // Optimistically update read notifications
      queryClient.setQueriesData(
        {
          queryKey: [NOTIFICATIONS_QUERY_KEY],
          predicate: query => {
            const [, , isRead] = query.queryKey;
            return isRead === true;
          },
        },
        (old: NotificationsData | undefined) => {
          if (!old) return old;
          return {
            ...old,
            readNotifications: old.readNotifications.filter(
              notification => !readIds.includes(notification.id),
            ),
            total_count: old.total_count - readIds.length,
          };
        },
      );

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        context.previousData.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      console.error('Error deleting read notifications:', err);
    },
    onSuccess: () => {
      console.log('Successfully deleted read notifications');
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: [NOTIFICATIONS_QUERY_KEY] });
    },
  });
};

// Mutation để đánh dấu notification là đã đọc
export const useMarkAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ ids }: { ids: string[] }) => {
      const response = await callApi({
        method: 'post',
        route: '/notifications/read',
        data: { ids },
      });

      if (!response.success) {
        throw new Error('Failed to mark notifications as read');
      }

      return response;
    },
    onSuccess: (data, { ids }) => {
      console.log('Successfully marked notifications as read');

      // Invalidate queries để refetch data mới
      queryClient.invalidateQueries({ queryKey: [NOTIFICATIONS_QUERY_KEY] });
    },
    onError: err => {
      console.error('Error marking notifications as read:', err);
    },
  });
};

// Mutation để đánh dấu tất cả là đã đọc
export const useMarkAllAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ ids }: { ids: string[] }) => {
      const response = await callApi({
        method: 'post',
        route: '/notifications/read',
        data: { ids },
      });

      if (!response.success) {
        throw new Error('Failed to mark all notifications as read');
      }

      return response;
    },
    onMutate: async ({ ids }) => {
      // Cancel ongoing queries
      await queryClient.cancelQueries({ queryKey: [NOTIFICATIONS_QUERY_KEY] });

      // Snapshot previous value
      const previousData = queryClient.getQueriesData({
        queryKey: [NOTIFICATIONS_QUERY_KEY],
      });

      // Optimistically move all unread to read
      const unreadData = queryClient.getQueryData<NotificationsData>([
        NOTIFICATIONS_QUERY_KEY,
        1,
        false,
      ]);

      if (unreadData) {
        const movedNotifications = unreadData.unreadNotifications.map(
          notification => ({
            ...notification,
            type: 'read' as const,
          }),
        );

        // Update unread notifications
        queryClient.setQueryData(
          [NOTIFICATIONS_QUERY_KEY, 1, false],
          (old: NotificationsData | undefined) => {
            if (!old) return old;
            return {
              ...old,
              unreadNotifications: [],
              total_count: 0,
            };
          },
        );

        // Update read notifications
        queryClient.setQueryData(
          [NOTIFICATIONS_QUERY_KEY, 1, true],
          (old: NotificationsData | undefined) => {
            if (!old) return old;
            return {
              ...old,
              readNotifications: [
                ...movedNotifications,
                ...old.readNotifications,
              ],
              total_count: old.total_count + movedNotifications.length,
            };
          },
        );
      }

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        context.previousData.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
      console.error('Error marking all notifications as read:', err);
    },
    onSuccess: () => {
      console.log('Successfully marked all notifications as read');
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: [NOTIFICATIONS_QUERY_KEY] });
    },
  });
};

// Hook tổng hợp tất cả mutations
export const useNotiMutations = () => {
  const deleteAllRead = useDeleteAllRead();
  const markAsRead = useMarkAsRead();
  const markAllAsRead = useMarkAllAsRead();

  return {
    deleteAllRead: deleteAllRead.mutate,
    deleteAllReadAsync: deleteAllRead.mutateAsync,
    isDeleting: deleteAllRead.isPending,

    markAsRead: markAsRead.mutate,
    markAsReadAsync: markAsRead.mutateAsync,
    isMarkingAsRead: markAsRead.isPending,

    markAllAsRead: markAllAsRead.mutate,
    markAllAsReadAsync: markAllAsRead.mutateAsync,
    isMarkingAllAsRead: markAllAsRead.isPending,
  };
};
