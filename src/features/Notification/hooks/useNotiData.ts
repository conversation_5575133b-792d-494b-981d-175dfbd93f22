import { useQuery, useQueryClient } from '@tanstack/react-query';

import { callApi } from 'services/api/api';

import { transformNotifications } from '../adapters';
import { ReportData } from '../types';

interface NotificationResponse {
  notifications: any[];
  page: number;
  total_pages: number;
  total_count: number;
}

interface UseNotificationsParams {
  page: number;
  isRead: boolean;
  enabled?: boolean;
}

interface NotificationsData {
  unreadNotifications: ReportData[];
  readNotifications: ReportData[];
  page: number;
  total_pages: number;
  total_count: number;
}

export const NOTIFICATIONS_QUERY_KEY = 'notifications';

export const useNotifications = ({
  page,
  isRead,
  enabled = true,
}: UseNotificationsParams) => {
  return useQuery({
    queryKey: [NOTIFICATIONS_QUERY_KEY, page, isRead],
    queryFn: async (): Promise<NotificationsData> => {
      const response = await callApi({
        method: 'get',
        route: `/notifications?page=${page}&type=${isRead ? 'read' : 'unread'}`,
      });

      if (response.success) {
        const {
          unreadDataList: unreadNotifications,
          readDataList: readNotifications,
        } = transformNotifications(
          response.response.data.notifications,
          isRead,
        );

        const { total_pages, total_count } = response.response.data;

        return {
          unreadNotifications,
          readNotifications,
          page,
          total_pages,
          total_count,
        };
      }

      throw new Error('Failed to fetch notifications');
    },
    enabled,
    staleTime: 30000, // 30 seconds
  });
};

// Hook để lấy cả unread và read notifications
export const useAllNotifications = (page: number = 1) => {
  const unreadQuery = useNotifications({ page, isRead: false });
  const readQuery = useNotifications({ page, isRead: true });

  return {
    unreadNotifications: unreadQuery.data?.unreadNotifications || [],
    readNotifications: readQuery.data?.readNotifications || [],
    isLoading: unreadQuery.isLoading || readQuery.isLoading,
    isError: unreadQuery.isError || readQuery.isError,
    totalPages: Math.max(
      unreadQuery.data?.total_pages || 1,
      readQuery.data?.total_pages || 1,
    ),
    totalUnread: unreadQuery.data?.total_count || 0,
    totalRead: readQuery.data?.total_count || 0,
    refetch: () => {
      unreadQuery.refetch();
      readQuery.refetch();
    },
  };
};
