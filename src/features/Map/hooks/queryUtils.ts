// Common configurations and factory functions for Map queries
import { UseMutationOptions, UseQueryOptions } from '@tanstack/react-query';

import { qsStringifyUrl } from 'services/api/utils';
import { createApiMutationFn, createApiQueryFn } from 'services/reactQuery';

// Common stale times based on data frequency
export const STALE_TIMES = {
  REALTIME: 10000, // 10 seconds - for realtime data
  FREQUENT: 30000, // 30 seconds - for frequently changing data
  MODERATE: 300000, // 5 minutes - for moderately changing data
  STABLE: 600000, // 10 minutes - for stable data
  STATIC: 3600000, // 1 hour - for static data
} as const;

// Common query options factory
export const createQueryOptions = (
  staleTime: number,
): Partial<UseQueryOptions> => ({
  staleTime,
  refetchOnWindowFocus: false,
  retry: 3,
  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
});

// Device-related query factory
export const createDeviceQuery = (
  endpoint: string,
  params: Record<string, any>,
  staleTime: number = STALE_TIMES.MODERATE,
) => ({
  queryFn: createApiQueryFn({
    method: 'GET',
    route: qsStringifyUrl({
      url: endpoint,
      query: params,
    }),
  }),
  ...createQueryOptions(staleTime),
});

// IMEI-based query factory
export const createImeiQuery = (
  imei: string,
  endpoint: string,
  additionalParams: Record<string, any> = {},
  staleTime: number = STALE_TIMES.MODERATE,
) => ({
  queryFn: createApiQueryFn({
    method: 'GET',
    route: qsStringifyUrl({
      url: endpoint.replace(':imei', imei),
      query: additionalParams,
    }),
  }),
  enabled: !!imei,
  ...createQueryOptions(staleTime),
});

// Common mutation factory
export const createMutation = <TData, TVariables>(
  mutationFn: (variables: TVariables) => any,
  options: Partial<UseMutationOptions<TData, Error, TVariables>> = {},
) => ({
  mutationFn: createApiMutationFn(mutationFn),
  ...options,
});

export const buildDateRangeParams = (startDate: string, endDate: string) => ({
  start_date: startDate,
  end_date: endDate,
});

// Type-safe parameter builders
export const sanitizeOptionalParams = (
  params: Record<string, any>,
): Record<string, string> => {
  const result: Record<string, string> = {};
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        if (value.length > 0) result[key] = value.join(',');
      } else {
        result[key] = String(value);
      }
    }
  });
  return result;
};
