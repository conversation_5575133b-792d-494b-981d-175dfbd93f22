import { useCallback, useReducer } from 'react';

import { DeviceItemActionMapType } from 'components/DeviceHistory/types';
import { CollapseItemData } from 'components/RRCollapseTable';

import { MapUIActionType } from '../types/ui-state.types';
import { initialMapUIState, mapUIReducer } from './mapUIReducer';

export const useMapState = () => {
  // UI State Management với useReducer
  const [state, dispatch] = useReducer(mapUIReducer, initialMapUIState);

  // Mutations

  const setSelectedFilterAccount = useCallback(
    (account: CollapseItemData | null) => {
      dispatch({
        type: MapUIActionType.SET_SELECTED_FILTER_ACCOUNT,
        payload: account,
      });
    },
    [],
  );

  const closeDeviceDetail = useCallback(() => {
    dispatch({
      type: MapUIActionType.TOGGLE_DEVICE_DETAIL,
      // Ensure it closes and resets currentAction
    });
  }, []);

  const setShowAreaVisible = useCallback((visible?: boolean) => {
    dispatch({
      type: MapUIActionType.TOGGLE_AREA_MODAL,
      payload: visible,
    });
  }, []);

  const setShowCommandBox = useCallback((visible?: boolean) => {
    dispatch({
      type: MapUIActionType.TOGGLE_COMMAND_BOX,
      payload: visible,
    });
  }, []);

  const resetToDeviceList = useCallback(() => {
    dispatch({ type: MapUIActionType.RESET_TO_DEVICE_LIST });
  }, []);

  // Main handler for device item actions
  const handleItemActionClick = useCallback(
    (actionType: DeviceItemActionMapType) => {
      dispatch({
        type: MapUIActionType.HANDLE_DEVICE_ITEM_ACTION,
        payload: actionType,
      });
    },
    [],
  );

  return {
    // UI State
    state,

    // Individual state properties (for easier access)
    showDeviceDetailVisible: state.showDeviceDetailVisible,
    showAreaVisible: state.showAreaVisible,
    showDeviceHistory: state.showDeviceHistory,
    isShowCommandBox: state.isShowCommandBox,
    currentDeviceItemAction: state.currentDeviceItemAction,

    // UI Actions
    setSelectedFilterAccount,
    closeDeviceDetail,
    setShowAreaVisible,
    setShowCommandBox,
    resetToDeviceList,
    handleItemActionClick,
  };
};

export type UseMapStateReturn = ReturnType<typeof useMapState>;
