import React, { useCallback, useEffect, useRef, useState } from 'react';

import { InputRef } from 'antd';
import { debounce } from 'lodash';
import { Scrollbars } from 'react-custom-scrollbars';

import { useMapMutations } from 'features/Map/hooks/useMapMutations';
import { useMessageTransformation } from 'features/Map/hooks/useMapUtils';
import { CommandMessage } from 'features/Map/types';

// Simplified hook using utils
const useCommandBox = (
  imei: string,
  onLoadMoreMsg: (page: number) => void,
  page: number,
  initialCommands: CommandMessage[] = [],
) => {
  const { sendCommand } = useMapMutations();

  // States
  const [commandInput, setCommandInput] = useState('');
  const [activeButton, setActiveButton] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Refs
  const inputRef = useRef<InputRef>(null);
  const scrollRef = useRef<Scrollbars>(null);

  // Use utility hook for message transformation
  const messagesByDate = useMessageTransformation(initialCommands);

  // Auto scroll on message changes
  useEffect(() => {
    setTimeout(() => scrollRef.current?.scrollToBottom(), 0);
  }, [messagesByDate]);

  // Handlers
  const handleChange = useCallback(
    debounce(
      (e: React.ChangeEvent<HTMLInputElement>) =>
        setCommandInput(e.target.value),
      1,
    ),
    [],
  );

  const handleSendCommand = useCallback(() => {
    if (!commandInput.trim()) return;

    sendCommand.mutate(
      { imei, command: commandInput },
      { onSuccess: () => scrollRef.current?.scrollToBottom() },
    );
    setCommandInput('');
  }, [commandInput, sendCommand, imei]);

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLElement>) => {
      if (e.currentTarget.scrollTop === 0 && !isLoadingMore) {
        setIsLoadingMore(true);
        onLoadMoreMsg(page + 1);
        setTimeout(() => setIsLoadingMore(false), 500);
      }
    },
    [isLoadingMore, onLoadMoreMsg, page],
  );

  const handleListItemClick = useCallback((item: string) => {
    setCommandInput(item);
    inputRef.current?.focus();
  }, []);

  return {
    messagesByDate,
    commandInput,
    activeButton,
    inputRef,
    scrollRef,
    handleChange,
    handleSendCommand,
    handleScroll,
    setActiveButton,
    handleListItemClick,
  };
};

export default useCommandBox;
