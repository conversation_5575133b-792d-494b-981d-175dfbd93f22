import { useEffect, useMemo, useState } from 'react';

import { Dayjs } from 'dayjs';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import DeviceListPanel from 'features/DeviceBusiness/components/DeviceListPanel';
import { useDeviceDetail, useDeviceState } from 'features/DeviceBusiness/hooks';
import { useMapGeofences } from 'features/Map/hooks/useMapData';
import { useMapState } from 'features/Map/hooks/useMapState';
import { getRoutePointData } from 'features/Map/hooks/useMapUtils';
import { DataItem } from 'features/Map/types';

import DeviceHistory from 'components/DeviceHistory/DeviceHistory';
import { DrawerDeviceDetail } from 'components/DeviceModal';
import RRMap from 'components/RRMap';

import CommandBox from './components/CommandBox';
import RadiusModal from './components/RadiusModal';
import { useDeviceListRealtime } from './hooks/useMapData';

const Map = () => {
  const { profile: selectedProfile, profile } = useProfile();
  const {
    state,
    openModal,
    updateState,
    onDeviceSelect,
    onSelectAccount,
    onChangeBreadcrumb,
  } = useDeviceState();
  const { selectedDevice, multipleSelect } = state;

  // Combined Map State Management (UI + Data)
  const {
    // State values
    showDeviceDetailVisible,
    showAreaVisible,
    showDeviceHistory,
    isShowCommandBox,
    currentDeviceItemAction,

    // Actions
    setSelectedFilterAccount,
    setShowAreaVisible,
    setShowCommandBox,
    resetToDeviceList,
    handleItemActionClick,
    closeDeviceDetail,
  } = useMapState();

  const { data: geofences } = useMapGeofences({
    imei: selectedDevice?.imei || '',
  });
  const firstGeofence = useMemo(() => {
    return geofences?.[0];
  }, [geofences]);

  const [selectedPreviewDate, setSelectedPreviewDate] = useState<
    Dayjs | undefined
  >(undefined);
  const [currentViewData, setCurrentViewData] = useState<
    DataItem | undefined
  >();
  const [groupsPoint, setGroupsPoint] = useState<any[]>([]);
  const [lostItems, setLostItems] = useState<any[]>([]);

  const [deviceList, setDeviceList] = useState<any>([]);
  // Realtime device signal from device
  const [deviceDetail, setDeviceDetail] = useState<any>(null);
  const deviceImeiList = deviceList.map((item: any) => item.imei);
  const { data: devicesRealTime } = useDeviceListRealtime({
    imeiList: deviceImeiList,
    enabled: deviceImeiList.length > 0 && !showDeviceHistory,
  });

  const handleViewPreview = (previewDate?: Dayjs, item?: DataItem) => {
    setSelectedPreviewDate(prev =>
      prev && prev.isSame(previewDate) ? undefined : previewDate,
    );
    setCurrentViewData(item);

    const routePointData = getRoutePointData(item);
    setGroupsPoint(routePointData.groupsPoint);
    setLostItems(routePointData.lostItems);
  };

  const handleGoBackPreview = () => {
    if (selectedPreviewDate) {
      setSelectedPreviewDate(undefined);
      setCurrentViewData(undefined);
      setGroupsPoint([]);
      setLostItems([]);
    } else {
      resetToDeviceList();
    }
  };

  const handleSelectDevice = device => {
    if (device?.imei === selectedDevice?.imei) {
      return;
    }
    onDeviceSelect(device);
  };

  useEffect(() => {
    setSelectedFilterAccount(
      selectedProfile
        ? {
            id: selectedProfile.id?.toString() ?? '',
            key: selectedProfile.id?.toString() ?? '',
            name: selectedProfile.username || '',
            username: selectedProfile.username || '',
            level: 0,
          }
        : null,
    );
  }, [selectedProfile, setSelectedFilterAccount]);

  useEffect(() => {
    if (devicesRealTime && selectedDevice?.imei) {
      const { devices = [], device_infos = [] } = devicesRealTime || [];
      // realtime device signal
      const tmpDevice = devices.find(item => item.imei === selectedDevice.imei);

      // device info
      const tmpDeviceInfo = device_infos.find(
        item => item.imei === selectedDevice.imei,
      );
      const mergedDevice = {
        ...tmpDeviceInfo,
        ...tmpDevice,
      };
      setDeviceDetail(mergedDevice);
    }
  }, [devicesRealTime, selectedDevice]);

  return (
    <>
      <Container>
        <RRMap
          selectedPreviewDate={selectedPreviewDate}
          selectedDevice={deviceDetail}
          groupsPoint={groupsPoint}
          lostItems={lostItems}
          currentViewData={currentViewData}
          radius={Number(firstGeofence?.radius || 0)}
          currentDeviceItemAction={currentDeviceItemAction}
          onItemActionClick={handleItemActionClick}
        />

        <MainContainer>
          <DeviceListPanel
            isMap={true}
            className='w-[360px] rounded-xl p-4 shadow-panel'
            enableViewAllDevice={false}
            enableAdvanceFilter={false}
            enableAddDevice={false}
            enableSelectAccount={true}
            enableMultipleSelect={false}
            selectedAccount={state.selectedAccount || (profile as any)}
            multipleSelect={multipleSelect}
            profile={profile}
            selectedDevice={deviceDetail}
            onDeviceSelect={handleSelectDevice}
            openModal={openModal}
            updateState={updateState}
            onSelectAccount={onSelectAccount}
            onChangeBreadcrumb={onChangeBreadcrumb}
            onUpdateDeviceList={setDeviceList}
          />
        </MainContainer>

        {deviceDetail && (
          <div className='absolute inset-y-3 left-3 z-20 flex flex-col'>
            <DeviceHistory
              selectedPreviewDate={selectedPreviewDate}
              currentViewData={currentViewData}
              visible={showDeviceHistory}
              device={deviceDetail}
              onViewPreview={handleViewPreview}
              onGoBack={handleGoBackPreview}
            />
          </div>
        )}
        <RadiusModal
          selectedDevice={selectedDevice}
          data={firstGeofence}
          visible={showAreaVisible}
          onClose={() => setShowAreaVisible(false)}
        />
      </Container>
      {/* Modals */}

      {isShowCommandBox && deviceDetail && (
        <CommandBox
          visible={isShowCommandBox}
          onClose={() => setShowCommandBox(false)}
          device={deviceDetail}
        />
      )}

      <DrawerDeviceDetail
        visible={showDeviceDetailVisible}
        onClose={closeDeviceDetail}
        selectedImei={selectedDevice?.imei ?? ''}
      />
    </>
  );
};

export default Map;

const Container = tw.div`relative w-full h-full`;
const MainContainer = tw.div`absolute top-0 left-0 bottom-0 w-[360px] z-20 pointer-events-none flex flex-col p-3 bg-transparent & > * { pointer-events-auto }`;
