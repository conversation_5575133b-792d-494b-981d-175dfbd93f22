interface GPS {
  timestamp: number;
  latitude: number;
  longitude: number;
  locating_information: string;
  satellites: number;
  speed: number;
  distance: number;
  half_of_course: number;
}

interface Device {
  imei: string;
  device_name: string;
  device_description: string;
  gps: GPS;
  device_category: string;
  driver: {
    timestamp: number;
    order_of_driver: number;
    driver_tag: string;
    driver_license_and_name: string;
  };
  status: string;
  device_plate_number: string;
}

export interface DeviceListResponse {
  devices: Device[];
  page: number;
  total_pages: number;
  total_count: number;
}

export interface GeocodeResponse {
  geocode_result?: {
    full_address?: string;
    formatted_address?: string;
    place_id?: string;
    types?: string[];
  };
}

export interface CommandsResponse {
  commands: CommandMessage[];
}

export interface CommandSamplesResponse {
  commands: CommandSample[];
  config_commands: CommandSample[];
}

export interface PageInfo {
  page: number;
  totalPages: number;
  totalCount: number;
}

export interface CommandMessage {
  id: number;
  command_text: string;
  response: string;
  success: boolean;
  status: string;
  created_at: string;
  response_at: string;
  command_name: string;
}

export interface CommandSample {
  id: string;
  name: string;
  message: string;
}

interface StatisticState {
  acc: number;
  door: number;
  ben: number;
  air: number;
  engine: number;
  power: number;
  buzzer: number;
  sos: number;
}

interface Status {
  timestamp: number;
  battery_power_in_voltage: number;
  main_power_in_voltage: number;
  open_door_count: number;
  over_speed_count: number;
  statistic_state: StatisticState;
}

interface Point {
  timestamp: number;
  type: string;
  device_status: string;
  speed: number;
  latitude: number;
  longitude: number;
  distance: number;
  locating_information: string;
  course: number;
  status: Status;
}

interface Route {
  type: string;
  start: Point;
  end: Point;
  points: Point[];
}

export interface DataItem {
  time_from: string;
  time_to: string;
  routes: Route[];
}

export interface HistoryData {
  data: DataItem[];
}

export interface OverviewData {
  lost_connection_time: number;
  max_speed: number;
  move_time: number;
  stop_time: number;
  total_distance: number;
}
