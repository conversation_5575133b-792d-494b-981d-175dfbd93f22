import { DeviceListResponse } from './types';

export const convertToDeviceList = (data: DeviceListResponse): any[] => {
  return data?.devices?.map((device, index) => ({
    id: index + 1,
    deviceName: device.device_name ?? '',
    imei: device.imei ?? '',
    deviceDescription: device.device_description ?? '',
    address: '',
    gps: device?.gps,
    lat: device.gps?.latitude,
    lng: device.gps?.longitude,
    status: device.status,
    speed: '',
    expiredDate: '',
    vehicleInfo: '',
    deviceCategory: device.device_category ?? '',
    plate: device.device_plate_number ?? '',
  }));
};

// Removed converPageInfo as pagination is now handled by React Query
