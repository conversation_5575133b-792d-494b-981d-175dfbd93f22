import React, { useCallback, useState } from 'react';

import { useProfile, useRole } from 'hooks';
import tw from 'tailwind-styled-components';

import AddAccountModal from 'features/AccountBusiness/components/AddAccountModal';
import {
  useAccountData,
  useAccountMutations,
} from 'features/AccountBusiness/hooks';
import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdExtend, FontBold } from 'assets/styles';
import { RRCollapseTable, RRInput } from 'components';
import { RRCollapseTableProps } from 'components/RRCollapseTable';

export interface FilterPanelProps {
  className?: string;
  handleSelect: RRCollapseTableProps['onSelectKey'];
  selectedKey: string;
  hideTitle?: boolean;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  className,
  handleSelect,
  selectedKey,
  hideTitle,
}) => {
  const { profile } = useProfile();
  const { roles } = useRole();
  const [addAccountModalVisible, setAddAccountModalVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Use hooks thay vì saga
  const { accounts } = useAccountData(profile);
  const mutations = useAccountMutations(profile);

  const accountData = accounts.data || [];

  const onCreateAccount = useCallback(
    (data: any) => {
      mutations.createAccount.mutate(data, {
        onSuccess: () => {
          setAddAccountModalVisible(false);
        },
      });
    },
    [mutations.createAccount],
  );

  const onExpand = (id: string) => {
    mutations.expandAccount.mutate({ id });
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  return (
    <Container className={className}>
      {!hideTitle && (
        <Title className={`${BodyMdExtend} ${FontBold}`}>
          {t('map.accountList')}
        </Title>
      )}
      <RRInput
        type='text'
        value={searchTerm}
        onChange={handleSearch}
        prefixIcon={images.Icon.SearchLoupe}
        className='border-gray-300 mb-3 w-full rounded-lg border p-2'
        placeholder={t('map.filterPlaceholder')}
      />
      <RRCollapseTable
        onAddNewItem={() => setAddAccountModalVisible(true)}
        multipleSelectedIds={[selectedKey]}
        onSelectId={handleSelect}
        data={accountData}
        expandIds={[]}
        onExpand={onExpand}
      />
      <AddAccountModal
        visible={addAccountModalVisible}
        onClose={() => {
          setAddAccountModalVisible(false);
        }}
      />
    </Container>
  );
};

export default FilterPanel;

const Container = tw.div`overflow-auto bg-white-1000 rounded-xl border-grey-100 h-full border p-4 w-[360px]`;
const Title = tw.h2`text-lg font-bold mb-4`;
