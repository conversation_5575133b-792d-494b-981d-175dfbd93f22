import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Modal, Slider, Tooltip } from 'antd';
import { Trans } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { useMapMutations } from 'features/Map/hooks/useMapMutations';
import { t } from 'i18next';

import images from 'assets/images';
import {
  BodyMdBase,
  Colors,
  FontBold,
  FontMedium,
  FontSemibold,
} from 'assets/styles';
import { RRConfirmationModal } from 'components';

interface RadiusModalProps {
  selectedDevice: any;
  data: any;
  visible: boolean;
  onClose: () => void;
}

const RadiusModal: React.FC<RadiusModalProps> = ({
  selectedDevice,
  data,
  visible,
  onClose,
}) => {
  const { createGeofence, deleteGeofence } = useMapMutations();

  const [sliderValue, setSliderValue] = useState(0);
  const [showConfirmRemove, setShowConfirmRemove] = useState(false);
  const [isEditingMax, setIsEditingMax] = useState(false);
  const [maxValue, setMaxValue] = useState(400);
  const [isEditingValue, setIsEditingValue] = useState(false);
  const [tempSliderValue, setTempSliderValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const valueInputRef = useRef<HTMLInputElement>(null);

  const onCreateZoneHandler = useCallback(() => {
    if (sliderValue === 0 || !selectedDevice?.imei) return;
    if (String(data?.radius) === String(sliderValue)) {
      onClose();
      return;
    }
    if (sliderValue > 0) {
      createGeofence.mutate(
        {
          imei: selectedDevice.imei,
          name: '',
          radius: sliderValue,
          center_lat: selectedDevice?.gps?.latitude || process.env.DEFAULT_LAT,
          center_lng: selectedDevice?.gps?.longitude || process.env.DEFAULT_LNG,
        },
        {
          onSuccess: () => {
            onClose();
          },
        },
      );
    }
    return;
  }, [data, selectedDevice, sliderValue, createGeofence, onClose]);

  const onCloseRemoveModal = useCallback(() => {
    setShowConfirmRemove(false);
  }, []);

  const onRemoveRadius = useCallback(() => {
    if (!selectedDevice?.imei || !data?.id) return;
    deleteGeofence.mutate(
      { imei: selectedDevice.imei, id: data.id },
      {
        onSuccess: () => {
          onCloseRemoveModal();
        },
      },
    );
  }, [data, deleteGeofence, selectedDevice?.imei, onCloseRemoveModal]);

  // Value input handlers
  const onValueClick = useCallback(() => {
    setIsEditingValue(true);
    setTempSliderValue(sliderValue.toString());
  }, [sliderValue]);

  const onValueSubmit = useCallback(() => {
    const newValue = parseInt(tempSliderValue);
    if (!isNaN(newValue) && newValue >= 0 && newValue <= maxValue) {
      setSliderValue(newValue);
    }
    setIsEditingValue(false);
    setTempSliderValue('');
  }, [tempSliderValue, maxValue]);

  const onValueKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        onValueSubmit();
      } else if (e.key === 'Escape') {
        setIsEditingValue(false);
        setTempSliderValue('');
      }
    },
    [onValueSubmit],
  );

  const onValueInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      // Only allow numbers
      if (/^\d*$/.test(value)) {
        setTempSliderValue(value);
      }
    },
    [],
  );

  // Handle slider change - don't update when editing value input
  const onSliderChange = useCallback(
    (value: number) => {
      if (!isEditingValue) {
        setSliderValue(value);
      }
    },
    [isEditingValue],
  );

  useEffect(() => {
    if (!data?.radius) {
      setSliderValue(0);
      return;
    }
    setSliderValue(data.radius);
  }, [data]);

  useEffect(() => {
    if (isEditingMax && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditingMax]);

  useEffect(() => {
    if (isEditingValue && valueInputRef.current) {
      valueInputRef.current.focus();
      valueInputRef.current.select();
    }
  }, [isEditingValue]);

  return (
    <>
      {Number(data?.radius) > 0 && (
        <div className='radius  absolute left-1/2 top-0 z-30 mt-3 flex -translate-x-1/2 cursor-pointer items-center gap-2 rounded-xl bg-white-1000 p-2 shadow-xl'>
          <Trans
            i18nKey='map.removeRadiusContent'
            values={{
              radius: Number(data?.radius),
            }}
            components={{
              strong: <strong className={`${FontSemibold} ${BodyMdBase}`} />,
            }}
          />
          <img
            className='size-5 rounded-full bg-red-10'
            onClick={() => {
              setShowConfirmRemove(true);
            }}
            src={images.Icon.RadiusRemove}
            alt='Remove'
          />
        </div>
      )}

      <RRConfirmationModal
        title={t('map.deleteTitle')}
        message={t('map.deleteContent')}
        onCancel={onCloseRemoveModal}
        onConfirm={onRemoveRadius}
        visible={showConfirmRemove}
      />
      <Modal
        open={visible}
        centered
        closable={false}
        title={'Tạo vùng'}
        maskClosable={false}
        onCancel={onClose}
        footer={
          <Footer>
            <CancelButton
              className={`${FontMedium}`}
              key='cancel'
              onClick={onClose}
            >
              Huỷ bỏ
            </CancelButton>
            <OkButton
              className={`${FontMedium}`}
              key='confirm'
              onClick={onCreateZoneHandler}
            >
              Xác nhận
            </OkButton>
          </Footer>
        }
      >
        <Body>
          <Radius className={`${FontBold}`}>{t('map.radius')}</Radius>
          <div className='flex w-full items-center'>
            <div>{t('map.radiusMin')}</div>
            <Slider
              className='flex-1'
              styles={{
                track: {
                  background: Colors.black[1000],
                },
              }}
              min={0}
              step={1}
              max={maxValue}
              onChange={onSliderChange}
              value={sliderValue}
              tooltip={{
                formatter: value => `${value} km`,
                overlayStyle: {
                  backgroundColor: '#ffffff',
                  color: '#000000',
                },
                overlayInnerStyle: {
                  color: '#000000',
                  fontWeight: 500,
                  backgroundColor: '#ffffff',
                },
                className: 'slider-custom-tooltip',
              }}
            />
            {isEditingValue ? (
              <MaxValueInput
                ref={valueInputRef}
                value={tempSliderValue}
                onChange={onValueInputChange}
                onKeyDown={onValueKeyDown}
                onBlur={onValueSubmit}
                placeholder='0'
              />
            ) : (
              <MaxValue onClick={onValueClick}>
                {Math.floor(sliderValue)}km
              </MaxValue>
            )}
          </div>
        </Body>
      </Modal>
    </>
  );
};

export default RadiusModal;

const Body = tw.div`py-5`;
const Footer = tw.div`flex gap-3`;
const Radius = tw.div`mb-3`;
const MaxValue = tw.div`bg-grey-50 px-2 py-1 rounded-lg text-grey-600 cursor-pointer hover:bg-grey-100 transition-colors w-[62px] text-center whitespace-nowrap`;
const MaxValueInput = tw.input`bg-grey-50 py-1 px-2 rounded-lg text-grey-600  outline-none w-[62px] text-center`;
const OkButton = tw.button`w-1/2 rounded-lg bg-brand-300 py-2 text-black-1000`;
const CancelButton = tw.button`w-1/2 rounded-lg border border-grey-100 py-2 text-black-1000`;
