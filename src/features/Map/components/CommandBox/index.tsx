import React, { useEffect, useState } from 'react';

import { Drawer, Input, List } from 'antd';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import useCommandBox from 'features/Map/hooks/useCommandBox';
import {
  useQueryCommandSamples,
  useQueryCommands,
} from 'features/Map/hooks/useMapData';
import { CommandSamplesResponse, CommandsResponse } from 'features/Map/types';
import { t } from 'i18next';
import type { DeviceType } from 'types/device';

import images from 'assets/images';
import { BodyMdBase, BodyMdExtend, FontBold, TitleMd } from 'assets/styles';
import { Icon } from 'components';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import './CommandBox.css';

const ListCustomItem = ({
  label,
  value,
  className = '',
  classNameValue = '',
}) => {
  return (
    <div className={twMerge('relative', className)}>
      <span className='text-grey-600'>{label}</span>
      <span className={twMerge('ml-1 text-black-1000', classNameValue)}>
        {value}
      </span>
    </div>
  );
};

interface CommandBoxProps {
  visible: boolean;
  onClose: () => void;
  device: DeviceType;
}

const CommandBox: React.FC<CommandBoxProps> = ({
  visible,
  onClose,
  device,
}) => {
  const [page, setPage] = useState(1);
  const [isShowCommandList, setIsShowCommandList] = useState(false);

  const commandsQuery = useQueryCommands(device?.imei ?? '');
  const commandSamplesQuery = useQueryCommandSamples(device?.imei ?? '');

  const commandSamples =
    (commandSamplesQuery.data as CommandSamplesResponse)?.commands || [];
  const configCommands =
    (commandSamplesQuery.data as CommandSamplesResponse)?.config_commands || [];
  const commandMessages =
    (commandsQuery.data as CommandsResponse)?.commands || [];

  const onLoadMoreMsg = () => {
    setPage(prev => prev + 1);
  };

  const {
    messagesByDate,
    commandInput,
    activeButton,
    inputRef,
    scrollRef,
    handleChange,
    handleSendCommand,
    handleScroll,
    setActiveButton,
    handleListItemClick,
  } = useCommandBox(device.imei, onLoadMoreMsg, page, commandMessages);

  useEffect(() => {
    const firstResponse = commandMessages?.[0]?.response;
    const intervalId = setInterval(() => {
      commandsQuery.refetch();
    }, 1000);

    if (firstResponse) {
      clearInterval(intervalId);
    }

    return () => {
      clearInterval(intervalId);
    };
  }, [commandMessages]);

  return (
    <Drawer
      className='command-box no-transition'
      closable={false}
      open={visible}
      width='auto'
    >
      <Content>
        {isShowCommandList && (
          <CommandListContainer>
            <div className='p-4'>
              <Header>
                <div className={`${BodyMdExtend} ${FontBold}`}>
                  {t('chatBox.commandListTitle')}
                </div>
                <div className='ant-modal-close bg-white'>
                  <Icon
                    src={images.Icon.X}
                    className='cursor-pointer'
                    onClick={() => setIsShowCommandList(false)}
                  />
                </div>
              </Header>
              <div className='flex w-full items-center'>
                {[true, false].map(isActive => (
                  <SubmitButton
                    key={String(isActive)}
                    className={isActive ? 'rounded-l-lg' : 'rounded-r-lg'}
                    isActive={activeButton === isActive}
                    onClick={() => setActiveButton(isActive)}
                  >
                    <IconContainer>
                      <Icon
                        src={
                          isActive
                            ? images.Icon.SearchLoupe
                            : images.Icon.Setting
                        }
                      />
                      {t(isActive ? 'chatBox.search' : 'chatBox.settings')}
                    </IconContainer>
                  </SubmitButton>
                ))}
              </div>
            </div>
            <RRScrollView
              scrollbarTrackStyle={{
                right: 4,
              }}
            >
              <List
                className='px-4'
                dataSource={activeButton ? commandSamples : configCommands}
                renderItem={item => (
                  <Item
                    className={`${BodyMdBase} ${FontBold}`}
                    onClick={() => handleListItemClick(item.message)}
                  >
                    <div className='mx-[-8px] flex h-[52px] w-[calc(100%+16px)] items-center rounded-xl px-2 hover:bg-grey-50'>
                      {item.name}
                    </div>
                  </Item>
                )}
              />
            </RRScrollView>
          </CommandListContainer>
        )}
        <ChatContainer>
          <ChatHeader>
            <Header>
              <Icon
                onClick={() => setIsShowCommandList(prev => !prev)}
                src={images.Icon.At}
                className='cursor-pointer'
              />
              <div className={`${TitleMd} ${FontBold}`}>
                {t('chatBox.sendCommand')}
              </div>
              <div className='ant-modal-close bg-white'>
                <Icon
                  onClick={onClose}
                  src={images.Icon.X}
                  className='cursor-pointer'
                />
              </div>
            </Header>
            <VehicleInfo>
              <p className={`${BodyMdBase} ${FontBold} uppercase`}>
                {device?.device_name}
              </p>
              <ListCustomItem label='IMEI:' value={device?.imei} />
              <ListCustomItem
                label='Biển số xe:'
                value={device?.device_plate_number}
              />
            </VehicleInfo>
          </ChatHeader>
          <ChatBody>
            <RRScrollView
              scrollbarTrackStyle={{
                right: 4,
              }}
              className='w-full overflow-hidden'
              ref={scrollRef}
              onScroll={handleScroll}
            >
              {Object.keys(messagesByDate).map(date => (
                <div className='mb-4 flex flex-col' key={date}>
                  <div className='mb-4 text-center'>
                    <p className='text-sm font-medium text-grey-400'>{date}</p>
                  </div>
                  {Object.keys(messagesByDate[date]).map(time => (
                    <div className='flex flex-col gap-4' key={time}>
                      {messagesByDate[date][time].map((message, index) => (
                        <div
                          key={index}
                          className={twMerge(
                            'flex flex-col px-4 gap-1',
                            message.type === 'right' ? 'items-end' : '',
                          )}
                        >
                          {message.text && message.type === 'left' && (
                            <>
                              <LeftMessage>{message.text}</LeftMessage>
                              <p className='text-xs font-medium text-grey-400'>
                                {time}
                              </p>
                            </>
                          )}
                          {message.text && message.type === 'right' && (
                            <>
                              <RightMessage>{message.text}</RightMessage>
                              <div className='flex flex-row gap-2'>
                                {message?.status === 'sent' && (
                                  <>
                                    <svg
                                      width='16'
                                      height='16'
                                      viewBox='0 0 16 16'
                                      fill='none'
                                      xmlns='http://www.w3.org/2000/svg'
                                    >
                                      <path
                                        fillRule='evenodd'
                                        clipRule='evenodd'
                                        d='M13.1508 4.8287C13.3943 5.04432 13.3943 5.3939 13.1508 5.60952L6.87035 11.1719C6.75343 11.2755 6.59485 11.3337 6.42951 11.3337C6.26416 11.3336 6.10559 11.2755 5.98868 11.1719L2.84928 8.39068C2.60586 8.17504 2.60591 7.82545 2.84939 7.60987C3.09288 7.39428 3.48759 7.39432 3.731 7.60997L6.42961 10.0007L12.2692 4.8287C12.5127 4.61309 12.9074 4.61309 13.1508 4.8287Z'
                                        fill='#54565C'
                                      />
                                    </svg>

                                    <p className='text-xs text-black-1000'>
                                      Đã gửi
                                    </p>
                                  </>
                                )}
                                <p className='text-xs font-medium text-grey-400'>
                                  {time}
                                </p>
                              </div>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              ))}
            </RRScrollView>

            <InputContainer>
              <Input
                ref={inputRef}
                bordered={false}
                placeholder={t('chatBox.placeholder')}
                value={commandInput}
                onChange={handleChange}
                onPressEnter={handleSendCommand}
                suffix={
                  <Icon
                    onClick={handleSendCommand}
                    width='40px'
                    height='40px'
                    className='cursor-pointer rounded-lg bg-brand-300 p-2'
                    src={images.Icon.SendCommand}
                  />
                }
                style={{
                  height: '72px',
                  display: 'flex',
                  alignItems: 'center',
                }}
              />
            </InputContainer>
          </ChatBody>
        </ChatContainer>
      </Content>
    </Drawer>
  );
};

export default CommandBox;

const Content = tw.div`flex h-full overflow-y-hidden`;
const CommandListContainer = tw.div`flex flex-col w-[360px] border-grey-100 flex-1 border-r`;
const ChatContainer = tw.div`flex h-full w-[360px] flex-1 flex-col justify-between bg-white-1000`;
const ChatHeader = tw.div`p-4`;
const VehicleInfo = tw.div`rounded-xl border border-grey-100 p-3 flex flex-col gap-2`;
const ChatBody = tw.div`flex flex-col flex-1 justify-end`;
const LeftMessage = tw.p`inline-block rounded-md bg-brand-300 p-3 text-left break-all`;
const RightMessage = tw.p`inline-block rounded-md bg-grey-50 p-3 text-right`;
const InputContainer = tw.div`border-t border-grey-100 h-[72px]`;
const Header = tw.div`flex justify-between mb-4`;
const Item = tw(List.Item)`cursor-pointer p-0`;
const SubmitButton = tw.button<{
  isActive: boolean;
}>`h-[36px] w-1/2 py-2 text-black-1000 border flex items-center justify-center ${p =>
  p.isActive ? 'bg-brand-300' : 'bg-white-100 hover:bg-grey-50'} ${p =>
  !p.isActive ? 'border-grey-100' : 'border-brand-300'}`;
const IconContainer = tw.div`flex items-center justify-center gap-2 cursor-pointer`;
