import { useState } from 'react';

import { useMutation } from '@tanstack/react-query';
import { Button, Flex, UploadFile } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useProfile } from 'hooks/useProfile';
import { xor } from 'lodash';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { createApiMutationFn } from 'services/reactQuery';

import images from 'assets/images';
import { BodyLg, BodyMdExtend, H4 } from 'assets/styles';
import { Icon, RRUpload } from 'components';
import { PrimaryButton } from 'components/Button';
import showToast from 'components/RRToastMessage/Toast';

interface ReportIssueProps {}

const commonIssues = [
  { label: 'Mất tín hiệu GPS', value: 'gps_signal_loss' },
  { label: 'Độ chính xác không cao', value: 'low_accuracy' },
  { label: 'Hao pin nhanh', value: 'fast_battery_drain' },
  { label: 'Giao diện phức tạp', value: 'complex_interface' },
  { label: 'Lỗi phần mềm', value: 'software_error' },
  { label: 'Tài khoản không thể truy cập', value: 'login_issues' },
  // { label: 'Khác', value: 'other' },
];

const ReportIssue = ({}: ReportIssueProps) => {
  const { profile } = useProfile();
  const [selectedIssues, setSelectedIssues] = useState<
    (typeof commonIssues)[0][]
  >([]);
  const [description, setDescription] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const { mutate: reportIssue, isPending: isReporting } = useMutation({
    mutationFn: createApiMutationFn<any, any>(data => ({
      method: 'POST',
      route: '/issue_reports',
      data,
    })),
  });

  const handleChange = (newFileList: UploadFile[]) => {
    setFileList(newFileList);
  };

  const handleIssueSelect = value => {
    setSelectedIssues(prev => xor(prev, [value]));
  };

  const handleDescriptionChange = e => {
    setDescription(e.target.value);
  };

  const handleSubmit = async () => {
    const formData = new FormData();
    formData.append(
      'issue_type',
      selectedIssues.map(issue => issue.value).join(','),
    );
    formData.append('description', description);
    if (fileList.length > 0) {
      fileList.forEach(file => {
        if (file.originFileObj) {
          formData.append('attachments', file.originFileObj);
        }
      });
    }

    reportIssue(formData, {
      onSuccess: () => {
        showToast('success', 'Gửi vấn đề thành công');
        // reset form
        setSelectedIssues([]);
        setDescription('');
        setFileList([]);
      },
    });
  };

  return (
    <Container>
      <Header>
        <div className='flex flex-col gap-2'>
          <GreetingText className={BodyLg}>
            Xin chào {profile?.full_name}
          </GreetingText>
          <OverviewText className={H4}>
            {t('reportIssue.headerText')}
          </OverviewText>
        </div>
      </Header>
      <div className='mt-6 flex justify-between gap-10'>
        <div className='flex w-1/2 flex-col'>
          <div className='mb-3 text-sm  font-normal leading-[24px] text-grey-600'>
            {t('reportIssue.instruction')}
          </div>
          <Flex wrap>
            {commonIssues.map(issue => {
              const selected = selectedIssues.includes(issue);
              return (
                <TagIssue
                  onClick={() => handleIssueSelect(issue)}
                  key={issue.value}
                  className={`${
                    selected
                      ? 'border-brand-300 bg-brand-300 hover:border-grey-100'
                      : ''
                  } ${BodyMdExtend} cursor-pointer hover:bg-grey-100`}
                >
                  {issue.label}
                </TagIssue>
              );
            })}
          </Flex>

          <h3 className='my-1 font-medium'>Khác</h3>
          <TextArea
            placeholder={t('reportIssue.describePlaceholder')}
            value={description}
            onChange={handleDescriptionChange}
            className='w-full border-grey-50 bg-grey-50 hover:border-grey-100 hover:bg-grey-100 focus-visible:border-grey-100 focus-visible:bg-grey-100'
            rows={10}
          />
          <UploadSection>
            <UploadIntro>{t('reportIssue.uploadIntro')}</UploadIntro>
            <RRUpload
              fileType='image'
              fileList={fileList}
              onChange={handleChange}
            />
          </UploadSection>
          <PrimaryButton
            htmlType='submit'
            className='mt-3'
            disabled={!selectedIssues.length || !description || isReporting}
            loading={isReporting}
            onClick={handleSubmit}
          >
            <Icon src={images.Icon.SendMessage} />
            {t('reportIssue.sendReportButton')}
          </PrimaryButton>
        </div>
        <div className='flex flex-1 items-start justify-center'>
          <div className='size-full'>
            <img src={images.ReportIssue.CrashReportSection} />
          </div>
        </div>
      </div>
    </Container>
  );
};

const Container = tw.div`flex h-full flex-col p-6`;
const Header = tw.div`flex justify-between`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const TagIssue = tw.button`mb-2 mr-2 flex items-center justify-center rounded-full border border-grey-100 px-3 py-1 text-center hover:bg-grey-100`;
const UploadIntro = tw.div`text-sm font-normal leading-[24px] my-3 text-grey-600`;
const UploadSection = tw.div``;

export default ReportIssue;
