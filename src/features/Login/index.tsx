import { useEffect, useState } from 'react';

import { Checkbox, CheckboxProps } from 'antd';
import { yupResolver } from 'hooks/useYupResolver';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import tw from 'tailwind-styled-components';

import images from 'assets/images';
import { RRFieldInput, RRFieldPassword } from 'components';
import { LinkButton, PrimaryButton } from 'components/Button';

import AppDownloadBanner from './components/AppDownloadBanner';
import ForgotPassModal from './components/ForgotPassModal';
import { useLoginMutation } from './hooks/useLoginMutations';
import { loginSchema } from './schema/loginSchemas';
import { LoginForm } from './types';

// Constants cho localStorage keys
const REMEMBER_ME_KEY = 'navio_remember_me';
const SAVED_CREDENTIALS_KEY = 'navio_saved_credentials';

interface LoginProps {}

const Login = ({}: LoginProps) => {
  const { t } = useTranslation();
  const searchParams = new URLSearchParams(window.location.search);

  // Load saved remember me state
  const [rememberMe, setRememberMe] = useState(() => {
    try {
      return localStorage.getItem(REMEMBER_ME_KEY) === 'true';
    } catch {
      return false;
    }
  });

  const [isForgot, setIsForgot] = useState(false);

  // Use react-query mutation instead of redux
  const loginMutation = useLoginMutation();

  const {
    control,
    handleSubmit,
    formState,
    reset,
    setValue,
    formState: { errors },
  } = useForm<LoginForm>({
    mode: 'onBlur',
    resolver: yupResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  // Load saved credentials when component mounts
  useEffect(() => {
    if (rememberMe) {
      try {
        const savedCredentials = localStorage.getItem(SAVED_CREDENTIALS_KEY);
        if (savedCredentials) {
          const { username, password } = JSON.parse(savedCredentials);
          setValue('username', username || '');
          setValue('password', password || '');
        }
      } catch (error) {
        console.error('Error loading saved credentials:', error);
        // Clear corrupted data
        localStorage.removeItem(SAVED_CREDENTIALS_KEY);
      }
    }
  }, [rememberMe, setValue]);

  const handleRememberMeChange: CheckboxProps['onChange'] = e => {
    const checked = e.target.checked;
    setRememberMe(checked);

    try {
      // Save remember me preference
      localStorage.setItem(REMEMBER_ME_KEY, checked.toString());

      // If unchecked, clear saved credentials
      if (!checked) {
        localStorage.removeItem(SAVED_CREDENTIALS_KEY);
      }
    } catch (error) {
      console.error('Error saving remember me preference:', error);
    }
  };

  const onSubmit = (data: LoginForm) => {
    // Save credentials if remember me is checked
    if (rememberMe) {
      try {
        const credentials = {
          username: data.username,
          password: data.password,
        };
        localStorage.setItem(
          SAVED_CREDENTIALS_KEY,
          JSON.stringify(credentials),
        );
      } catch (error) {
        console.error('Error saving credentials:', error);
      }
    }

    const callback = ({ status, shouldChangePassword }) => {
      if (status && shouldChangePassword) {
        // handle reset password
        window.location.href = '/reset-password';
        return;
      }
      if (status) {
        window.location.href = '/overview';
        return;
      }
      reset();
    };

    // Use react-query mutation instead of redux dispatch
    loginMutation.mutate({ data, callback });
  };

  const handleCloseModal = () => {
    setIsForgot(false);
    window.history.replaceState({}, document.title, window.location.pathname);
  };

  useEffect(() => {
    // enter key press
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && isForgot === false) {
        handleSubmit(onSubmit)();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isForgot]);

  useEffect(() => {
    if (searchParams.get('forget_password') === 'true') {
      setIsForgot(true);
    }
  }, [searchParams]);

  return (
    <Container>
      <WelComeSection
        style={{
          backgroundImage: `url(${images.Login.SignInWelcome})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <AppDownloadBanner />
      </WelComeSection>
      <div className='flex w-1/2 flex-col items-center justify-center'>
        <LogoSection>
          <MainLogo src={images.Login.Logo} alt='Icon' />
        </LogoSection>
        <form
          className='flex w-full max-w-[360px] flex-col gap-3'
          onSubmit={handleSubmit(onSubmit)}
          autoComplete='off'
        >
          <RRFieldInput
            control={control}
            id='username'
            name='username'
            label={t('login.username')}
            placeholder={t('login.usernamePlaceholder')}
            prefixIcon={images.Icon.User}
            errors={errors}
            autoComplete='off'
          />
          <RRFieldPassword
            control={control}
            label={t('login.password')}
            id='password'
            name='password'
            placeholder={t('login.passwordPlaceholder')}
            prefixIcon={images.Icon.Lock}
            errors={errors}
            autoComplete='new-password'
          />
          <div className='mb-7 flex w-full items-center justify-between'>
            <CheckBoxSection>
              <Checkbox checked={rememberMe} onChange={handleRememberMeChange}>
                <span className='font-medium'>{t('login.remember')}</span>
              </Checkbox>
            </CheckBoxSection>
            <LinkButton
              onClick={() => {
                setIsForgot(true);
              }}
              size='small'
              className='h-6 text-end'
            >
              {t('login.forgotPassword')}
            </LinkButton>
          </div>
          <PrimaryButton
            htmlType='submit'
            disabled={loginMutation.isPending || !formState.isValid}
            loading={loginMutation.isPending}
          >
            {t('login.login')}
          </PrimaryButton>
        </form>
      </div>
      <ForgotPassModal visible={isForgot} onCancel={handleCloseModal} />
    </Container>
  );
};

export default Login;

const Container = tw.div`flex h-screen`;
const WelComeSection = tw.div`relative w-1/2 flex-wrap items-center justify-center`;
const LogoSection = tw.div`mb-10 flex flex-col items-center`;
const MainLogo = tw.img`mb-4`;
const CheckBoxSection = tw.label`flex items-center`;
