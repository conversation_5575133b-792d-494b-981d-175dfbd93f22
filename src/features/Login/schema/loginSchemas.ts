import * as yup from 'yup';

import { EMAIL_REGEX } from 'utils/regex';

// Base validation schemas
export const baseValidations = {
  username: yup.string().required('<PERSON><PERSON><PERSON> khoản là bắt buộc'),

  password: yup
    .string()
    .required('<PERSON><PERSON><PERSON> khẩu là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),

  email: yup
    .string()
    .required('Email là bắt buộc')
    .matches(EMAIL_REGEX, 'Email không đúng định dạng'),
};

// Create Account Schema (Normal User)
export const loginSchema = yup.object({
  username: baseValidations.username,
  password: baseValidations.password,
});

export const forgetPasswordSchema = yup.object({
  email: baseValidations.email,
});

export type LoginForm = yup.InferType<typeof loginSchema>;
export type ForgotPasswordForm = yup.InferType<typeof forgetPasswordSchema>;
