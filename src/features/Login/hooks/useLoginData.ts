import { useQueryClient } from '@tanstack/react-query';

import { Account } from 'features/Profile/types';

/**
 * Hook to get login token from react-query cache
 */
export const useLoginToken = (): string | undefined => {
  const queryClient = useQueryClient();
  return queryClient.getQueryData<string>(['auth', 'token']);
};

/**
 * Hook to get user profile data from react-query cache
 */
export const useLoginProfile = (): Account | undefined => {
  const queryClient = useQueryClient();
  return queryClient.getQueryData<Account>(['auth', 'user']);
};

/**
 * Hook to check if user is authenticated
 */
export const useIsAuthenticated = (): boolean => {
  const token = useLoginToken();
  return !!token;
};

/**
 * Hook to get complete auth state
 */
export const useLoginData = () => {
  const token = useLoginToken();
  const profile = useLoginProfile();
  const isAuthenticated = useIsAuthenticated();

  return {
    token,
    profile,
    user: profile, // Alias for backward compatibility
    isAuthenticated,
    loading: false, // React Query manages loading states per mutation
  };
};
