import { useMutation, useQueryClient } from '@tanstack/react-query';

import { Account } from 'features/Profile/types';
import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';
import { removeUserLoginCookie, setUserLoginCookie } from 'services/cookies';
import { getErrorMsg } from 'services/reactQuery';
import { createApiMutationFn } from 'services/reactQuery';

import showToast from 'components/RRToastMessage/Toast';

import { LoginForm } from '../types';

interface LoginResponse {
  token: string;
  user: Account;
  shouldChangePassword?: boolean;
}

interface LoginMutationData {
  data: LoginForm;
  callback: (response: any) => void;
}

export interface ForgotPasswordForm {
  email: string;
}

interface ForgotPasswordResponse {
  message: string;
  success: boolean;
}

export const useLoginMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<LoginResponse, Error, LoginMutationData>({
    mutationFn: async ({ data }) => {
      const response: ApiResponse = await callApi({
        method: 'post',
        route: `/users/sign_in`,
        data: {
          user: {
            login: data.username,
            password: data.password,
          },
        },
      });

      if (response.success) {
        const responseData = response.response?.data?.status;
        const user = responseData?.data?.user ?? {};
        const shouldChangePassword =
          responseData?.data?.password_require_change;
        const token = responseData?.data?.token ?? '';

        return {
          token,
          user,
          shouldChangePassword,
        };
      } else {
        const errorMessage = getErrorMsg(response.error);
        throw new Error(errorMessage || 'Login failed');
      }
    },
    onSuccess: (loginResponse, variables) => {
      const { token, user, shouldChangePassword } = loginResponse;
      const { callback } = variables;

      // Set cookie
      setUserLoginCookie({
        token,
        userId: user.id?.toString() ?? '',
      });

      // Update query cache
      queryClient.setQueryData(['auth', 'user'], user);
      queryClient.setQueryData(['auth', 'token'], token);

      // Handle password change callback
      callback?.({
        status: true,
        shouldChangePassword,
      });
      showToast('success', 'Đăng nhập thành công');
    },
    onError: (error: any, { callback }) => {
      const message = getErrorMsg(error);
      showToast('failed', message || 'Đăng nhập thất bại');
      callback?.({
        status: false,
      });
    },
  });
};

export const useLogoutMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, void>({
    mutationFn: async () => {
      const response: ApiResponse = await callApi({
        method: 'delete',
        route: `/users/sign_out`,
      });

      // Remove cookie regardless of response
      removeUserLoginCookie();

      if (!response.success && response.error) {
        // Still proceed with logout even if API call fails
        console.warn(
          'Logout API call failed, but continuing with local logout',
        );
      }
    },
    onSuccess: () => {
      // Clear all auth-related cache
      queryClient.removeQueries({ queryKey: ['auth'] });
      queryClient.clear();
    },
  });
};

export const useForgotPassword = () => {
  return useMutation<ForgotPasswordResponse, Error, ForgotPasswordForm>({
    mutationFn: createApiMutationFn<ForgotPasswordResponse, ForgotPasswordForm>(
      data => ({
        method: 'POST',
        route: '/users/password',
        data: {
          email: data.email,
        },
      }),
    ),
    onSuccess: () => {
      showToast('success', 'Yêu cầu đặt lại mật khẩu đã được gửi');
    },
  });
};

export const useUpdateProfileMutation = () => {
  const queryClient = useQueryClient();

  return (updatedProfile: Account) => {
    queryClient.setQueryData(['auth', 'user'], updatedProfile);
  };
};
