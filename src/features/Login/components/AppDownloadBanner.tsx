import React from 'react';

import { Rate } from 'antd';
import {
  AppStoreButton,
  ButtonsContainer,
  GooglePlayButton,
} from 'react-mobile-app-button';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyLg, BodyMdBase, FontBold, FontMedium } from 'assets/styles';
import { Icon } from 'components';

const AppDownloadBanner = () => {
  return (
    <>
      <Container>
        <AppName>
          <IconContainer>
            <Icon
              width={'60px'}
              height={'60px'}
              src={images.Icon.NavioAppIcon}
            />
          </IconContainer>
          <div>
            <Name className={`${BodyLg} ${FontBold}`}>
              {t('login.appName')}
            </Name>
            <RateContainer>
              <RateValue className={`${BodyMdBase} ${FontMedium}`}>
                {t('login.defaultRate')}
              </RateValue>
              <Rate
                allowHalf
                disabled
                defaultValue={4.98}
                style={{ fontSize: 16 }}
              />
            </RateContainer>
          </div>
        </AppName>
        <div className='wrapper-mobile-buttons'>
          <ButtonsContainer>
            <AppStoreButton url={'www.google.com'} theme={'dark'} />
            <GooglePlayButton url={'www.google.com'} theme={'dark'} />
          </ButtonsContainer>
        </div>
      </Container>
    </>
  );
};

export default AppDownloadBanner;

const Container = tw.div`h-[92px] absolute inset-x-4 bottom-4 flex items-center justify-between rounded-2xl bg-white-1000 p-4 shadow-md`;
const AppName = tw.div`flex items-center space-x-4`;
const IconContainer = tw.div`bg-yellow-400 flex size-12 items-center justify-center rounded-full`;
const Name = tw.div`text-lg font-bold`;
const RateContainer = tw.div`flex items-center`;
const RateValue = tw.span`mr-2 text-grey-400`;
