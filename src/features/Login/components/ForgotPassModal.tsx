import React, { useState } from 'react';

import { Modal } from 'antd';
import { yupResolver } from 'hooks/useYupResolver';
import { useForm } from 'react-hook-form';
import { Trans } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { FontBold, TitleMd } from 'assets/styles';
import { RRFieldInput } from 'components';
import { PrimaryButton } from 'components/Button';

import { useForgotPassword } from '../hooks/useLoginMutations';
import { forgetPasswordSchema } from '../schema/loginSchemas';

interface ForgotPassModalProps {
  visible: boolean;
  onCancel: () => void;
}

export interface ForgotPasswordForm {
  email: string;
}

const ForgotPassModal: React.FC<ForgotPassModalProps> = ({
  visible,
  onCancel,
}) => {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const forgotPasswordMutation = useForgotPassword();

  const {
    control,
    handleSubmit,
    formState,
    formState: { errors },
    watch,
    reset,
  } = useForm<ForgotPasswordForm>({
    mode: 'onBlur',
    resolver: yupResolver(forgetPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const email = watch('email');

  const onSubmit = (data: ForgotPasswordForm) => {
    forgotPasswordMutation.mutate(data, {
      onSuccess: () => {
        setIsSubmitted(true);
      },
    });
  };

  // Reset form when modal closes
  const handleCancel = () => {
    setIsSubmitted(false);
    reset();
    onCancel();
  };

  // Reset state when modal opens
  React.useEffect(() => {
    if (visible) {
      setIsSubmitted(false);
      reset();
    }
  }, [visible, reset]);

  React.useEffect(() => {
    // enter key press
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && visible) {
        handleSubmit(onSubmit)();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible]);

  return (
    <Modal
      open={visible}
      centered
      title={
        <Title className={`${TitleMd} ${FontBold}`}>
          {isSubmitted ? 'Kiểm tra email' : 'Đặt lại mật khẩu'}
        </Title>
      }
      onCancel={handleCancel}
      footer={null}
    >
      {isSubmitted ? (
        <>
          <LogoSection>
            <MainLogo src={images.Login.IlluEmail} alt='Icon' />
          </LogoSection>
          <NoticeText>
            <Trans
              i18nKey='login.notice'
              values={{ email }}
              components={{ strong: <strong /> }}
            />
          </NoticeText>
        </>
      ) : (
        <form id='forgot-password-form' onSubmit={handleSubmit(onSubmit)}>
          <NoticeText className='mt-3'>{t('login.noticeForgot')}</NoticeText>
          <RRFieldInput
            id='email'
            name='email'
            control={control}
            label={t('login.email')}
            className='mt-3 w-full'
            placeholder={t('login.emailPlaceholder')}
            prefixIcon={images.Icon.MailClose}
            errors={errors}
          />
          <PrimaryButton
            htmlType='submit'
            className='mt-6 w-full'
            disabled={forgotPasswordMutation.isPending || !formState.isValid}
            loading={forgotPasswordMutation.isPending}
          >
            {t('login.forgotPasswordButton')}
          </PrimaryButton>
        </form>
      )}
    </Modal>
  );
};

export default ForgotPassModal;

const Title = tw.div``;
const NoticeText = tw.span`text-grey-600 inline-block`;
const LogoSection = tw.div`mb-10 flex flex-col items-center`;
const MainLogo = tw.img`mb-4`;
