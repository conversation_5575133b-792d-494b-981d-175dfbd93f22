import { useMutation } from '@tanstack/react-query';

import { t } from 'i18next';

import showToast from 'components/RRToastMessage/Toast';

// Types
export interface FeedbackFormData {
  fullName: string;
  email: string;
  phone: string;
  message: string;
}

// Google Script URL for submitting feedback
const GOOGLE_SCRIPT_URL =
  'https://script.google.com/macros/s/AKfycbxmxQVq4S95YrvCVL5qjQrs3s2XPJF9V7znot-nOiEGyOiAtYa-3zqZoLW5eToQl_KdDw/exec';

/**
 * Custom mutation function to submit feedback to Google Script
 */
const submitFeedbackApi = async (data: FeedbackFormData): Promise<void> => {
  const { fullName, email, phone, message } = data;

  // Create form data for URL encoding
  const formData = new URLSearchParams();
  formData.append('fullName', fullName);
  formData.append('email', email);
  formData.append('phone', phone);
  formData.append('message', message);

  const response = await fetch(GOOGLE_SCRIPT_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData.toString(),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to submit feedback: ${errorText}`);
  }

  console.log('Feedback submitted successfully');
};

/**
 * Hook to submit feedback/contact form
 */
export const useSubmitFeedback = () => {
  return useMutation<void, Error, FeedbackFormData>({
    mutationFn: submitFeedbackApi,
    onSuccess: () => {
      showToast('success', 'Thông tin đã được gửi');
    },
    onError: error => {
      console.error('Error submitting feedback:', error);
      showToast('failed', 'Gửi thông tin thất bại. Vui lòng thử lại');
    },
  });
};

/**
 * Hook with retry functionality for critical feedback
 */
export const useSubmitFeedbackWithRetry = () => {
  return useMutation<void, Error, FeedbackFormData>({
    mutationFn: submitFeedbackApi,
    retry: 2, // Retry up to 2 times on failure
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    onSuccess: () => {
      showToast('success', 'Thông tin đã được gửi');
    },
    onError: error => {
      console.error('Error submitting feedback after retries:', error);
      showToast(
        'failed',
        'Không thể gửi thông tin sau nhiều lần thử. Vui lòng kiểm tra kết nối mạng',
      );
    },
  });
};
