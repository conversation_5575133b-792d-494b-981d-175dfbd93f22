import * as yup from 'yup';

import { EMAIL_REGEX, PHONE_REGEX } from 'utils/regex';

// Base validation schemas
export const baseValidations = {
  fullName: yup
    .string()
    .required('Họ và tên là bắt buộc')
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
    .max(100, 'Họ và tên không được quá 100 ký tự'),

  phone: yup
    .string()
    .required('Số điện thoại là bắt buộc')
    .matches(
      PHONE_REGEX,
      'Số điện thoại không đúng định dạng (VD: **********)',
    ),

  email: yup
    .string()
    .required('Email là bắt buộc')
    .matches(EMAIL_REGEX, 'Email không đúng định dạng'),

  message: yup.string().required('Địa chỉ là bắt buộc'),

  activeArea: yup.string().required('<PERSON>hu vực hoạt động là bắt buộc'),
};

// Transfer Accounts Schema
export const createSupportContactSchema = yup.object({
  phone: baseValidations.phone,
});
