import { Flex } from 'antd';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyLg, H4 } from 'assets/styles';
import { Icon, RRStaticMap } from 'components';
import { PinType } from 'components/RRStaticMap';

import FeedbackForm from './components/FeedbackForm';

interface ContactProps {}

const Contact = ({}: ContactProps) => {
  const { profile } = useProfile();

  return (
    <Container>
      <Header>
        <div className='flex flex-col gap-2'>
          <GreetingText className={BodyLg}>
            Xin chào {profile?.full_name}
          </GreetingText>
          <OverviewText className={H4}>{t('contact.headerText')}</OverviewText>
        </div>
      </Header>
      <MainContainer>
        <div className='flex flex-1 flex-col'>
          <RRStaticMap
            className='overflow-hidden rounded-2xl'
            center={{
              lat: Number(process.env.DEFAULT_LAT),
              lng: Number(process.env.DEFAULT_LNG),
            }}
          />

          <CompanyInfoContainer>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.Home} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.companyName')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  Công ty TNHH Ứng Dụng Công Nghệ Navio
                </Subtitle>
              </TextContainer>
            </RowInfo>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.MapLocation} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.address')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  RR16-RR17 Hồng Lĩnh, Phường 15, Quận 10, Thành phố Hồ Chí Minh
                </Subtitle>
              </TextContainer>
            </RowInfo>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.PhoneCallGrey} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.phone')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  84-901-290-290
                </Subtitle>
              </TextContainer>
            </RowInfo>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.MailCloseGrey} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.email')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  <a href='mailto:<EMAIL>'><EMAIL></a>
                </Subtitle>
              </TextContainer>
            </RowInfo>
          </CompanyInfoContainer>
        </div>
        <div className='flex flex-1'>
          <FeedbackForm />
        </div>
      </MainContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full flex-col p-6`;

const MainContainer = tw.div`flex flex-1 pt-6 gap-10`;
const SingleLineText = 'line-clamp-1';
const Header = tw.div`flex justify-between`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const CompanyInfoContainer = tw(Flex)`flex flex-col my-2`;
const RowInfo = tw.div`flex flex-1 flex-row gap-4 py-3 text-sm`;
const LineBox = 'border-b-[0.5px] border-grey-100';
const IconInfo = tw(Icon)`mt-1`;
const TextContainer = tw.div`flex flex-1 flex-col gap-1`;

const Title = tw.span`
  font-medium
  text-black-1000
  leading-[24px]
  font-semibold
`;

const Subtitle = tw.span`
  text-grey-600
  leading-[20px]
`;

export default Contact;
