import { Button, Form } from 'antd';
import tw from 'tailwind-styled-components';

export const FormContainer = tw.form`flex flex-1`;
export const FormSingleItem = tw(Form.Item)``;
export const GridFormWarpper = tw.div`flex flex-1 flex-col rounded-2xl border border-grey-100 px-6 py-1`;
export const Grid = tw.div`grid gap-1`;
export const SubmitButton = tw(
  Button,
)`my-3 bg-brand-300 py-2 text-sm font-medium leading-[24px] flex items-center justify-center hover:text-black-1000 hover:border-0`;

export const Label = tw.div`mb-2`;
