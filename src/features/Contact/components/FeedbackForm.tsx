import React, { useRef, useState } from 'react';

import { yupResolver } from 'hooks/useYupResolver';
import ReCAPTCHA from 'react-google-recaptcha';
import { useForm } from 'react-hook-form';

import { t } from 'i18next';

import images from 'assets/images';
import { Icon } from 'components';
import { PrimaryButton } from 'components/Button';
import RRFieldInput from 'components/FormField/RRFieldInput';
import RRFieldTextArea from 'components/FormField/RRFieldTextArea';

import { FeedbackFormData, useSubmitFeedbackWithRetry } from '../hooks';
import { createSupportContactSchema } from '../schema/accountSchemas';

const FeedbackForm = () => {
  const [captchaValue, setCaptchaValue] = useState(null);
  const recaptchaRef = useRef();

  const {
    mutate: submitFeedback,
    isPending: isSubmitting,
    reset: resetMutation,
  } = useSubmitFeedbackWithRetry();

  const methods = useForm<FeedbackFormData>({
    mode: 'onBlur',
    resolver: yupResolver(createSupportContactSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      message: '',
    },
  });

  const { handleSubmit, formState, reset, control } = methods;
  const { errors } = formState;

  const handleCaptchaChange = value => {
    setCaptchaValue(value);
  };

  const onSubmit = (data: FeedbackFormData) => {
    submitFeedback(data, {
      onSuccess: () => {
        reset(); // Reset form on success
        // Reset mutation state after 3 seconds
        setTimeout(() => {
          resetMutation();
        }, 3000);
      },
      onError: error => {
        console.error('Submit feedback error:', error);
        // Reset mutation state after 3 seconds
        setTimeout(() => {
          resetMutation();
        }, 3000);
      },
    });
  };

  return (
    <div className='flex h-fit w-full flex-col rounded-2xl border border-grey-100 p-6'>
      <form {...methods} onSubmit={handleSubmit(onSubmit)}>
        <div className='flex w-full flex-col gap-4'>
          <RRFieldInput
            id='fullName'
            name='fullName'
            control={control}
            label={t('contact.name')}
            placeholder={t('contact.placeholderName')}
            errors={errors}
          />
          <RRFieldInput
            id='email'
            name='email'
            control={control}
            label={t('contact.email')}
            placeholder={t('contact.placeholderEmail')}
            errors={errors}
          />
          <RRFieldInput
            id='phone'
            name='phone'
            required
            control={control}
            label={t('contact.phone')}
            placeholder={t('contact.placeholderPhone')}
            errors={errors}
          />
          <RRFieldTextArea
            id='message'
            name='message'
            control={control}
            label={t('contact.message')}
            placeholder={t('contact.placeholderMessage')}
            className='w-full bg-grey-50'
            rows={6}
          />
          <div className='recaptcha-container'>
            <ReCAPTCHA
              ref={recaptchaRef}
              sitekey={process.env.RECAPTCHA_SITE_KEY}
              onChange={handleCaptchaChange}
              onExpired={() => setCaptchaValue(null)}
              onErrored={() => setCaptchaValue(null)}
              theme='light' // "light" hoặc "dark"
              size='normal' // "normal" hoặc "compact"
              hl='vi' // Language code
            />
          </div>
          <PrimaryButton
            htmlType='submit'
            disabled={isSubmitting || !captchaValue}
            loading={isSubmitting}
          >
            <Icon src={images.Icon.SendMessage} />
            {t('contact.sendFeadbackButton')}
          </PrimaryButton>
        </div>
      </form>
    </div>
  );
};

export default FeedbackForm;
