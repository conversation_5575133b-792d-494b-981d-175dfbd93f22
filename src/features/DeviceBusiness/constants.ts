export const DEVICE_STATUS = {
  ONLINE: '1',
  OFFLINE: '0',
  MAINTENANCE: '2',
} as const;

export const DEVICE_STATUS_LABELS = {
  [DEVICE_STATUS.ONLINE]: 'Online',
  [DEVICE_STATUS.OFFLINE]: 'Offline',
  [DEVICE_STATUS.MAINTENANCE]: 'Maintenance',
} as const;

export const DEVICE_TYPES = {
  GPS: 'gps',
  CAMERA: 'camera',
  SENSOR: 'sensor',
} as const;

export const DEVICE_CATEGORIES = {
  VEHICLE: 'vehicle',
  PERSONAL: 'personal',
  ASSET: 'asset',
} as const;

export const MODAL_TYPES = {
  ADD_DEVICE: 'addDevice',
  MOVE_DEVICE: 'moveDevice',
  DEVICE_LOG: 'deviceLog',
  CONFIRM_REMOVE: 'confirmRemove',
  DEVICE_DETAILS: 'deviceDetails',
  DRAWER_DETAILS: 'drawerDetails',
} as const;

export const DEFAULT_PAGINATION = {
  page: 1,
  per_page: 20,
} as const;
