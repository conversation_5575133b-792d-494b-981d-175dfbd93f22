import { useMemo } from 'react';

import { useQuery } from '@tanstack/react-query';

import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

import { DeviceLogParams } from '../types';

interface DeviceFilter {
  search_keyword?: string;
  owner_id: string;
  is_activated?: string;
  statuses?: string;
  service_package_id?: string;
  device_category?: string;
  activated_at_from?: string;
  activated_at_to?: string;
  expired_at_from?: string;
  expired_at_to?: string;
  page?: number;
  per_page?: number;
}

export const useDeviceData = (props: DeviceFilter) => {
  const { page = 1, per_page = 20, owner_id, ...rest } = props;

  // Create stable query key to prevent unnecessary re-renders
  const queryKey = useMemo(
    () => queryKeys.devices.list({ owner_id, page, per_page, ...rest }),
    [owner_id, page, per_page, JSON.stringify(rest)],
  );

  const devices = useQuery({
    queryKey,
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/devices',
        query: {
          page: page.toString(),
          per_page: per_page.toString(),
          owner_id: owner_id,
          ...rest,
        },
      }),
    }),
    enabled: !!owner_id,
    staleTime: 0, // Cache for 30 seconds instead of 0
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    select: (data: any) => {
      return {
        data: data.devices || [],
        pagination: {
          page: data.page || 1,
          total_count: data.total_count || 0,
          total_pages: data.total_pages || 1,
        },
      };
    },
  });

  return devices;
};

export const useDeviceDetail = ({ imei } = { imei: '' }) => {
  return useQuery({
    queryKey: queryKeys.devices.detail(imei || ''),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/devices/${imei}`,
    }),
    enabled: !!imei,
    staleTime: 30000,
    select: (data: any) => {
      return {
        ...(data?.device || {}),
        ...(data?.device_info || {}),
        coordinates: `${data?.device?.gps?.latitude || 0},${
          data?.device?.gps?.longitude || 0
        }`,
        driver: data?.device?.driver || data?.driver || {},
      };
    },
  });
};

export const useDeviceLogs = (payload: DeviceLogParams) => {
  const {
    enabled,
    imei,
    params: { action_type, created_at_from, created_at_to, page, per_page },
  } = payload || {};
  return useQuery({
    queryKey: queryKeys.devices.logs(imei || ''),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: `/devices/${imei}/device-logs`,
        query: {
          action_type: action_type,
          created_at_from: created_at_from,
          created_at_to: created_at_to,
          page: page.toString(),
          per_page: per_page.toString(),
        },
      }),
    }),
    enabled: enabled,
    staleTime: 0,
    select: (data: any) => ({
      logs: data.logs || [],
      pagination: {
        page: data.page,
        total_count: data.total_count,
        total_pages: data.total_pages,
      },
    }),
  });
};

export const useDeviceCountByStatus = (
  props: Omit<DeviceFilter, 'page' | 'per_page'>,
) => {
  const { owner_id, ...rest } = props;

  // Create stable query key with ALL filter parameters, just like useDeviceData
  const queryKey = useMemo(
    () => queryKeys.devices.countByStatus({ owner_id, ...rest }),
    [owner_id, JSON.stringify(rest)],
  );

  return useQuery({
    queryKey,
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/devices/count-by-status',
        query: {
          owner_id: owner_id,
          ...rest,
        },
      }),
    }),
    enabled: !!owner_id,
    staleTime: 30000, // Cache for 60 seconds - count changes less frequently
    gcTime: 5 * 60 * 1000, // Keep in cache for 10 minutes
  });
};

export const useDeviceSelectors = () => {
  return useQuery({
    queryKey: queryKeys.devices.selectors(),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: '/device-selectors',
    }),
    staleTime: 30000, // Cache for 60 seconds - count changes less frequently
    gcTime: 5 * 60 * 1000, // Keep in cache for 10 minutes
    select: (data: any) => {
      return data.device_infos || [];
    },
  });
};
