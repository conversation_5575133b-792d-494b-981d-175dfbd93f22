import { useCallback, useState } from 'react';

import type { DeviceType } from 'types/device';

import type { CollapseItemData } from 'components/RRCollapseTable';

import { MODAL_TYPES } from '../constants';
import type { AccountBreadCrumbItem } from '../types';
import { AppState } from '../types';

const initialState: AppState = {
  selectedAccount: null,
  accountBreadcrumb: [],
  deviceData: [],
  selectedDevice: null,
  searchTerm: '',
  modals: {
    [MODAL_TYPES.DEVICE_LOG]: false,
    [MODAL_TYPES.ADD_DEVICE]: false,
    [MODAL_TYPES.MOVE_DEVICE]: false,
    [MODAL_TYPES.CONFIRM_REMOVE]: false,
    [MODAL_TYPES.DEVICE_DETAILS]: false,
    [MODAL_TYPES.DRAWER_DETAILS]: false,
  },
  modalProps: {},
  multipleSelect: { isActive: false, multipleDevice: [] },
};

type ModalType = keyof typeof MODAL_TYPES;

export const useDeviceState = () => {
  const searchParams = new URLSearchParams(location.search);

  const [state, setState] = useState<AppState>(initialState);

  const updateState = useCallback(
    (updates: Partial<AppState>) => {
      setState(prev => ({ ...prev, ...updates }));
    },
    [state],
  );

  const openModal = useCallback(
    (modalType: ModalType | string, modalProps?: any) => {
      setState(prev => ({
        ...prev,
        modals: { ...prev.modals, [modalType]: true },
        modalProps: {
          ...prev.modalProps,
          [modalType]: modalProps,
        },
      }));
    },
    [],
  );

  const closeModal = useCallback((modalType: string) => {
    setState(prev => ({
      ...prev,
      modals: { ...prev.modals, [modalType]: false },
      modalProps: {
        ...prev.modalProps,
        [modalType]: undefined,
      },
    }));
  }, []);

  const setSearchTerm = useCallback(
    (term: string) => {
      updateState({ searchTerm: term });
    },
    [updateState],
  );

  const onDeviceSelect = useCallback(
    (device, updateUrl = false) => {
      // move device
      const { multipleSelect } = state;
      if (multipleSelect.isActive) {
        const newMultipleDevice = multipleSelect.multipleDevice.includes(device)
          ? multipleSelect.multipleDevice.filter(
              item => item.imei !== device.imei,
            )
          : [...multipleSelect.multipleDevice, device];
        updateState({
          multipleSelect: {
            ...multipleSelect,
            multipleDevice: newMultipleDevice,
          },
        });
        return;
      }
      // unselect device
      if (device?.imei === state.selectedDevice?.imei) {
        updateState({ selectedDevice: null });
        if (updateUrl) {
          searchParams.delete('imei');
          const url = new URL(window.location.href);
          window.history.pushState(
            {},
            '',
            `${url.origin}${url.pathname}?${searchParams.toString()}`,
          );
        }
        return;
      }
      // select device
      updateState({ selectedDevice: device });
      if (device?.imei && updateUrl) {
        searchParams.set('imei', device.imei);
        const url = new URL(window.location.href);
        window.history.pushState(
          {},
          '',
          `${url.origin}${url.pathname}?${searchParams.toString()}`,
        );
      }
    },
    [state, searchParams],
  );

  const onSelectedMultipleDevice = (devices: DeviceType[]) => {
    updateState({
      multipleSelect: { ...state.multipleSelect, multipleDevice: devices },
    });
  };

  const onResetMultipleDevice = () => {
    updateState({
      multipleSelect: { multipleDevice: [], isActive: false },
      selectedDevice: null,
    });
  };

  const onSelectAccount = (account: CollapseItemData) => {
    updateState({ selectedAccount: account });
    // update url
    searchParams.set('owner_id', account.id);
    const url = new URL(window.location.href);
    window.history.pushState(
      {},
      '',
      `${url.origin}${url.pathname}?${searchParams.toString()}`,
    );
  };
  const onChangeBreadcrumb = (breadcrumb: AccountBreadCrumbItem[]) => {
    updateState({ accountBreadcrumb: breadcrumb });
  };

  return {
    state,
    updateState,
    openModal,
    closeModal,
    setSearchTerm,
    onDeviceSelect,
    onSelectedMultipleDevice,
    onResetMultipleDevice,
    onSelectAccount,
    onChangeBreadcrumb,
  };
};
