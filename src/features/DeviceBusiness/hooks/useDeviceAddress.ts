import { useMapGeocodeReverse } from 'features/Map/hooks/useMapData';
import { DeviceType } from 'types/device';

const useDeviceAddress = (device: DeviceType) => {
  const address = useMapGeocodeReverse({
    lat: device?.gps?.latitude,
    lng: device?.gps?.longitude,
  }) as { data: any | undefined };

  if (device?.default?.toString() !== '0') {
    return 'RR16-RR17 <PERSON>, Phường 15, Quận 10, <PERSON>h<PERSON><PERSON> ph<PERSON>';
  }

  return String(address);
};

export default useDeviceAddress;
