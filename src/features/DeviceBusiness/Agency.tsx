import { Breadcrumb, Flex } from 'antd';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import { UserRole } from 'types/UserRoleTypes';

import images from 'assets/images';
import { Icon } from 'components';
import WithRoleAccess from 'components/WithRoleAccess';

import DeviceDetailTabs from './components/DeviceDetailTabs';
import DeviceListPanel from './components/DeviceListPanel';
import DeviceModals from './components/DeviceModals';
import { useDeviceState } from './hooks';

interface BusinessProps {}

const ManageDeviceBusiness = ({}: BusinessProps) => {
  const { profile } = useProfile();
  const {
    state,
    openModal,
    closeModal,
    updateState,
    onDeviceSelect,
    onResetMultipleDevice,
    onSelectedMultipleDevice,
    onSelectAccount,
    onChangeBreadcrumb,
  } = useDeviceState();
  const {
    modals,
    selectedDevice,
    multipleSelect,
    selectedAccount,
    accountBreadcrumb,
  } = state;

  return (
    <Container>
      <MainContainer vertical>
        <Header>
          <Breadcrumb separator={<Icon src={images.Icon.ChevronRightGrey} />}>
            <Breadcrumb.Item className='text-grey-600'>
              Kinh doanh
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <div className='font-semibold'>Quản lý thiết bị</div>
            </Breadcrumb.Item>
          </Breadcrumb>
        </Header>
        <DeviceListPanel
          enableAddDevice
          enableSelectAccount
          enableMultipleSelect
          enableAdvanceFilter
          enableViewAllDevice={false}
          className='px-6 pb-0'
          multipleSelect={multipleSelect}
          selectedAccount={selectedAccount}
          profile={profile}
          accountBreadcrumb={accountBreadcrumb}
          selectedDevice={selectedDevice}
          onDeviceSelect={onDeviceSelect}
          openModal={openModal}
          updateState={updateState}
          onSelectAccount={onSelectAccount}
          onChangeBreadcrumb={onChangeBreadcrumb}
        />
      </MainContainer>
      <RightContainer>
        <DeviceDetailTabs
          selectedImei={selectedDevice?.imei || ''}
          openModal={openModal}
          onSelectedMultipleDevice={onSelectedMultipleDevice}
        />
      </RightContainer>
      {/* Modal */}
      <DeviceModals
        modals={modals}
        selectedAccount={selectedAccount}
        accountBreadcrumb={accountBreadcrumb}
        multipleSelect={multipleSelect}
        selectedImei={selectedDevice?.imei || ''}
        closeModal={closeModal}
        onSelectedMultipleDevice={onSelectedMultipleDevice}
        onResetMultipleDevice={onResetMultipleDevice}
      />
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 justify-between flex-[7] p-6`;
const MainContainer = tw(Flex)`py-6 flex-[3] grow h-screen`;
const Header = tw.div`px-6 flex justify-between`;

const BusinessAgency = WithRoleAccess(ManageDeviceBusiness, [UserRole.Agency]);
export default BusinessAgency;
