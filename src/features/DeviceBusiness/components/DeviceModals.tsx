import type { DeviceType } from 'types/device';

import {
  AddDeviceModal,
  DeviceLogModal,
  TransferDeviceModal,
} from 'components/DeviceModal';
import { CollapseItemData } from 'components/RRCollapseTable';

import { MODAL_TYPES } from '../constants';
import { useDeviceMutations } from '../hooks';
import { AccountBreadCrumbItem } from '../types';

interface Props {
  selectedAccount: CollapseItemData | null;
  accountBreadcrumb: AccountBreadCrumbItem[];
  multipleSelect: { isActive: boolean; multipleDevice: DeviceType[] };
  selectedImei: string;
  modals: Record<string, boolean>;
  closeModal: (modalType: string) => void;
  onSelectedMultipleDevice: (data: any) => void;
  onResetMultipleDevice: () => void;
}

const DeviceModals = (props: Props) => {
  const {
    selectedAccount,
    accountBreadcrumb,
    multipleSelect,
    selectedImei,
    modals,
    closeModal,
    onSelectedMultipleDevice,
    onResetMultipleDevice,
  } = props;
  const { transferDevices } = useDeviceMutations();

  const handleTransferMultipleAccount = (imeis: string[], userId: string) => {
    transferDevices.mutate(
      {
        imeis,
        to_user_id: userId,
        note: '',
      },
      {
        onSuccess: () => {
          closeModal(MODAL_TYPES.MOVE_DEVICE);
          onResetMultipleDevice();
        },
      },
    );
  };

  return (
    <>
      <DeviceLogModal
        deviceImei={selectedImei}
        isVisible={modals[MODAL_TYPES.DEVICE_LOG]}
        onClose={() => closeModal(MODAL_TYPES.DEVICE_LOG)}
      />

      <AddDeviceModal
        selectedAccount={selectedAccount}
        accountBreadcrumb={accountBreadcrumb}
        visible={modals[MODAL_TYPES.ADD_DEVICE]}
        onClose={() => closeModal(MODAL_TYPES.ADD_DEVICE)}
      />

      <TransferDeviceModal
        isLoading={transferDevices.isPending}
        multipleDeviceSelected={multipleSelect.multipleDevice}
        visible={modals[MODAL_TYPES.MOVE_DEVICE]}
        onSelectedMultipleDevice={onSelectedMultipleDevice}
        onClose={() => closeModal(MODAL_TYPES.MOVE_DEVICE)}
        onSubmit={handleTransferMultipleAccount}
      />
    </>
  );
};

export default DeviceModals;
