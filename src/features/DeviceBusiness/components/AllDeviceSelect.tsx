import { useMemo, useState } from 'react';

import { Checkbox, Select } from 'antd';
import { DeviceStatusOption } from 'constants/device';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdBase, BodyMdExtend } from 'assets/styles';
import { Icon } from 'components';
import {
  ICON_STYLES,
  getCombinedInputStyles,
} from 'components/FormField/styles';

const AllValueDefault = DeviceStatusOption.map(item => item.value);

export type DeviceListState = 'none' | 'select' | 'move';

interface Props {
  counterStatus: {
    [key: string]: number;
  };
  status: string[];
  onChange: (status: string[]) => void;
}

const AllDeviceSelect = (props: Props) => {
  const { counterStatus = {}, status, onChange } = props;
  const [isOpen, setIsOpen] = useState(false);

  const MappingOptionStatus = useMemo(() => {
    return DeviceStatusOption.map(item => ({
      ...item,
      label: item.label + `(${counterStatus?.[item.value] || 0})`,
    }));
  }, [counterStatus]);

  const handleFilterStatusChange = (value: string) => {
    if (status.includes(value)) {
      onChange(status.filter(v => v !== value));
      return;
    }
    onChange([...status, value]);
  };

  const handleSelectAll = () => {
    const newStatus =
      status.length === DeviceStatusOption.length ? [] : AllValueDefault;
    onChange(newStatus);
  };

  const selectedAllStatus = status.length === DeviceStatusOption.length;
  const allStatusLabel =
    t('deviceList.status.allStatuses') +
    ` (${counterStatus?.total_count || 0})`;

  return (
    <Select
      value={status}
      onSelect={handleFilterStatusChange}
      onDeselect={handleFilterStatusChange}
      labelRender={({ label }) => {
        return <span className={`${BodyMdExtend}`}>{label}</span>;
      }}
      placeholder={t('deviceList.filterPlaceholder')}
      tagRender={({ label, value }) => {
        const indexOfLabel = status.indexOf(value);
        const isFirstItem = indexOfLabel == 0;
        return (
          <span className={`flex-1 ${isFirstItem ? 'pl-2' : ''}`}>
            {selectedAllStatus ? (
              <span>{isFirstItem ? allStatusLabel : ''}</span>
            ) : (
              label
            )}
            <span>
              {indexOfLabel !== status.length - 1 && !selectedAllStatus
                ? ','
                : ''}
            </span>
          </span>
        );
      }}
      className={`cursor-pointer ${getCombinedInputStyles()}`}
      options={MappingOptionStatus}
      mode='multiple'
      maxTagCount='responsive'
      showSearch={false}
      dropdownRender={menu => {
        return (
          <>
            <div className='flex flex-row items-center justify-start px-3 py-2'>
              <div className={`flex-1 text-grey-400 ${BodyMdBase}`}>
                {allStatusLabel}
              </div>
              <Checkbox
                checked={selectedAllStatus}
                onClick={handleSelectAll}
                className={`flex-row-reverse ${
                  selectedAllStatus ? 'text-blue-200' : 'text-grey-400'
                }`}
              >
                {t('deviceList.selectedAll')}
              </Checkbox>
            </div>
            <div className='menu-container'>{menu}</div>{' '}
          </>
        );
      }}
      suffixIcon={
        <Icon
          src={images.Icon.CaretDownSm}
          alt='chevron'
          className={`${
            ICON_STYLES.suffix
          } size-6 transition-transform duration-200 ease-in-out ${
            isOpen ? 'rotate-180' : 'rotate-0'
          }`}
        />
      }
      onDropdownVisibleChange={setIsOpen}
    />
  );
};

export default AllDeviceSelect;
