import { useCallback, useEffect } from 'react';

import { useQuery } from '@tanstack/react-query';
import { Tabs } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import useDebounceValue from 'hooks/useDebounceValue';
import { FormProvider, useForm } from 'react-hook-form';

import { t } from 'i18next';
import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn } from 'services/reactQuery';

import images from 'assets/images';
import { Icon } from 'components';
import { LinkButton, PrimaryButton } from 'components/Button';
import {
  ConfigForm,
  DeviceInfoForm,
  StatusForm,
} from 'components/DeviceDetail';
import EmptyChoice from 'components/EmptyChoice';

import { MODAL_TYPES } from '../constants';
import { useDeviceDetail, useDeviceMutations } from '../hooks';
import { parseDeviceToForm, payloadUploadDevice } from '../utils';

// Define the form data interface
interface DeviceFormData {
  transport_department_id?: string;
  transport_type?: string;
  is_allow_data_transport?: boolean;
  device_name?: string;
  device_plate_number?: string;
  device_ccid?: string;
  device_sim_number?: string;
  device_category?: string;
  service_package?: string;
  activated_at?: string;
  expired_at?: string;
  not_turn_off_the_ignition_time?: number;
  max_allowable_speed?: number;
  device_pin?: string;
  gprs_interval?: number;
  min_speed?: number;
  stop_time?: number;
  timezone?: string;
  sensor_type?: string;
  stop_distance?: number;
  engine?: string;
  ignitionLock?: string;
  [key: string]: any; // For additional fields that might come from deviceDetail
}

interface DeviceDetailTabsProps {
  selectedImei: string;
  openModal: (modalType: string, modalProps?: any) => void;
  onSelectedMultipleDevice: (devices: any[]) => void;
}

const DeviceDetailTabs = ({
  selectedImei,
  openModal,
  onSelectedMultipleDevice,
}: DeviceDetailTabsProps) => {
  const { data: deviceDetail } = useDeviceDetail({
    imei: selectedImei,
  });

  const { updateDevice } = useDeviceMutations();

  const methods = useForm<DeviceFormData>({
    defaultValues: {},
  });
  const { formState, handleSubmit, watch } = methods;

  const watchDeviceSimNumber = watch('device_sim_number') as string;
  const debouncedWatchDeviceSimNumber = useDebounceValue(
    watchDeviceSimNumber,
    200,
  );

  // Todo: inventory, packageFee filter ??? keyword format filter
  const { data: SimData } = useQuery({
    queryKey: ['sim_storages', debouncedWatchDeviceSimNumber],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/sim_storages',
        query: {
          page: '1',
          per_page: '1',
          search_keyword: String(debouncedWatchDeviceSimNumber) || '',
        },
      }),
    }),
    staleTime: 30000,
    select: (data: any) => {
      return {
        data: data.sim_storages || [],
        pagination: data.pagination || {},
      };
    },
    enabled: !!(
      debouncedWatchDeviceSimNumber && debouncedWatchDeviceSimNumber !== '-'
    ),
  });

  const onSubmit = useCallback(
    data => {
      const payload = payloadUploadDevice(data);
      updateDevice.mutate({
        imei: selectedImei,
        deviceInfo: payload,
      });
    },
    [selectedImei, updateDevice],
  );

  const handleOpenTransferModal = useCallback(() => {
    onSelectedMultipleDevice([deviceDetail]);
    setTimeout(() => {
      openModal(MODAL_TYPES.MOVE_DEVICE);
    }, 0);
  }, [openModal, deviceDetail, selectedImei]);

  useEffect(() => {
    if (deviceDetail) {
      methods.reset(parseDeviceToForm(deviceDetail));
    }
  }, [deviceDetail, methods]);

  useEffect(() => {
    if (SimData?.data?.length > 0) {
      methods.setValue('device_ccid', SimData?.data[0]?.ccid);
    } else {
      methods.setValue('device_ccid', 'Không có dữ liệu');
    }
  }, [SimData, methods]);

  if (!selectedImei) {
    return <EmptyChoice content='Chọn thiết bị để xem thông tin' />;
  }

  return (
    <div>
      <div className='mb-3 flex items-center justify-between'>
        <LinkButton
          onClick={() => openModal(MODAL_TYPES.DEVICE_LOG)}
          size='small'
          iconPosition='left'
          className='h-6'
          icon={<Icon src={images.Icon.Calendar} />}
        >
          {t('deviceDetail.deviceLog')}
        </LinkButton>

        <LinkButton
          onClick={handleOpenTransferModal}
          size='small'
          iconPosition='left'
          className='h-6'
          icon={<Icon src={images.Icon.ChangeDevice} />}
        >
          {t('deviceDetail.transferDevice')}
        </LinkButton>
      </div>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className='relative'>
          <Tabs
            className='size-full'
            style={{ height: '100%' }}
            tabPosition='top'
          >
            <TabPane tab={'Thông tin tổng quan'} key='1'>
              <DeviceInfoForm />
            </TabPane>
            <TabPane tab={'Trạng Thái'} key='2'>
              <StatusForm device={deviceDetail} />
            </TabPane>
            <TabPane tab={'Cấu hình'} key='3'>
              <ConfigForm />
            </TabPane>
          </Tabs>
          <div className='absolute inset-x-[24px] bottom-[24px]'>
            <PrimaryButton
              htmlType='submit'
              className='w-full'
              disabled={
                updateDevice.isPending ||
                !formState.isDirty ||
                !formState.isValid
              }
              loading={updateDevice.isPending}
            >
              {t('deviceDetail.saveInfo')}
            </PrimaryButton>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default DeviceDetailTabs;
