import { Dayjs } from 'dayjs';

interface DateRange {
  start?: Dayjs;
  end?: Dayjs;
}

export interface FilterState {
  servicePackage: string[];
  typeOfDevice: string[];
  inventory: false | null;
  activeDateRange: DateRange;
  expiredDateRange: DateRange;
  isActive?: boolean;
  lastedUpdated: number | null;
}

export interface AdvancedFilterProps {
  className?: string;
  onFilterSelect?: (filterState: FilterState) => void;
}

export interface AdvancedFilterRef {
  reset: () => void;
}

export interface UseAdvancedFilterProps {
  onFilterSelect?: AdvancedFilterProps['onFilterSelect'];
}
