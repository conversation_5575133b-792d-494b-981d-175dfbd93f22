import * as yup from 'yup';

import { EMAIL_REGEX, PHONE_REGEX } from 'utils/regex';

// Base validation schemas
export const baseValidations = {
  username: yup.string().required('<PERSON><PERSON><PERSON> khoản là bắt buộc'),

  password: yup
    .string()
    .required('<PERSON>ật khẩu là bắt buộc')
    .min(6, '<PERSON>ật khẩu phải có ít nhất 6 ký tự'),

  passwordConfirmation: yup
    .string()
    .required('Xác nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], 'Mật khẩu xác nhận không khớp'),

  roleId: yup
    .number()
    .required('Vai trò là bắt buộc')
    .typeError('Vui lòng chọn vai trò'),

  fullName: yup
    .string()
    .required('<PERSON>ọ và tên là bắt buộc')
    .min(2, 'H<PERSON> và tên phải có ít nhất 2 ký tự')
    .max(100, '<PERSON><PERSON> và tên không được quá 100 ký tự'),

  phone: yup
    .string()
    .required('Số điện thoại là bắt buộc')
    .matches(
      PHONE_REGEX,
      'Số điện thoại không đúng định dạng (VD: 0912345678)',
    ),

  email: yup
    .string()
    .required('Email là bắt buộc')
    .matches(EMAIL_REGEX, 'Email không đúng định dạng'),

  address: yup.string().required('Địa chỉ là bắt buộc'),

  activeArea: yup.string().required('Khu vực hoạt động là bắt buộc'),
};

export const createDeviceSchema = yup.object({
  activationDate: yup.string().required('Ngày nhập là bắt buộc'),
  type: yup.string().required('Loại thiết bị là bắt buộc'),
  imei: yup.string().required('IMEI là bắt buộc'),
});
