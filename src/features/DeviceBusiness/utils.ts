import { DeviceStatus, DeviceStatusLabel } from 'constants/device';
import dayjs from 'dayjs';

export const getDeviceLabelStatus = (device: any) => {
  if (String(device?.default) === '1') {
    return DeviceStatusLabel[DeviceStatus.LostConnection] + `: (-)`;
  }

  if (
    [
      DeviceStatus.Running,
      DeviceStatus.Run,
      DeviceStatus.SlowConnection,
    ].includes(device?.status)
  ) {
    return (
      DeviceStatusLabel[device?.status] + `: ${device?.gps?.speed || 0}km/h`
    );
  }

  let seconds = 0;

  if (device?.status === DeviceStatus.Stopped) {
    seconds = device?.stopped_duration_seconds;
  } else if (
    [
      DeviceStatus.Disconnect,
      DeviceStatus.LostGPS,
      DeviceStatus.LostConnection,
    ].includes(device?.status)
  ) {
    seconds = device?.lost_connection_duration || 0;
  }

  if (!seconds) return '0 giờ 0 phút';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return DeviceStatusLabel[device?.status] + `: ${hours} giờ ${minutes} phút`;
};

export const getDeviceLabelStatusFromPoint = (currentPoint: any) => {
  if (!currentPoint) {
    return '(-)';
  }
  if (
    [
      DeviceStatus.Running,
      DeviceStatus.Run,
      DeviceStatus.SlowConnection,
    ].includes(currentPoint?.status)
  ) {
    return (
      DeviceStatusLabel[currentPoint?.status] +
      `: ${currentPoint?.speed || 0}km/h`
    );
  }

  if (!currentPoint?.start?.timestamp || !currentPoint?.end?.timestamp) {
    return '(-)';
  }

  const seconds =
    (currentPoint?.end?.timestamp - currentPoint?.start?.timestamp) / 1000;
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return (
    DeviceStatusLabel[currentPoint?.status] + `: ${hours} giờ ${minutes} phút`
  );
};

export const payloadUploadDevice = (data: any) => {
  return {
    // info
    transport_department_id: data?.transport_department_id,
    transport_type: data?.transport_type,
    is_allow_data_transport: data?.is_allow_data_transport,
    device_name: data?.device_name,
    device_plate_number: data?.device_plate_number,
    device_ccid: (data?.device_ccid || '').replace('Không có dữ liệu', ''),
    device_sim_number: data?.device_sim_number,
    device_category: data?.device_category,
    service_package: data?.service_package,
    activated_at: data?.activated_at,
    expired_at: data?.expired_at,

    // status
    not_turn_off_the_ignition_time: data?.not_turn_off_the_ignition_time,
    max_allowable_speed: data?.max_allowable_speed,
    device_pin: data?.device_pin,

    // Config
    gprs_interval: data?.gprs_interval,
    min_speed: data?.min_speed,
    stop_time: data?.stop_time,
    timezone: data?.timezone,
    sensor_type: data?.sensor_type,
    stop_distance: data?.stop_distance,
  };
};

export const parseDeviceToForm = (device: any) => {
  return {
    status: device?.status,
    default: device?.default,
    lost_connection_duration: device?.lost_connection_duration,
    stopped_duration_seconds: device?.stopped_duration_seconds,
    // info
    username: device?.username,
    transport_department_id: device?.transport_department_id,
    transport_type: device?.transport_type,
    is_allow_data_transport: device?.is_allow_data_transport,
    device_name: device?.device_name,
    device_plate_number: device?.device_plate_number,
    imei: device?.imei || device?.device_imei,
    device_sim_number: device?.device_sim_number || '',
    device_ccid: device?.device_sim_number
      ? device?.device_ccid || 'Không có dữ liệu'
      : '',
    device_category: device?.device_category,
    service_package: device?.service_package,
    activated_at: device?.activated_at,
    expired_at: device?.expired_at,
    // status
    firmware: device?.firmware,
    coordinates: `${device?.gps?.latitude || 0}, ${
      device?.gps?.longitude || 0
    }`,
    power: device?.device_status_info?.power || 0,
    gsm_signal: device?.device_status_info?.gsm_signal_percent || 0,
    satellites: device?.gps?.satellites || 0,
    engine: String(device?.device_status_info?.engine) === '1' ? 'Bật' : 'Tắt',
    acc: String(device?.device_status_info?.acc) === '1' ? 'Bật' : 'Tắt',
    not_turn_off_the_ignition_time: device?.not_turn_off_the_ignition_time,
    speed: device?.gps?.speed || 0,
    max_allowable_speed: device?.max_allowable_speed || 0,
    device_pin: device?.device_pin,
    updated_at: device?.timestamp ? dayjs(device?.timestamp) : '',
    // config
    gprs_interval: device?.gprs_interval,
    min_speed: device?.min_speed,
    stop_time: device?.stop_time,
    timezone: device?.timezone,
    sensor_type: device?.sensor_type,
    stop_distance: device?.stop_distance,
  };
};
