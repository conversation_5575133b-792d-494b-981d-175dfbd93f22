import type { DeviceType } from 'types/device';

import type { CollapseItemData } from 'components/RRCollapseTable';

export interface AppState {
  selectedAccount: CollapseItemData | null;
  accountBreadcrumb: AccountBreadCrumbItem[];
  deviceData: DeviceType[];
  selectedDevice: DeviceType | null;
  searchTerm: string;
  modals: Record<string, boolean>;
  modalProps: Record<string, any>;
  multipleSelect: { isActive: boolean; multipleDevice: DeviceType[] };
}

export interface DeviceDetailForm {
  transport_type: string;
  transport_department_id: string;
  device_category: string;
  is_allow_data_transport: boolean;
  device_name: string;
  device_plate_number: string;
  device_ccid: string;
  device_sim_number: string;
  max_allowable_speed: number;
  service_package_id?: string;
  service_package?: string;
  activated_at: string;
  expired_at: string;
  gprs_interval: number;
  min_speed: number;
  timezone: string;
  sensor_type: string;
  stop_distance: number;
  stop_time: number;
  device_pin: string;
}

export interface BreadCrumbItem {
  label: string;
  level: number;
  imei?: string;
}

export interface CreateDeviceData {
  type: string;
  owner_id: string;
  devices: Array<{
    imei: string;
    device_name?: string;
    device_plate_number?: string;
    [key: string]: any;
  }>;
}

export interface UpdateDeviceData {
  imei: string;
  [key: string]: any;
}

export interface TransferDevicesData {
  imeis: string[];
  to_user_id: string;
  note?: string;
}

export interface DeviceLogParams {
  enabled: boolean;
  imei: string;
  params: {
    action_type: string | null;
    created_at_from: string | null;
    created_at_to: string | null;
    page: number;
    per_page: number;
  };
}

export interface DeviceCountByStatusParams {
  owner_id: string;
}

export interface DeviceLogResponse {
  logs: any[];
  page: number;
  total_count: number;
  total_pages: number;
}

export interface DeviceCountByStatusResponse {
  online: number;
  offline: number;
  maintenance: number;
  total: number;
}

export interface DevicesResponse {
  devices: any[];
  page: number;
  total_count: number;
  total_pages: number;
}

export interface AccountBreadCrumbItem {
  label: string;
  level: number;
  role_id: string;
}
