import { useProfile } from 'hooks/useProfile';

import OverviewAgency from './Agency';
import OverviewUser from './User';

interface OverviewProps {}

const Overview = ({}: OverviewProps) => {
  const { profile } = useProfile();

  if (profile?.is_distributor) {
    return <OverviewAgency />;
  }
  if (profile?.is_end_user) {
    return <OverviewUser />;
  }
  return null;
};

export default Overview;
