import { useMemo } from 'react';

import { ResponsiveBar } from '@nivo/bar';
import tw from 'tailwind-styled-components';

import { useDeviceSelectors } from 'features/DeviceBusiness/hooks/useDeviceData';
import { t } from 'i18next';
import type { DeviceType } from 'types/device';
import { uppercaseString } from 'utils';

import { BodyMdBase, Colors, FontBold, TitleMd } from 'assets/styles';
import EmptyPanel from 'components/EmptyPanel';
import Select from 'components/FormField/RRSelect';

type DataPoint = {
  date: string;
  value: number;
};

const generateTickValues = (data: DataPoint[], step: number): string[] => {
  const tickValues: string[] = [];
  for (let i = 0; i < data.length; i += step) {
    tickValues.push(data[i].date);
  }
  return tickValues;
};

interface OverViewChartProps {
  currentDevice: DeviceType;
  data: { date: string; value: number }[] | undefined;
  reportSummary?: {
    total_distance: number;
    max_speed: number;
    move_time: number;
    stop_time: number;
    lost_connection_time: number;
    report_count: number;
    last_time_updated: string;
  };
  deviceList: DeviceType[];
  onChangeDevice: (device: DeviceType) => void;
}

const OverViewChart = ({
  currentDevice,
  data = [],
  reportSummary,
  deviceList,
  onChangeDevice,
}: OverViewChartProps) => {
  const { data: deviceSelect } = useDeviceSelectors();
  const tickValues = useMemo(() => generateTickValues(data, 6), [data]);
  const movingTime = useMemo(
    () => ({
      hours: Math.floor((reportSummary?.move_time || 0) / 3600),
      minutes: Math.floor(((reportSummary?.move_time || 0) % 3600) / 60),
    }),
    [reportSummary],
  );
  const deviceOptions = useMemo(() => {
    return (deviceSelect || []).map(item => ({
      label: uppercaseString(`${item.device_name} (${item.device_imei})`),
      value: item.device_imei,
    }));
  }, [deviceSelect]);

  const handleDeviceChange = (value: string) => {
    const device = (deviceList || []).find(item => item.device_imei === value);
    if (!device || device?.imei === currentDevice?.imei) {
      return;
    }
    onChangeDevice(device);
  };

  return (
    <div className='size-full rounded-lg border border-grey-100 p-6'>
      <div className='flex items-end justify-between'>
        <div className='flex flex-1 items-end gap-6'>
          <Select
            containerClassName='max-w-[200px]'
            value={currentDevice?.imei}
            id='device'
            name='device'
            label='Thiết bị'
            options={deviceOptions}
            placeholder='Chọn'
            className='h-[32px] max-h-[32px]'
            onChange={handleDeviceChange}
          />
          <div>
            <Label className={BodyMdBase}>{t('overview.movingTime')}</Label>
            <Value className={`${TitleMd} ${FontBold}`}>
              {movingTime.hours} giờ {movingTime.minutes} phút
            </Value>
          </div>
        </div>
        <div className='flex h-full flex-col justify-end'>
          <Label className={BodyMdBase}>{t('overview.data')} </Label>
          <Value className={`${BodyMdBase} ${FontBold}`}>
            30 ngày gần nhất
          </Value>
        </div>
      </div>
      <div className='mt-2 h-[calc(100%-36px)]'>
        {data.length === 0 ? (
          <div className='flex h-full items-center justify-center'>
            <EmptyPanel />
          </div>
        ) : (
          <ResponsiveBar
            data={data}
            keys={['value']}
            indexBy='date'
            margin={{ top: 20, right: 0, bottom: 50, left: 0 }}
            padding={0.7}
            colors={Colors.blue[200]}
            enableLabel={false}
            borderRadius={5}
            theme={{
              axis: {
                ticks: {
                  text: {
                    fill: Colors.grey[600],
                  },
                },
              },
            }}
            axisLeft={null}
            axisBottom={{
              tickValues: tickValues,
            }}
          />
        )}
      </div>
    </div>
  );
};

export default OverViewChart;

const Label = tw.div`text-grey-600`;
const Value = tw.div`text-black-1000`;
const Header = tw.div`flex justify-between`;
