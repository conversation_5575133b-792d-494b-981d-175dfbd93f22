import React from 'react';

import { Rate } from 'antd';
import {
  AppStoreButton,
  ButtonsContainer,
  GooglePlayButton,
} from 'react-mobile-app-button';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import {
  BodyLg,
  BodyMdBase,
  FontBold,
  FontMedium,
  TitleMd,
} from 'assets/styles';
import { Icon } from 'components';

const AppDownloadBanner = ({ className = '' }) => {
  return (
    <Container className={className}>
      <AppName>
        <div className={`${TitleMd} ${FontBold}`}>{t('overview.appName')}</div>
        <Desciption className={BodyMdBase}>
          {t('overview.appDescription')}
        </Desciption>
        <div className='mb-4 flex gap-4'>
          <IconContainer>
            <Icon
              width={'60px'}
              height={'60px'}
              src={images.Icon.NavioAppIcon}
            />
          </IconContainer>
          <div className='flex flex-col'>
            <Name className={`${BodyLg} ${FontBold}`}>
              {t('login.appName')}
            </Name>
            <RateContainer>
              <RateValue className={`${BodyMdBase} ${FontMedium}`}>
                {t('login.defaultRate')}
              </RateValue>
              <Rate
                allowHalf
                disabled
                defaultValue={4.98}
                style={{ fontSize: 16 }}
              />
            </RateContainer>
          </div>
        </div>
      </AppName>
      <div className='wrapper-mobile-buttons w-full'>
        <ButtonsContainer>
          <AppStoreButton
            className='w-full text-sm'
            url={'www.google.com'}
            theme={'dark'}
          />
          <GooglePlayButton
            className='w-full text-sm'
            url={'www.google.com'}
            theme={'dark'}
          />
        </ButtonsContainer>
      </div>
    </Container>
  );
};

export default AppDownloadBanner;

const Container = tw.div`flex-col px-4 mb-6 bg-white-1000 w-[380px] bottom-4 flex items-center justify-between`;
const AppName = tw.div`flex flex-col`;
const IconContainer = tw.div`bg-yellow-400 flex size-12 items-center justify-center rounded-full`;
const Name = tw.div`text-lg font-bold`;
const RateContainer = tw.div`flex items-center`;
const RateValue = tw.span`mr-2 text-grey-400`;
const Desciption = tw.div`text-grey-600 mt-2 mb-4`;
