import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';

import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

import {
  converPageInfo,
  convertToDeviceList,
  parseChartData,
} from '../adapters';
import { AgencyReportSummary, DeviceListResponse } from '../types';

// Device list parameters
interface DeviceListParams {
  page: number;
  search_keyword?: string;
  status?: string[];
  owner_id?: string;
  device_category?: string[];
  activated_at_from?: string;
  activated_at_to?: string;
  expired_at_from?: string;
  expired_at_to?: string;
  is_activated?: boolean;
}

// Device report summary response
interface DeviceReportResponse {
  daily: Record<string, { move_time: number }>;
  summary: {
    total_distance: number;
    max_speed: number;
    move_time: number;
    stop_time: number;
    lost_connection_time: number;
    report_count: number;
    last_time_updated: string;
  };
}

/**
 * Hook to fetch device list with pagination and filtering
 */
export const useDeviceList = (
  params: DeviceListParams,
  getAddress: boolean = false,
) => {
  const query: Record<string, string> = {
    page: params.page.toString(),
  };

  if (params.owner_id) query.owner_id = params.owner_id;
  if (params.search_keyword) query.search_keyword = params.search_keyword;
  if (params.status?.length) query.statuses = params.status.join(',');
  if (params.device_category?.length)
    query.device_category = params.device_category.join(',');
  if (params.activated_at_from)
    query.activated_at_from = params.activated_at_from;
  if (params.activated_at_to) query.activated_at_to = params.activated_at_to;
  if (params.expired_at_from) query.expired_at_from = params.expired_at_from;
  if (params.expired_at_to) query.expired_at_to = params.expired_at_to;

  return useQuery({
    queryKey: queryKeys.devices.list(params),
    queryFn: createApiQueryFn<DeviceListResponse>({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/devices',
        query,
      }),
    }),
    staleTime: 2 * 60 * 1000, // 2 minutes
    select: data => ({
      deviceList: convertToDeviceList(data),
      pageInfo: converPageInfo(data),
    }),
    enabled: !!params,
  });
};

/**
 * Hook to fetch device report summary for charts
 */
export const useDeviceReportSummary = (imei: string) => {
  const endDate = dayjs().format('YYYY-MM-DD');
  const startDate = dayjs().subtract(30, 'days').format('YYYY-MM-DD');

  return useQuery({
    queryKey: queryKeys.devices.logs(imei),
    queryFn: createApiQueryFn<DeviceReportResponse>({
      method: 'GET',
      route: `/device_reports/summary?imei=${imei}&start_date=${startDate}&end_date=${endDate}`,
    }),
    enabled: !!imei,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: data => ({
      daily: parseChartData(data.daily),
      summary: data.summary,
    }),
  });
};

/**
 * Hook to fetch agency overview statistics
 */
export const useAgencyOverview = () => {
  return useQuery({
    queryKey: ['agency', 'overview'],
    queryFn: createApiQueryFn<AgencyReportSummary>({
      method: 'GET',
      route: '/distributor_reports/summary',
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: data => data,
  });
};
