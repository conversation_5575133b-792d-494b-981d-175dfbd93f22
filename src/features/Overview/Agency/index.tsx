import { useState } from 'react';

import { Flex } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { HeaderSection, Icon } from 'components';
import { LinkButton } from 'components/Button';
import EmptyPanel from 'components/EmptyPanel';
import RRTable from 'components/RRTable';

import DeviceListOverview from '../components/DeviceListOverview';
import { useAgencyOverview } from '../hooks';

interface OverviewProps {}

type ReportType = 'installation' | 'service-expiration';

export const columns: ColumnsType<Record<string, any>> = [
  {
    title: 'Ngày kích hoạt',
    dataIndex: 'activatedDate',
    key: 'activatedDate',
    align: 'center',
  },
  {
    title: 'Tài khoản',
    dataIndex: 'accountName',
    key: 'accountName',
    align: 'center',
  },
  {
    title: 'IMEI',
    dataIndex: 'imei',
    key: 'imei',
    align: 'center',
  },
  {
    title: 'Sim',
    dataIndex: 'sim',
    key: 'sim',
    align: 'center',
  },
  {
    title: 'Địa chỉ',
    dataIndex: 'address',
    key: 'address',
    align: 'left',
  },
];

const Overview = ({}: OverviewProps) => {
  const navigate = useNavigate();

  const [reportType, setReportType] = useState<ReportType>('installation');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const { data: agencySum, isLoading } = useAgencyOverview();

  const handleRedirectToAcc = () => {
    navigate(`/business/accounts`);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const onPageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  return (
    <Container>
      <div className='relative flex w-[calc(100%-400px)] flex-col p-6'>
        <div>
          <HeaderSection content={t('overview.overview')} />
          <div className='mb-4 mt-6 flex items-center justify-between'>
            <p className='text-lg font-bold'>{t('overview.statistic')}</p>
            <LinkButton
              onClick={handleRedirectToAcc}
              size='small'
              className='text-end'
            >
              {t('overview.manageAccountAndDevice')}
            </LinkButton>
          </div>

          {isLoading ? (
            <div className='flex justify-center py-8'>Loading...</div>
          ) : (
            <div className='flex items-center justify-evenly gap-4'>
              <CardWrapper>
                <RowContainer>
                  <BadgeItem>Kích hoạt</BadgeItem>
                  <SubText>Tuần này</SubText>
                </RowContainer>
                <RowContainer className='items-end'>
                  <div className='flex items-end gap-2'>
                    <MainText>
                      {agencySum?.total_activated_devices_today || 0}
                    </MainText>
                    <SubText className='italic'>Thiết bị</SubText>
                  </div>
                  <BadgePercentage>
                    <div className='w-4'>
                      <Icon src={images.Icon.Up} />
                    </div>
                    <GreenText className='flex flex-1'>
                      {agencySum?.total_activated_devices_ytd
                        ? Math.round(
                            (agencySum?.total_activated_devices_today /
                              agencySum?.total_activated_devices_ytd) *
                              100,
                          )
                        : 0}
                      %
                    </GreenText>
                  </BadgePercentage>
                </RowContainer>
              </CardWrapper>
              <CardWrapper>
                <RowContainer>
                  <BadgeItem>Tồn kho</BadgeItem>
                  <SubText>Tổng nhập</SubText>
                </RowContainer>
                <RowContainer>
                  <div className='flex items-end gap-2'>
                    <MainText>
                      {agencySum?.total_inactive_devices || 0}/
                      {agencySum?.total_devices || 0}
                    </MainText>
                    <SubText className='italic'>Thiết bị</SubText>
                  </div>
                </RowContainer>
              </CardWrapper>
              <CardWrapper>
                <RowContainer>
                  <BadgeItem>Hết hạn dịch vụ</BadgeItem>
                  <SubText>Tháng này</SubText>
                </RowContainer>
                <RowContainer className='items-end'>
                  <div className='flex items-end gap-2'>
                    <MainText>
                      {agencySum?.fetch_expired_devices_today || 0}
                    </MainText>
                    <SubText className='italic'>Thiết bị</SubText>
                  </div>
                  <BadgePercentage>
                    <Icon src={images.Icon.Up} className='size-3' />
                    <GreenText className='flex flex-1'>
                      {agencySum?.total_expired_devices_ytd
                        ? Math.round(
                            (agencySum?.fetch_expired_devices_today /
                              agencySum?.total_expired_devices_ytd) *
                              100,
                          )
                        : 0}
                      %
                    </GreenText>
                  </BadgePercentage>
                </RowContainer>
              </CardWrapper>
            </div>
          )}
        </div>
        <div className=' flex flex-1 flex-col'>
          <div className='flex items-center justify-between pb-3 pt-6'>
            <p className='text-lg font-bold'>Báo cáo</p>
            <div className='flex items-center gap-2'>
              <ButtonTab
                type='button'
                className={
                  reportType === 'installation'
                    ? 'border border-grey-100 text-black-1000'
                    : ''
                }
                onClick={() => setReportType('installation')}
              >
                Lắp đặt thiết bị
              </ButtonTab>
              <ButtonTab
                type='button'
                className={
                  reportType === 'service-expiration'
                    ? 'border border-grey-100 text-black-1000'
                    : ''
                }
                onClick={() => setReportType('service-expiration')}
              >
                Thời hạn dịch vụ
              </ButtonTab>
            </div>
          </div>
          {/* <EmptyPanel /> */}
          <div className='flex-1'>
            <RRTable
              tableLayout='auto'
              columns={columns}
              data={[]}
              total={0}
              currentPage={currentPage}
              pageSize={pageSize}
              loading={false}
              onPageChange={handlePageChange}
              onPageSizeChange={onPageSizeChange}
            />
          </div>
        </div>
      </div>

      <RightContainer>
        <DeviceListOverview enableAdvanceFilter />
      </RightContainer>
    </Container>
  );
};

const Container = tw.div`flex size-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 justify-between`;
const GreenText = tw.span`text-green-200`;

const CardWrapper = tw.div`rounded-2xl border border-grey-100 p-4 size-full flex flex-col gap-2 justify-between`;
const BadgeItem = tw.div`rounded-lg bg-grey-50 px-2 py-1 text-xs  font-semibold text-black-1000`;
const BadgePercentage = tw.div`rounded-lg bg-green-10 px-2 py-1 text-xs  font-semibold text-green-200 flex gap-[2px] items-center justify-between`;
const MainText = tw.p`text-[32px] font-semibold text-black-1000 leading-[40px] mt-1`;
const SubText = tw.p` text-grey-600 text-[14px]`;
const RowContainer = tw.div`flex items-center justify-between gap-4`;
const ButtonTab = tw.button`rounded-lg px-4 py-1 text-[14px] font-medium text-grey-400 hover:bg-grey-100 border border-transparent`;

export default Overview;
