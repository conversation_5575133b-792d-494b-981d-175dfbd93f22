import { useEffect, useMemo, useState } from 'react';

import dayjs from 'dayjs';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import { BodyMdBase, BodySm, BodyXl, FontBold, TitleMd } from 'assets/styles';
import { HeaderSection, RRStaticMap } from 'components';
import { PinType } from 'components/RRStaticMap';
import Spinner from 'components/Spinner';

import AppDownloadBanner from '../components/AppDownloadBanner';
import DeviceListOverview from '../components/DeviceListOverview';
import OverViewChart from '../components/OverviewChart';
import { useDeviceReportSummary } from '../hooks';

const Overview = () => {
  const [deviceList, setDeviceList] = useState<any>([]);
  const [currentDevice, setCurrentDevice] = useState<any>(null);

  const { data: deviceReportSummary, isLoading } = useDeviceReportSummary(
    currentDevice?.imei ?? '',
  );

  useEffect(() => {
    if (!currentDevice && deviceList.length > 0) {
      setCurrentDevice(deviceList[0]);
    }
  }, [deviceList, currentDevice]);

  const lastedUpdated = useMemo(() => {
    const dateTime = deviceReportSummary?.summary?.last_time_updated
      ? dayjs(deviceReportSummary.summary.last_time_updated)
      : dayjs();
    return {
      date: dateTime.format('DD/MM/YYYY'),
      time: dateTime.format('HH:mm:ss'),
      day: dateTime.format('dddd'),
    };
  }, [deviceReportSummary?.summary?.last_time_updated]);

  const deviceMarker = useMemo(() => {
    return deviceList.map((item: any) => ({
      position: { lat: item?.gps?.latitude, lng: item?.gps?.longitude },
      label: item.device_plate_number,
      deviceHalfOfCourse: item?.gps?.half_of_course || 0,
      deviceStatus: item?.status,
    }));
  }, [deviceList]);

  return (
    <Container>
      <MainContainer>
        <div className='flex h-[84px] justify-between'>
          <HeaderSection content={t('overview.overview')} />
          <DateTimeSection>
            <LatestUpdated className={`${BodySm}`}>
              {t('overview.latestUpdate')}
            </LatestUpdated>
            <Flex>
              <DateBox className={`${TitleMd} ${FontBold}`}>
                {lastedUpdated.day}
              </DateBox>
              <DateTime>
                <Date className={BodyMdBase}>{lastedUpdated.date}</Date>
                <Time className={`${BodyXl} ${FontBold}`}>
                  {lastedUpdated.time}
                </Time>
              </DateTime>
            </Flex>
          </DateTimeSection>
        </div>
        <div className='flex h-[330px] min-h-[330px] w-full items-center justify-center 4xl:h-[400px] 4xl:min-h-[400px]'>
          {isLoading ? (
            <Spinner />
          ) : (
            <OverViewChart
              currentDevice={currentDevice}
              deviceList={deviceList}
              data={deviceReportSummary?.daily}
              reportSummary={deviceReportSummary?.summary}
              onChangeDevice={setCurrentDevice}
            />
          )}
        </div>
        <div className='h-full'>
          <div className={`${FontBold} mb-2`}>{t('mapComponent.mapQuery')}</div>
          <RRStaticMap
            pinType={PinType.Car}
            markerList={deviceMarker}
            className='h-[calc(100%-32px)] overflow-hidden rounded-xl'
            center={{
              lat: currentDevice?.gps?.latitude,
              lng: currentDevice?.gps?.longitude,
            }}
          />
        </div>
      </MainContainer>
      <RightContainer>
        <DeviceListOverview onUpdateDeviceList={setDeviceList} />
        <AppDownloadBanner className='w-[400px] px-6' />
      </RightContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 justify-between`;
const MainContainer = tw.div`p-6 grow overflow-y max-h-screen flex flex-col gap-6`;
const DateTimeSection = tw.div`flex flex-col items-center rounded-xl border border-grey-100 overflow-hidden`;
const DateBox = tw.span`mr-2 flex h-full items-center border-r  border-grey-100 px-3 py-3.5`;
const DateTime = tw.div`mr-2 flex flex-col py-2`;
const Date = tw.span`text-grey-400`;
const Time = tw.span``;
const Flex = tw.div`flex`;
const LatestUpdated = tw.div`w-full border-b border-grey-100 bg-grey-50 py-1 text-center text-grey-400`;

export default Overview;
