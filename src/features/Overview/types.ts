export interface GPS {
  timestamp: number;
  latitude: number;
  longitude: number;
  locating_information: string;
  satellites: number;
  speed: number;
  distance: number;
  half_of_course: number;
}

export interface Device {
  imei: string;
  device_name: string;
  device_description: string;
  gps: GPS;
  status: string;
  device_plate_number?: string;
  inactive_duration?: number;
  timestamp?: number;
  latitude?: number;
  longitude?: number;
  locating_information?: string;
  satellites?: number;
  speed?: number;
  distance?: number;
  half_of_course?: number;
  device_category?: string;
  expired_at?: string;
}

export interface DeviceListResponse {
  devices: Device[];
  page: number;
  total_pages: number;
  total_count: number;
}

export interface PageInfo {
  page: number;
  totalPages: number;
  totalCount: number;
}

export interface AgencyReportSummary {
  total_devices: number;
  total_users: number;
  total_inactive_devices: number;
  total_activated_devices_today: number;
  total_activated_devices_ytd: number;
  fetch_expired_devices_today: number;
  total_expired_devices_ytd: number;
  user_locations: {
    address: string;
    user_count: number;
  }[];
}
