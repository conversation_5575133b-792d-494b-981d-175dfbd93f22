import dayjs from 'dayjs';

import { DeviceListResponse, PageInfo } from './types';

export const convertToDeviceList = (data: DeviceListResponse): any[] => {
  return data?.devices?.map((device, index) => {
    // Prefer nested gps object, fallback to flat fields
    const gps = device.gps ?? {
      timestamp: device.timestamp,
      latitude: device.latitude,
      longitude: device.longitude,
      locatingInformation: device.locating_information,
      satellites: device.satellites,
      speed: device.speed,
      distance: device.distance,
      halfOfCourse: device.half_of_course,
    };

    return {
      id: index + 1,
      deviceName: device.device_name ?? '',
      imei: device.imei ?? '',
      deviceDescription: device.device_description ?? '',
      address: '',
      gps: gps,
      lat: gps.latitude,
      lng: gps.longitude,
      status: device.status ?? '',
      deviceCategory: device.device_category,
      speed:
        gps.speed !== undefined && gps.speed !== null ? String(gps.speed) : '',
      expiredDate: device.expired_at
        ? dayjs(device.expired_at).format('DD/MM/YYYY')
        : '-',
      vehicleInfo: '',
      plate: device.device_plate_number ?? '',
      inactiveDuration: device.inactive_duration ?? null,
    };
  });
};

export const converPageInfo = (data: DeviceListResponse): PageInfo => {
  return {
    page: data.page,
    totalPages: data.total_pages,
    totalCount: data.total_count,
  };
};

export const parseChartData = (
  dailyData: Record<string, { move_time: number }>,
) => {
  return Object.entries(dailyData).map(([date, data]) => ({
    date: dayjs(date).format('DD-[Tháng] M'),
    value: data.move_time / 3600,
  }));
};
