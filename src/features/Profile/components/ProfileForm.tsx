import { useCallback, useEffect, useState } from 'react';

import { Avatar, Flex, Upload, UploadProps } from 'antd';
import { useProfile } from 'hooks/useProfile';
import { yupResolver } from 'hooks/useYupResolver';
import { useForm } from 'react-hook-form';
import { NameInitialsAvatar } from 'react-name-initials-avatar';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { USER_ID_KEY, getCookie } from 'services/cookies';

import images from 'assets/images';
import { Colors } from 'assets/styles';
import { BodyMdExtend, FontSemibold } from 'assets/styles';
import { PrimaryButton, SecondaryButton } from 'components/Button';
import RRFieldInput from 'components/FormField/RRFieldInput';
import showToast from 'components/RRToastMessage/Toast';

import { useProfileMutations } from '../hooks/useProfileMutations';
import { updateProfileSchema } from '../schema/profileSchemas';

const ProfileForm: React.FC<{ className?: string }> = ({ className }) => {
  const { profile } = useProfile();
  const { updateProfile, uploadAvatar } = useProfileMutations();
  const id = getCookie(USER_ID_KEY);

  const [avatar, setAvatar] = useState<string | undefined>(
    profile?.avatar_url ? `https://core.navio.asia/${profile?.avatar_url}` : '',
  );

  const methods = useForm({
    mode: 'onBlur',
    resolver: yupResolver(updateProfileSchema),
    defaultValues: {
      username: '',
      role_id: 0,
      full_name: '',
      phone_no: '',
      email: '',
      address: '',
      active_area: 'Việt Nam',
    },
  });

  const { handleSubmit, control, formState, reset } = methods;
  const { errors } = formState;

  useEffect(() => {
    if (profile) {
      reset({
        username: profile?.username ?? '',
        role_id: profile.role_id ?? 0,
        full_name: profile.full_name ?? '',
        phone_no: profile.phone_no ?? '',
        email: profile.email ?? '',
        address: profile.address ?? '',
        active_area: profile.active_area ?? '',
      });
      if (profile.avatar_url) {
        setAvatar(`https://core.navio.asia/${profile.avatar_url}`);
      }
    }
  }, [profile, reset]);

  const onSubmit = useCallback(
    data => {
      const { account, ...rest } = data;
      if (id) {
        rest.id = +id;

        // Use React Query mutation instead of Redux action
        updateProfile.mutate({
          data: rest,
          userId: +id,
        });
      }
    },
    [updateProfile, id],
  );

  const handleChangeFile: UploadProps['onChange'] = ({ file }) => {
    // validate file

    if (
      file.name.split('.').pop() !== 'jpg' &&
      file.name.split('.').pop() !== 'png' &&
      file.name.split('.').pop() !== 'gif'
    ) {
      showToast('failed', 'File upload phải là file jpg, png, gif');
      return;
    }

    if (file && !!profile?.id) {
      // Use React Query mutation for avatar upload
      uploadAvatar.mutate({
        id: profile.id,
        file: file as any,
      });
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setAvatar(URL.createObjectURL(file as any));
  };

  return (
    <div className={className}>
      <div className='mb-6'>
        <EmailLabel className='text-sm font-medium text-grey-400'>
          Ảnh đại diện
        </EmailLabel>

        <Flex align='center'>
          {avatar ? (
            <Avatar className='mr-6' size={88} src={avatar} />
          ) : (
            <div className='mr-6 size-[88px]'>
              <NameInitialsAvatar
                bgColor={Colors.brand[300]}
                borderColor={Colors.brand[300]}
                name={profile?.username?.toUpperCase() || ''}
                size='88px'
              />
            </div>
          )}
          <Flex vertical>
            <EmailField>
              Tối thiểu 800x800 được khuyến nghị
              <br />
              Định dạng JPG or PNG,GIF
            </EmailField>
            <SecondaryButton
              className='mt-2 h-8 w-fit'
              disabled={uploadAvatar.isPending}
              htmlType='button'
              variant='outline'
            >
              <Upload
                beforeUpload={() => false}
                onChange={handleChangeFile}
                showUploadList={false}
                disabled={uploadAvatar.isPending}
              >
                {uploadAvatar.isPending ? 'Đang tải lên...' : 'Tải lên ảnh mới'}
              </Upload>
            </SecondaryButton>
          </Flex>
        </Flex>
      </div>
      <form onSubmit={handleSubmit(onSubmit)} {...methods}>
        <div className='flex flex-col gap-3'>
          <div className='flex w-full items-center justify-between border-b border-grey-100 pb-4'>
            <EmailLabel className={`${BodyMdExtend} ${FontSemibold}`}>
              {t('profile.email')}
            </EmailLabel>
            <div className='flex items-center gap-4'>
              <EmailField>{profile?.email}</EmailField>
              <div className='flex h-6 items-center'>
                <svg
                  width='8'
                  height='12'
                  viewBox='0 0 8 12'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    fillRule='evenodd'
                    clipRule='evenodd'
                    d='M1.05806 0.558058C1.30214 0.313981 1.69786 0.313981 1.94194 0.558058L6.94194 5.55806C7.18602 5.80214 7.18602 6.19786 6.94194 6.44194L1.94194 11.4419C1.69786 11.686 1.30214 11.686 1.05806 11.4419C0.813981 11.1979 0.813981 10.8021 1.05806 10.5581L5.61612 6L1.05806 1.44194C0.813981 1.19786 0.813981 0.802136 1.05806 0.558058Z'
                    fill='#54565C'
                  />
                </svg>
              </div>
            </div>
          </div>
          <RRFieldInput
            id='username'
            label={t('accountForm.account')}
            control={control}
            disabled
            placeholder={t('accountForm.account')}
            prefixIcon={images.Icon.User}
            defaultValue={methods.getValues('username')}
          />
          <RRFieldInput
            required
            id='fullName'
            name='full_name'
            label={t('accountForm.fullName')}
            control={control}
            placeholder={t('accountForm.enterFullName')}
            errors={errors}
            prefixIcon={images.Icon.AaIcon}
            defaultValue={methods.getValues('full_name')}
          />
          <RRFieldInput
            required
            id='phoneNumber'
            name='phone_no'
            label={t('accountForm.phoneNumber')}
            control={control}
            placeholder={t('accountForm.enterPhoneNumber')}
            errors={errors}
            prefixIcon={images.Icon.PhoneCall}
            defaultValue={methods.getValues('phone_no')}
          />
          <RRFieldInput
            required
            id='address'
            name='address'
            label={t('accountForm.address')}
            control={control}
            placeholder={t('accountForm.enterAddress')}
            errors={errors}
            prefixIcon={images.Icon.PinLocation}
            defaultValue={methods.getValues('address')}
          />
          <RRFieldInput
            id='active_area'
            label={t('accountForm.operatingArea')}
            control={control}
            placeholder={t('accountForm.vietnam')}
            prefixIcon={images.Icon.VNFlag}
          />
          <PrimaryButton
            htmlType='submit'
            className='mt-3'
            disabled={
              updateProfile.isPending ||
              !formState.isValid ||
              !formState.isDirty
            }
            loading={updateProfile.isPending}
          >
            {t('profile.saveInfoButton')}
          </PrimaryButton>
        </div>
      </form>
    </div>
  );
};

const EmailField = tw.div`text-sm text-grey-600 leading-[24px] overflow-hidden text-ellipsis whitespace-nowrap`;
const EmailLabel = tw.div`overflow-hidden text-ellipsis whitespace-nowrap`;

export default ProfileForm;
