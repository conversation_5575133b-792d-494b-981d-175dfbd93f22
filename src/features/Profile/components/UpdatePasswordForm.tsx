import { useCallback, useState } from 'react';

import { yupResolver } from 'hooks/useYupResolver';
import { useForm } from 'react-hook-form';

import { useLogoutMutation } from 'features/Login/hooks/useLoginMutations';
import { t } from 'i18next';
import { USER_ID_KEY, getCookie } from 'services/cookies';

import images from 'assets/images';
import { PrimaryButton } from 'components/Button';
import { LinkButton } from 'components/Button';
import RRFieldPassword from 'components/FormField/RRFieldPassword';

import { useProfileMutations } from '../hooks/useProfileMutations';
import { updatePasswordSchema } from '../schema/profileSchemas';

const UpdatePasswordForm: React.FC<{ className?: string }> = ({
  className,
}) => {
  const logoutMutation = useLogoutMutation();
  const id = getCookie(USER_ID_KEY);
  const { updatePassword } = useProfileMutations();
  const [formKey, setFormKey] = useState(Math.random());

  const methods = useForm({
    mode: 'onBlur',
    resolver: yupResolver(updatePasswordSchema),
    defaultValues: {
      current_password: '',
      password: '',
      password_confirmation: '',
    },
  });

  const { handleSubmit, control, formState, reset } = methods;
  const { errors } = formState;

  const onSubmit = useCallback(
    data => {
      if (id) {
        // Transform data to match API expectations
        const transformedData = {
          newPassword: data.password,
          confirmPassword: data.password_confirmation,
          currentPassword: data.current_password,
        };

        // Use React Query mutation instead of Redux action
        updatePassword.mutate(
          {
            data: transformedData,
            userId: +id,
          },
          {
            onSuccess: () => {
              // Reset form on success
              reset();
              setFormKey(Math.random());
            },
          },
        );
      }
    },
    [id, updatePassword, reset],
  );

  const handleRedirectToForgotPassword = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        window.location.href = '/login?forget_password=true';
      },
    });
  };

  return (
    <div className={className}>
      <form key={formKey} onSubmit={handleSubmit(onSubmit)} {...methods}>
        <div className='flex flex-col gap-3'>
          <RRFieldPassword
            control={control}
            required
            id='current_password'
            label={t('profile.currentPassword')}
            placeholder='Nhập mật khẩu hiện tại'
            prefixIcon={images.Icon.Lock}
            errors={errors}
          />
          <RRFieldPassword
            control={control}
            required
            id='password'
            label={t('profile.newPassword')}
            placeholder='Nhập mật khẩu mới'
            prefixIcon={images.Icon.Lock}
            errors={errors}
          />
          <RRFieldPassword
            control={control}
            required
            id='password_confirmation'
            label={t('profile.confirmPassword')}
            placeholder='Nhập lại mật khẩu mới'
            prefixIcon={images.Icon.Lock}
            errors={errors}
          />

          <PrimaryButton
            htmlType='submit'
            className='mt-3'
            disabled={
              updatePassword.isPending ||
              !formState.isValid ||
              !formState.isDirty
            }
            loading={updatePassword.isPending}
          >
            {t('profile.updatePasswordButton')}
          </PrimaryButton>
          <LinkButton
            onClick={handleRedirectToForgotPassword}
            size='small'
            className='mx-auto h-6 w-fit text-end hover:bg-transparent'
          >
            {t('login.forgotPassword')}
          </LinkButton>
        </div>
      </form>
    </div>
  );
};

export default UpdatePasswordForm;
