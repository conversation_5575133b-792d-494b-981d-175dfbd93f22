import { useCallback, useEffect, useMemo } from 'react';

import { yupResolver } from 'hooks/useYupResolver';
import { useForm } from 'react-hook-form';

import { useDeviceSelectors } from 'features/DeviceBusiness/hooks/useDeviceData';
import { t } from 'i18next';
import { uppercaseString } from 'utils';

import images from 'assets/images';
import { RRFieldSelect } from 'components';
import { Icon } from 'components';
import { PrimaryButton } from 'components/Button';
import RRFieldInput from 'components/FormField/RRFieldInput';
import RRCreditCard from 'components/RRCreditCard';

import { useDriverInfo } from '../hooks/useProfileData';
import { useProfileMutations } from '../hooks/useProfileMutations';
import { updateDriverInfoSchema } from '../schema/profileSchemas';

const DriverInfoForm: React.FC<{ className?: string }> = ({ className }) => {
  const methods = useForm({
    mode: 'onBlur',
    resolver: yupResolver(updateDriverInfoSchema),
    defaultValues: {
      device_imei: '',
      driver_tag: '',
      driver_name: '',
      driver_license: '',
      rfid_command: '',
    },
  });

  const { handleSubmit, formState, reset, watch, control } = methods;
  const watchFormValues = watch();

  const { data: deviceSelect } = useDeviceSelectors();
  const { updateDriverInfo } = useProfileMutations();

  const options = useMemo(() => {
    return (deviceSelect || []).map(item => ({
      label: uppercaseString(`${item.device_name} (${item.device_imei})`),
      value: item.device_imei,
    }));
  }, [deviceSelect]);

  const { data: driverInfo } = useDriverInfo({
    imei: watchFormValues.device_imei,
    enabled: !!watchFormValues.device_imei,
  });

  const { errors } = formState;

  const onSubmit = useCallback(
    data => {
      updateDriverInfo.mutate({
        data,
        userId: data.device,
      });
    },
    [updateDriverInfo],
  );

  useEffect(() => {
    if (options.length > 0) {
      methods.setValue('device_imei', options[0].value);
    }
  }, [options?.length]);

  useEffect(() => {
    if (driverInfo?.driver) {
      reset({
        ...methods.getValues(),
        driver_tag: driverInfo.driver.driver_tag || '',
        driver_name: driverInfo.driver.driver_name || '',
        phone_number: driverInfo.driver.phone_number || '',
        driver_license: driverInfo.driver.driver_license || '',
        rfid_command: driverInfo.driver.rfid_command || '',
      });
    }
  }, [driverInfo, reset]);

  return (
    <div className={className}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        {...methods}
        className='flex max-w-5xl flex-row gap-6'
      >
        <div className='flex h-fit w-2/3 flex-1 flex-col rounded-2xl border border-grey-100 p-6'>
          <div className='flex flex-col gap-3'>
            <RRFieldSelect
              id='device_imei'
              name='device_imei'
              control={control}
              label={t('deviceDetail.deviceType')}
              options={options}
              errors={errors}
            />
            <RRFieldInput
              id='driver_tag'
              name='driver_tag'
              control={control}
              label='Mã thẻ'
              placeholder='Nhập mã thẻ'
              defaultValue={methods.getValues('driver_tag')}
              prefixIcon={images.Icon.CodeSquare}
              errors={errors}
            />
          </div>
        </div>
        <div className='w-360 flex flex-col gap-6 rounded-2xl border border-grey-100 p-6'>
          <RRCreditCard
            cardNumber={watchFormValues.driver_tag}
            cardHolderName={watchFormValues.driver_name}
            cardId={watchFormValues.driver_license}
          />
          <div className='flex flex-col gap-3'>
            <RRFieldInput
              required
              id='driver_name'
              name='driver_name'
              control={control}
              label={t('accountForm.fullName')}
              placeholder={t('accountForm.enterFullName')}
              errors={errors}
              prefixIcon={images.Icon.User}
              defaultValue={methods.getValues('fullName')}
            />
            <RRFieldInput
              required
              id='driver_license'
              name='driver_license'
              control={control}
              label={t('profile.driverLicenseLabel')}
              placeholder={t('profile.driverLicensePlaceholder')}
              prefixIcon={images.Icon.UserSquare}
              defaultValue={methods.getValues('driverLicense')}
            />
            <RRFieldInput
              required
              id='rfid_command'
              name='rfid_command'
              control={control}
              label={t('profile.queryInputLabel')}
              placeholder={t('accountForm.enterPhoneNumber')}
              errors={errors}
              defaultValue={methods.getValues('queryInput')}
              suffixIcon={
                <div role='button' className='size-4 cursor-pointer '>
                  <Icon src={images.Icon.Copy} alt='copy' className='' />
                </div>
              }
            />
            <PrimaryButton
              htmlType='submit'
              className='mt-3'
              disabled={
                updateDriverInfo.isPending ||
                !formState.isValid ||
                !formState.isDirty
              }
              loading={updateDriverInfo.isPending}
            >
              {t('profile.writeCardButton')}
            </PrimaryButton>
          </div>
        </div>
      </form>
    </div>
  );
};

export default DriverInfoForm;
