import * as yup from 'yup';

import { EMAIL_REGEX, PHONE_REGEX } from 'utils/regex';

// Base validation schemas
export const baseValidations = {
  username: yup.string().required('<PERSON><PERSON><PERSON> khoản là bắt buộc'),

  current_password: yup
    .string()
    .required('<PERSON>ật khẩu là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),

  password: yup
    .string()
    .required('Mật khẩu là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
    .test(
      'different-from-current',
      '<PERSON>ật khẩu mới phải khác mật khẩu cũ',
      function (value) {
        if (!value) return true; // Skip validation if empty
        const { current_password } = this.parent;
        return value !== current_password;
      },
    ),

  passwordConfirmation: yup
    .string()
    .required('<PERSON><PERSON><PERSON> nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], '<PERSON>ậ<PERSON> khẩu xác nhận không khớp'),

  roleId: yup
    .number()
    .required('Vai trò là bắt buộc')
    .typeError('Vui lòng chọn vai trò'),

  fullName: yup
    .string()
    .required('Họ và tên là bắt buộc')
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
    .max(100, 'Họ và tên không được quá 100 ký tự'),

  phone: yup
    .string()
    .required('Số điện thoại là bắt buộc')
    .matches(
      PHONE_REGEX,
      'Số điện thoại không đúng định dạng (VD: 0912345678)',
    ),

  email: yup
    .string()
    .required('Email là bắt buộc')
    .matches(EMAIL_REGEX, 'Email không đúng định dạng'),

  address: yup.string().required('Địa chỉ là bắt buộc'),

  activeArea: yup.string().required('Khu vực hoạt động là bắt buộc'),
};

// Update Profile Schema
export const updateProfileSchema = yup.object({
  username: baseValidations.fullName,
  role_id: yup.string().required('Vai trò là bắt buộc'),
  phone_no: baseValidations.phone,
  email: baseValidations.email,
  address: baseValidations.address,
  active_area: baseValidations.activeArea,
});

export const updatePasswordSchema = yup.object({
  current_password: baseValidations.current_password,
  password: baseValidations.password,
  password_confirmation: baseValidations.passwordConfirmation,
});

export const updateDriverInfoSchema = yup.object({
  device: yup.string().required('Thiết bị là bắt buộc'),
  driver_tag: yup.string().required('Mã thẻ là bắt buộc'),
  driver_name: yup.string().required('Họ và tên là bắt buộc'),
  driver_license: yup.string().required('Giấy phép lái xe là bắt buộc'),
  rfid_command: yup.string().required('Câu lệnh là bắt buộc'),
});

// Export types
export type UpdateProfileFormData = yup.InferType<typeof updateProfileSchema>;
