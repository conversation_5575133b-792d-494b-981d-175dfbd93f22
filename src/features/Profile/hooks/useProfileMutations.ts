import { useMutation, useQueryClient } from '@tanstack/react-query';

import { removeUserLoginCookie } from 'services/cookies';
import { createApiMutationFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

import showToast from 'components/RRToastMessage/Toast';

// Type definitions for mutations
interface UpdateProfileData {
  role_id: number;
  [key: string]: any;
}

interface UpdatePasswordData {
  newPassword: string;
  confirmPassword: string;
}

interface UploadAvatarData {
  id: string | number;
  file: File;
}
interface DriverInfoForm {
  device_imei: string;
  driver_tag: string;
  driver_name: string;
  phone_number: string;
  driver_license: string;
  rfid_command: string;
}

export const useProfileMutations = () => {
  const queryClient = useQueryClient();

  // Invalidate profile queries
  const invalidateProfile = (userId?: string | number) => {
    if (userId) {
      queryClient.invalidateQueries({
        queryKey: queryKeys.profile.detail(userId.toString()),
      });
    }
    queryClient.invalidateQueries({
      queryKey: queryKeys.profile.current,
    });
  };

  const updateProfile = useMutation<
    any,
    Error,
    { data: UpdateProfileData; userId: string | number }
  >({
    mutationFn: createApiMutationFn<
      any,
      { data: UpdateProfileData; userId: string | number }
    >(({ data, userId }) => ({
      method: 'PUT',
      route: `/users/${userId}`,
      data,
    })),
    onSuccess: (response, { userId }) => {
      invalidateProfile(userId);
      showToast('success', 'Cập nhật thông tin thành công');
    },
  });

  const updatePassword = useMutation<
    any,
    Error,
    { data: UpdatePasswordData; userId: string | number }
  >({
    mutationFn: createApiMutationFn<
      any,
      { data: UpdatePasswordData; userId: string | number }
    >(({ data, userId }) => ({
      method: 'PUT',
      route: `/users/${userId}`,
      data,
    })),
    onSuccess: (response, { userId }) => {
      invalidateProfile(userId);
      showToast('success', 'Đổi mật khẩu thành công');
    },
  });

  const uploadAvatar = useMutation<any, Error, UploadAvatarData>({
    mutationFn: createApiMutationFn<any, UploadAvatarData>(({ id, file }) => {
      const formData = new FormData();
      formData.append('avatar', file);

      return {
        method: 'POST',
        route: `/users/${id}/upload_avatar`,
        data: formData,
        headersConfig: { 'Content-Type': 'multipart/form-data' },
      };
    }),
    onSuccess: (response, { id }) => {
      invalidateProfile(id);
      showToast('success', 'Tải lên avatar thành công');
    },
  });

  const updateDriverInfo = useMutation<
    any,
    Error,
    { data: DriverInfoForm; userId: string | number }
  >({
    mutationFn: createApiMutationFn<
      any,
      { data: DriverInfoForm; userId: string | number }
    >(({ data }) => ({
      method: 'PUT',
      route: `/devices/${data.device_imei}/driver`,
      data,
    })),
    onSuccess: (response, { userId }) => {
      invalidateProfile(userId);
      showToast('success', 'Thẻ đã được ghi');
    },
  });

  const signOut = useMutation<any, Error, void>({
    mutationFn: createApiMutationFn<any>(() => ({
      method: 'delete',
      route: `/users/sign_out`,
    })),
    onSuccess: () => {
      removeUserLoginCookie();
      window.location.href = '/login';
    },
  });

  // Return all mutations with loading states
  return {
    updateProfile,
    updatePassword,
    uploadAvatar,
    updateDriverInfo,
    signOut,
  };
};
