import { useQuery } from '@tanstack/react-query';

import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

export const useDriverInfo = ({ imei, enabled }) => {
  return useQuery({
    queryKey: queryKeys.profile.driverInfo(imei),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/devices/${imei}/driver`,
    }),
    enabled: !!imei && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data: any) => {
      return data.driver || {};
    },
  });
};
