import { Breadcrumb, Flex } from 'antd';
import tw from 'tailwind-styled-components';

import { UserRole } from 'types/UserRoleTypes';

import images from 'assets/images';
import { Icon } from 'components';
import WithRoleAccess from 'components/WithRoleAccess';

import SimListPanel from './components/SimListPanel';
import SimModals from './components/SimModals';
import SimDetail from './components/SimpleDetail';
import { useSimState } from './hooks/useSimState';

interface BusinessProps {}

const SimBusiness = ({}: BusinessProps) => {
  const { state, openModal, onSimSelect, closeModal, onResetSimSelected } =
    useSimState();
  const { selectedSim } = state;

  return (
    <Container>
      <MainContainer vertical>
        <Header>
          <Breadcrumb separator={<Icon src={images.Icon.ChevronRightGrey} />}>
            <Breadcrumb.Item className='text-grey-600'>
              Kinh doanh
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <div className='font-semibold'>Kho Sim</div>
            </Breadcrumb.Item>
          </Breadcrumb>
        </Header>
        <SimListPanel
          enableAddSim
          enableAdvanceFilter
          selectedSim={selectedSim}
          openModal={openModal}
          onSimSelect={onSimSelect}
        />
      </MainContainer>
      <RightContainer>
        <SimDetail selectedSim={selectedSim} openModal={openModal} />
      </RightContainer>
      {/* Modal */}
      <SimModals
        selectedSim={selectedSim}
        modals={state.modals}
        closeModal={closeModal}
        onResetSimSelected={onResetSimSelected}
      />
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 justify-between flex-[7] p-6 `;
const MainContainer = tw(Flex)`py-6 flex-[3] grow h-screen`;
const Header = tw.div`px-6 flex justify-between mb-6`;

const SimAgency = WithRoleAccess(SimBusiness, [UserRole.Agency]);
export default SimAgency;
