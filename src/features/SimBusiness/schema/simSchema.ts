import * as yup from 'yup';

export const baseValidations = {
  device_imei: yup.string().required('IMEI là bắt buộc'),
  ccid: yup.string().required('CCID là bắt buộc'),
  sim_number: yup.string().required('<PERSON><PERSON> điện thoại là bắt buộc'),
  network_operator: yup.string().required('Nhà mạng là bắt buộc'),
  service_package_name: yup.string().required('G<PERSON>i cước là bắt buộc'),
};

export const createSimSchema = yup.object({
  // device_imei: baseValidations.device_imei,
  ccid: baseValidations.ccid,
  sim_number: baseValidations.sim_number,
  network_operator: baseValidations.network_operator,
  service_package_name: baseValidations.service_package_name,
});
