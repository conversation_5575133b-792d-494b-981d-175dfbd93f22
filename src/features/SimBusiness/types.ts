import { Dayjs } from 'dayjs';

import type { SimType } from 'types/sim';

export interface AppState {
  simData: SimType[];
  selectedSim: SimType | null;
  searchTerm: string;
  modals: Record<string, boolean>;
  modalProps: Record<string, any>;
}

export interface CreateSimType {
  device_imei?: string;
  ccid: string;
  sim_number: string;
  network_operator: string;
  active_date: string;
  expire_date: string;
  service_package_name: string;
}

export interface FilterState {
  typeOfSim: string[];
  servicePackage: string[];
  inventory: boolean | null;
  activeDateRange: { start?: Dayjs; end?: Dayjs };
  expiredDateRange: { start?: Dayjs; end?: Dayjs };
  isActive: boolean;
  lastedUpdated: number | null;
}

export interface AdvancedFilterProps {
  className?: string;
  ServicePackageOptions: { value: string; label: string }[];
  onFilterSelect?: (filterState: FilterState) => void;
}

export interface AdvancedFilterRef {
  reset: () => void;
}

export interface UseAdvancedFilterProps {
  onFilterSelect?: AdvancedFilterProps['onFilterSelect'];
}
