import { useMutation, useQueryClient } from '@tanstack/react-query';

import { createApiMutationFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

import showToast from 'components/RRToastMessage/Toast';

import { CreateSimType } from '../types';

export const useSimMutations = () => {
  const queryClient = useQueryClient();

  const invalidateSimList = () => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.sims.all,
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.sims.servicePackages(),
    });
  };

  const createSim = useMutation<any, Error, CreateSimType[]>({
    mutationFn: createApiMutationFn<any, any>((data: CreateSimType) => ({
      method: 'POST',
      route: '/sim_storages/imports',
      data: {
        sim_storages: data,
      },
    })),
    onSuccess: res => {
      invalidateSimList();
      showToast('success', res?.message || 'Nhập sim thành công');
    },
  });

  const updateSim = useMutation<any, Error, { id: string; simInfo: any }>({
    mutationFn: createApiMutationFn<any, { id: string; simInfo: any }>(
      ({ id, simInfo }) => ({
        method: 'PUT',
        route: `/sim_storages/${id}`,
        data: simInfo,
      }),
    ),
    onSuccess: (res, { id }) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.sims.detail(id),
      });
      invalidateSimList();
      showToast('success', res?.message || 'Cập nhật sim thành công');
    },
  });

  const importsSim = useMutation<any, Error, { file: File }>({
    mutationFn: createApiMutationFn<any, { file: File }>(({ file }) => ({
      method: 'POST',
      route: '/sim_storages/imports',
      data: file,
      headersConfig: { 'Content-Type': 'multipart/form-data' },
    })),
    onSuccess: res => {
      invalidateSimList();
      showToast('success', res?.message || 'Nhập sim thành công');
    },
  });

  const deleteSim = useMutation<any, Error, { id: string }>({
    mutationFn: createApiMutationFn<any, { id: string }>(({ id }) => ({
      method: 'DELETE',
      route: `/sim_storages/${id}`,
    })),
    onSuccess: res => {
      invalidateSimList();
      showToast('success', res?.message || 'Xóa sim thành công');
    },
  });

  return {
    createSim,
    deleteSim,
    updateSim,
    importsSim,
  };
};
