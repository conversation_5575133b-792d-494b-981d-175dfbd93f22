import { useCallback, useState } from 'react';

import { MODAL_TYPES } from '../constants';
import { AppState } from '../types';

const initialState: AppState = {
  simData: [],
  selectedSim: null,
  searchTerm: '',
  modals: {
    [MODAL_TYPES.ADD_SIM]: false,
    [MODAL_TYPES.CONFIRM_REMOVE]: false,
  },
  modalProps: {},
};

type ModalType = keyof typeof MODAL_TYPES;

export const useSimState = () => {
  const searchParams = new URLSearchParams(location.search);

  const [state, setState] = useState<AppState>(initialState);

  const updateState = useCallback(
    (updates: Partial<AppState>) => {
      setState(prev => ({ ...prev, ...updates }));
    },
    [state],
  );

  const openModal = useCallback(
    (modalType: ModalType | string, modalProps?: any) => {
      setState(prev => ({
        ...prev,
        modals: { ...prev.modals, [modalType]: true },
        modalProps: {
          ...prev.modalProps,
          [modalType]: modalProps,
        },
      }));
    },
    [],
  );

  const closeModal = useCallback((modalType: string) => {
    setState(prev => ({
      ...prev,
      modals: { ...prev.modals, [modalType]: false },
      modalProps: {
        ...prev.modalProps,
        [modalType]: undefined,
      },
    }));
  }, []);

  const setSearchTerm = useCallback(
    (term: string) => {
      updateState({ searchTerm: term });
    },
    [updateState],
  );

  const onSimSelect = useCallback(
    (sim, updateUrl = false) => {
      // unselect device
      if (sim?.sim_number === state.selectedSim?.sim_number) {
        updateState({ selectedSim: null });
        if (updateUrl) {
          searchParams.delete('imei');
          const url = new URL(window.location.href);
          window.history.pushState(
            {},
            '',
            `${url.origin}${url.pathname}?${searchParams.toString()}`,
          );
        }
        return;
      }
      // select device
      updateState({ selectedSim: sim });
      if (sim?.sim_number && updateUrl) {
        searchParams.set('imei', sim.sim_number);
        const url = new URL(window.location.href);
        window.history.pushState(
          {},
          '',
          `${url.origin}${url.pathname}?${searchParams.toString()}`,
        );
      }
    },
    [state, searchParams],
  );

  const onResetSimSelected = useCallback(() => {
    updateState({ selectedSim: null });
  }, [updateState]);

  return {
    state,
    updateState,
    openModal,
    closeModal,
    setSearchTerm,
    onSimSelect,
    onResetSimSelected,
  };
};
