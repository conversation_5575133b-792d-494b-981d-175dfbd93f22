import { useQuery } from '@tanstack/react-query';

import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

interface SimFilter {
  search_keyword?: string;
  created_at_from?: string;
  created_at_to?: string;
  expired_at_from?: string;
  expired_at_to?: string;
  is_activated?: string;
  network_operator?: string;
  service_package_id?: string;
  page?: number;
  per_page?: number;
}

export const useSimData = (props: SimFilter) => {
  const { page = 1, per_page = 20, ...rest } = props;

  const simList = useQuery({
    queryKey: queryKeys.sims.list({ page, per_page }),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/sim_storages',
        query: {
          page: page.toString(),
          per_page: per_page.toString(),
          ...rest,
        },
      }),
    }),
    staleTime: 30000,
    select: (data: any) => {
      return {
        data: data.sim_storages || [],
        pagination: data.pagination || {},
      };
    },
  });

  return simList;
};

export const useSimDetail = (id?: string | number) => {
  return useQuery({
    queryKey: queryKeys.sims.detail(id?.toString() || ''),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/sim_storages/${id}`,
    }),
    enabled: !!id,
    staleTime: 0,
    select: (data: any) => {
      return data.sim_storage || {};
    },
  });
};

export const useSimServicePackages = () => {
  return useQuery({
    queryKey: queryKeys.sims.servicePackages(),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: '/service_packages/sim',
    }),
    staleTime: 0,
    select: (data: any) => {
      return data.service_packages || [];
    },
  });
};
