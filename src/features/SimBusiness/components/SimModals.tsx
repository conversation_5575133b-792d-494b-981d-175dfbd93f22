import { useCallback } from 'react';

import type { SimType } from 'types/sim';

import { RRConfirmationModal } from 'components';

import AddSimModal from '../components/AddSimModal';
import { MODAL_TYPES } from '../constants';
import { useSimMutations } from '../hooks/useSimMutation';

interface Props {
  selectedSim: SimType | null;
  modals: Record<string, boolean>;
  closeModal: (modalType: string) => void;
  onResetSimSelected: () => void;
}

const SimModals = (props: Props) => {
  const { selectedSim, modals, closeModal, onResetSimSelected } = props;
  const { deleteSim } = useSimMutations();

  const handleDeleteAccount = useCallback(() => {
    const currentId = selectedSim?.id;
    if (currentId) {
      deleteSim.mutate(
        {
          id: currentId?.toString(),
        },
        {
          onSuccess: () => {
            closeModal(MODAL_TYPES.CONFIRM_REMOVE);
            onResetSimSelected();
          },
        },
      );
    }
  }, [deleteSim, selectedSim, closeModal, onResetSimSelected]);

  return (
    <>
      <AddSimModal
        visible={modals[MODAL_TYPES.ADD_SIM]}
        onClose={() => closeModal(MODAL_TYPES.ADD_SIM)}
      />

      <RRConfirmationModal
        title='Xoá Sim?'
        message='Bạn sẽ phải tạo lại từ đầu sau khi xoá'
        visible={modals.confirmRemove}
        isSubmitting={deleteSim.isPending}
        onCancel={() => closeModal(MODAL_TYPES.CONFIRM_REMOVE)}
        onConfirm={handleDeleteAccount}
      />
    </>
  );
};

export default SimModals;
