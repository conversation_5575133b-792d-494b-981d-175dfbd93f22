import { useEffect, useMemo, useRef, useState } from 'react';

import { Button } from 'antd';
import { Dayjs } from 'dayjs';
import useDebounce from 'hooks/useDebounce';
import { twMerge } from 'tailwind-merge';

import images from 'assets/images';
import { Icon } from 'components';
import { LinkButton } from 'components/Button';
import EmptyPanel from 'components/EmptyPanel';
import { RRInput } from 'components/FormField';

import { MODAL_TYPES } from '../constants';
import { useSimData, useSimServicePackages } from '../hooks/useSimData';
import type { FilterState } from '../types';
import AdvancedFilter from './AdvancedFilter';
import SimList from './SimList';

interface SimListPanelProps {
  enableAddSim: boolean;
  enableAdvanceFilter: boolean;
  className?: string;
  selectedSim: any;
  openModal: (modalType: string, modalProps?: any) => void;
  onSimSelect: (device: any) => void;
}

const DEFAULT_FILTER_STATE = {
  servicePackage: [],
  typeOfSim: [],
  inventory: null,
  activeDateRange: { start: undefined, end: undefined },
  expiredDateRange: { start: undefined, end: undefined },
  isActive: false,
  lastedUpdated: null,
};

const getDateString = (date?: Dayjs) => {
  if (!date || !date.isValid()) return '';
  return date.format('YYYY-MM-DD');
};

const SimListPanel = ({
  enableAddSim,
  enableAdvanceFilter,
  className = '',
  selectedSim,
  onSimSelect,
  openModal,
}: SimListPanelProps) => {
  const [searchText, setSearchText] = useState('');
  const [advancedFilters, setAdvancedFilters] =
    useState<FilterState>(DEFAULT_FILTER_STATE);
  const [simList, setSimList] = useState<any>([]);
  const [page, setPage] = useState(1);

  const advancedFilterRef = useRef<any>(null);
  const selectedSimRef = useRef(selectedSim);

  useEffect(() => {
    selectedSimRef.current = selectedSim;
  }, [selectedSim]);

  const simQueryParams = useMemo(
    () => ({
      page: page,
      per_page: 20,
      search_keyword: searchText,
      is_activated: advancedFilters.inventory?.toString(),
      service_package_id: advancedFilters.servicePackage.join(','),
      network_operator: advancedFilters.typeOfSim?.join(','),
      created_at_from: getDateString(advancedFilters.activeDateRange.start),
    }),
    [page, searchText, advancedFilters, advancedFilters?.lastedUpdated],
  );

  // Todo: inventory, packageFee filter ??? keyword format filter
  const { data, isLoading, status, refetch } = useSimData(simQueryParams);

  const { data: servicePackages } = useSimServicePackages();

  const ServicePackageOptions = useMemo(() => {
    return servicePackages?.map(item => ({
      value: item.id,
      label: item.name,
    }));
  }, [servicePackages]);

  const debouncedSearch = useDebounce(() => {
    setPage(1);
    refetch();
  }, 300);

  const handleResetFilter = () => {
    setSearchText('');
    advancedFilterRef.current?.reset();
    setAdvancedFilters({
      ...DEFAULT_FILTER_STATE,
      lastedUpdated: Date.now(),
    });
  };

  const handleFetchMore = () => {
    if (data?.pagination?.total_pages > page) {
      setPage(page + 1);
    }
  };

  useEffect(() => {
    if (searchText || advancedFilters?.lastedUpdated) {
      debouncedSearch();
    }
  }, [advancedFilters?.lastedUpdated, searchText]);

  useEffect(() => {
    if (page === 1) {
      setSimList(data?.data || []);
    } else {
      setSimList(prev => [...prev, ...(data?.data || [])]);
    }
  }, [data, page]);

  // Auto select first sim when simList changes
  const autoSelectTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (autoSelectTimeoutRef.current) {
      clearTimeout(autoSelectTimeoutRef.current);
    }

    if (!simList.length) {
      if (selectedSimRef.current) {
        onSimSelect(null);
      }
      return;
    }

    if (selectedSimRef.current) return;

    autoSelectTimeoutRef.current = setTimeout(() => {
      const firstSim = simList[0];
      if (firstSim && firstSim.id !== selectedSimRef.current?.id) {
        onSimSelect(firstSim);
      }
    }, 100);

    return () => {
      if (autoSelectTimeoutRef.current) {
        clearTimeout(autoSelectTimeoutRef.current);
      }
    };
  }, [simList]);

  return (
    <div
      className={twMerge(
        'flex flex-col bg-white-1000 border-grey-100 h-full rounded-md w-[400px] gap-3 px-6',
        className,
      )}
    >
      <div className='flex w-full flex-col items-center gap-3'>
        <div className='flex w-full flex-1 items-center'>
          <RRInput
            type='text'
            value={searchText}
            prefixIcon={images.Icon.SearchLoupe}
            containerClassName='w-full flex-1'
            placeholder='CCID / SIM / IMEI'
            showClearButton={true}
            onChange={e => setSearchText(e.target.value)}
            onClear={() => setSearchText('')}
          />
          {advancedFilters?.isActive && (
            <Button
              className='ml-3 box-border flex h-10 w-24 flex-row items-center justify-center gap-1 rounded-lg border-[1.5px] border-solid border-grey-100 px-5 font-medium leading-[24px] text-black-1000 hover:border-grey-400 hover:text-grey-600'
              onClick={handleResetFilter}
            >
              <Icon src={images.Icon.CancelFilter} />
              Hủy lọc
            </Button>
          )}
        </div>
        {enableAdvanceFilter && (
          <AdvancedFilter
            ref={advancedFilterRef}
            onFilterSelect={setAdvancedFilters}
            ServicePackageOptions={ServicePackageOptions}
          />
        )}
      </div>
      {enableAddSim && (
        <div className='flex items-center justify-start'>
          <LinkButton
            onClick={() => openModal(MODAL_TYPES.ADD_SIM)}
            size='small'
            iconPosition='left'
            className='h-6'
            icon={<Icon src={images.Icon.PlusBlue} />}
          >
            Nhập Sim
          </LinkButton>
        </div>
      )}
      {simList?.length === 0 && status === 'success' && (
        <div className='flex h-[300px] items-center justify-center'>
          <EmptyPanel />
        </div>
      )}
      {simList?.length > 0 && (
        <div className='h-full flex-1'>
          <SimList
            loading={isLoading}
            data={simList}
            selectedSim={selectedSim}
            onSelectedSim={onSimSelect}
            fetchMore={handleFetchMore}
          />
        </div>
      )}
    </div>
  );
};

export default SimListPanel;
