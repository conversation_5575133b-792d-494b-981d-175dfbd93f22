import dayjs, { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import { SimType } from 'types/sim';

import images from 'assets/images';
import { BodyMdBase, BodyMdExtend, FontBold } from 'assets/styles';
import { Icon } from 'components';

interface Props {
  className?: string;
  data: SimType;
  checked: boolean;
  onSelect: (item: SimType) => void;
}

const getDateString = (date?: Dayjs) => {
  if (!date || !date.isValid()) return '';
  return date.format('DD/MM/YYYY');
};

const SimCard = ({ className, data, checked, onSelect }: Props) => {
  const { t } = useTranslation();

  const handleSelect = () => {
    onSelect(data);
  };

  return (
    <StyledSimCard
      onClick={handleSelect}
      isSelected={checked}
      className={twMerge(className)}
    >
      <div className='flex justify-between'>
        <div className='capitalize text-blue-50'>{data.network_operator}</div>
        {data.is_verify === 1 && (
          <Icon width={24} height={24} src={images.Icon.Verify}></Icon>
        )}
      </div>
      <div className='flex items-center gap-3'>
        <div className={`${FontBold} ${BodyMdExtend}`}>{data.sim_number}</div>
        {data?.device_imei && (
          <div
            className={`${BodyMdBase} rounded-md border border-grey-100 px-1.5 py-0.5`}
          >
            {data.device_imei}
          </div>
        )}
      </div>
      <div className='flex items-center gap-1 text-grey-600'>
        <Icon src={images.Icon.CalendarMinus}></Icon>
        <span className='text-[14px]'>{t('sim.expirationDate')}:</span>
        <div className='flex items-center gap-1 text-[14px] text-black-1000'>
          <span>{getDateString(dayjs(data.active_date))}</span>
          <span>{`->`}</span>
          <span>{getDateString(dayjs(data.expire_date))}</span>
        </div>
      </div>
    </StyledSimCard>
  );
};

export default SimCard;

const StyledSimCard = tw.div<{ isSelected: boolean }>`
  flex cursor-pointer flex-col gap-2 border px-3 py-2 hover:rounded-xl hover:bg-grey-50 mb-2
  ${({ isSelected }) =>
    isSelected
      ? 'border-blue-50 rounded-xl border'
      : 'border-white-1000 border-b-grey-100'}
`;
