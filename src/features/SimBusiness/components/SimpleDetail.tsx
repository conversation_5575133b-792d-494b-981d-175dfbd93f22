import React, { useEffect } from 'react';

import { Button } from 'antd';
import dayjs from 'dayjs';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { SimType } from 'types/sim';

import images from 'assets/images';
import { FontBold, TitleMd } from 'assets/styles';
import { PrimaryButton } from 'components/Button';
import EmptyChoice from 'components/EmptyChoice';
import RRFieldDatePicker from 'components/FormField/RRFieldDatePicker';
import RRFieldInput from 'components/FormField/RRFieldInput';
import RRFieldSelect from 'components/FormField/RRFieldSelect';
import Icon from 'components/Icon';

import { MODAL_TYPES, SimNetworkOperatorOptions } from '../constants';
import { useSimDetail } from '../hooks/useSimData';
import { useSimMutations } from '../hooks/useSimMutation';

interface Props {
  selectedSim: SimType | null;
  openModal: (modalType: string, modalProps?: any) => void;
}

const SimDetail: React.FC<Props> = ({ selectedSim, openModal }) => {
  const { t } = useTranslation();

  const { data } = useSimDetail(selectedSim?.id || '');

  const { updateSim } = useSimMutations();

  const methods = useForm({
    defaultValues: {},
  });

  const { handleSubmit, control, formState } = methods;
  const { errors } = formState;

  const handleOpenDeleteModal = () => {
    openModal(MODAL_TYPES.CONFIRM_REMOVE);
  };

  useEffect(() => {
    if (data) {
      methods.reset(data);
    }
  }, [data]);

  const onSubmit = (values: any) => {
    if (!selectedSim || !selectedSim.id) return;
    updateSim.mutate({
      id: selectedSim.id.toString(),
      simInfo: {
        // how to get local time
        expire_date: dayjs(values.expire_date).format('YYYY-MM-DD HH:mm:ss'),
      },
    });
  };

  if (!selectedSim) {
    return <EmptyChoice content='Chọn Sim để xem thông tin' />;
  }

  return (
    <Wrapper>
      <Header>
        <Title className={`${TitleMd} ${FontBold}`}>{t('sim.details')}</Title>
        <div className='flex gap-3'>
          {data?.device_imei && (
            <div
              className={`rounded-md border border-grey-100 px-3 py-1 text-[14px]`}
            >
              {data?.device_imei}
            </div>
          )}
          {data?.is_verify !== 1 && (
            <Button
              shape='circle'
              icon={<Icon src={images.Icon.Trash} />}
              onClick={handleOpenDeleteModal}
              className='border-none bg-red-10 shadow-none hover:bg-red-alpha20'
            />
          )}
        </div>
      </Header>
      <FormProvider {...methods}>
        <FormContainer onSubmit={handleSubmit(onSubmit)}>
          <GridFormWrapper>
            <Grid className='mt-3 grid-cols-2 gap-3'>
              <RRFieldInput
                id='ccid'
                control={control}
                label={t('sim.ccid')}
                placeholder={t('sim.enterccid')}
                errors={errors}
                disabled
              />
              <RRFieldInput
                id='sim_number'
                control={control}
                label={t('sim.phoneNumber')}
                placeholder={t('sim.enterPhoneNumber')}
                errors={errors}
                disabled
              />
              <RRFieldSelect
                id='network_operator'
                options={SimNetworkOperatorOptions}
                control={control}
                label={t('sim.provider')}
                placeholder={t('sim.enterProvider')}
                errors={errors}
                disabled
              />
              <RRFieldInput
                id='service_package_name'
                control={control}
                label={t('sim.plan')}
                placeholder={t('sim.enterPlan')}
                errors={errors}
                disabled
              />
              <RRFieldDatePicker
                id='active_date'
                control={control}
                label={t('sim.activationDate')}
                placeholder={t('sim.activationDate')}
                errors={errors}
                suffixIcon={images.Icon.CalendarSchedule}
                disabled
              />
              <RRFieldDatePicker
                id='expire_date'
                control={control}
                label={t('sim.exprationDate')}
                errors={errors}
                placeholder={t('sim.activationDate')}
                suffixIcon={images.Icon.CalendarSchedule}
              />
            </Grid>
            <div className='mt-6'>
              <PrimaryButton
                htmlType='submit'
                className='w-full'
                disabled={
                  updateSim.isPending ||
                  !formState.isDirty ||
                  !formState.isValid
                }
                loading={updateSim.isPending}
              >
                {t('deviceDetail.saveInfo')}
              </PrimaryButton>
            </div>
          </GridFormWrapper>
        </FormContainer>
      </FormProvider>
    </Wrapper>
  );
};

export default SimDetail;

const FormContainer = tw.form``;
const Wrapper = tw.div`border border-grey-100 rounded-2xl p-6`;
const Grid = tw.div`grid gap-3`;
const Header = tw.div`flex justify-between relative`;
const Title = tw.div``;
const GridFormWrapper = tw.div`h-full rounded-2xl`;
