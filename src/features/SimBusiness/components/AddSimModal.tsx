import React, { useCallback, useEffect } from 'react';

import { Modal, Upload, UploadFile, UploadProps } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useProfile } from 'hooks/useProfile';
import { yupResolver } from 'hooks/useYupResolver';
import { FormProvider, useForm } from 'react-hook-form';
import tw from 'tailwind-styled-components';
import * as XLSX from 'xlsx';

import { t } from 'i18next';

import images from 'assets/images';
import {
  Icon,
  RRFieldDatePicker,
  RRFieldInput,
  RRFieldSelect,
} from 'components';
import { LinkButton, PrimaryButton, SecondaryButton } from 'components/Button';
import showToast from 'components/RRToastMessage/Toast';

import { SimNetworkOperatorOptions } from '../constants';
import { useSimMutations } from '../hooks';
import { createSimSchema } from '../schema/simSchema';
import type { CreateSimType } from '../types';

interface AddSimModalProps {
  visible: boolean;
  onClose?: () => void;
}

interface ExcelData {
  simno: string;
  ccid: string;
}

const DefaultValue = {
  ccid: '',
  sim_number: '',
  network_operator: '',
  active_date: '',
  expire_date: '',
  service_package_name: '',
};

const getDateString = (date?: Dayjs) => {
  if (!date || !date.isValid()) return '';
  return date.format('YYYY-MM-DD HH:mm:ss');
};

const HeaderSim = ({ profile }) => {
  const fullName = profile?.username || profile?.full_name || profile?.name;
  if (profile.is_distributor && !profile.parent_info) {
    return (
      <Row>
        <AgencyAccountContainer>
          <Icon src={images.Icon.Level0Star} />
          <AgencyAccountLabel>{fullName}</AgencyAccountLabel>
        </AgencyAccountContainer>
      </Row>
    );
  }

  if (profile.is_distributor && profile.parent_info) {
    return (
      <Row>
        <AgencyAccountContainer>
          <Icon src={images.Icon.Level1Star} />
          <AgencyAccountLabel>{fullName}</AgencyAccountLabel>
        </AgencyAccountContainer>
      </Row>
    );
  }

  return (
    <Row>
      <AgencyAccountContainer>
        <Icon src={images.Icon.Level2Star} />
        <AgencyAccountLabel>{fullName}</AgencyAccountLabel>
      </AgencyAccountContainer>
    </Row>
  );
};

const AddDeviceModal: React.FC<AddSimModalProps> = ({ visible, onClose }) => {
  const { profile } = useProfile();
  const { createSim } = useSimMutations();

  const [currentFile, setCurrentFile] = React.useState<UploadFile>();
  const [excelData, setExcelData] = React.useState<ExcelData[]>([]);

  const methods = useForm({
    defaultValues: DefaultValue,
    mode: 'onBlur',
    resolver: yupResolver(createSimSchema),
  });

  const { handleSubmit, formState, control, setValue } = methods;
  const { errors } = formState;

  const handleRemoveFile = () => {
    setCurrentFile(undefined);
    setExcelData([]);
  };

  const handleChangeFile: UploadProps['onChange'] = useCallback(
    ({ fileList: newFileList }) => {
      if (!newFileList.length) return;

      const file = newFileList[0];
      if (file.size > 25 * 1024 * 1024) {
        showToast('failed', 'File upload phải nhỏ hơn 25MB');
        return;
      }
      if (file.name.split('.').pop() !== 'xlsx') {
        showToast('failed', 'File upload phải là file xlsx');
        return;
      }

      setCurrentFile(file);

      const reader = new FileReader();

      reader.onload = event => {
        const data = event.target?.result;
        if (data) {
          const workbook = XLSX.read(new Uint8Array(data as ArrayBuffer), {
            type: 'array',
          });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          const dataRows = jsonData.slice(1);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const formattedData = dataRows.map(row => {
            return {
              simno: row?.[0],
              ccid: row?.[1],
            };
          });
          const uniqueData = formattedData.filter(
            (item, index, self) =>
              item.ccid && index === self.findIndex(t => t.ccid === item.ccid),
          );
          setExcelData(uniqueData);
          // update imei string
          const imeiArray = uniqueData
            .map(item => item.ccid)
            .filter(
              (item, index, self) =>
                item && index === self.findIndex(t => t === item),
            );
          const simnoArray = uniqueData
            .map(item => item.simno)
            .filter(
              (item, index, self) =>
                item && index === self.findIndex(t => t === item),
            );

          setValue('ccid', imeiArray.join(';'), {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
          });
          setValue('sim_number', simnoArray.join(';'), {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
          });
        }
      };
      if (file.originFileObj) {
        reader.readAsArrayBuffer(file.originFileObj);
      }
    },
    [],
  );

  const onSubmit = data => {
    const formattedData = {
      ...data,
      active_date: getDateString(dayjs(data.active_date)),
      expire_date: getDateString(dayjs(data.expire_date)),
    };

    const listCcid = formattedData.ccid
      .split(';')
      .filter((item: string) => item.trim() !== '');
    const listSimno = formattedData.sim_number
      .split(';')
      .filter((item: string) => item.trim() !== '');
    const Sims = [] as CreateSimType[];
    for (let i = 0; i < listCcid.length; i++) {
      const sim = excelData.find(item => item.ccid === listCcid[i]);
      if (sim) {
        Sims.push({
          ...formattedData,
          sim_number: sim.simno,
          ccid: sim.ccid,
        });
        continue;
      }
      Sims.push({
        ...formattedData,
        sim_number: listSimno[i],
        ccid: listCcid[i],
      });
    }

    createSim.mutate(Sims, {
      onSuccess: () => {
        onClose?.();
      },
    });
  };

  const handleDownloadTemplate = () => {
    const a = document.createElement('a');
    a.href = '/src/assets/files/IMPORT_SIM_TEMPLATE.xlsx';
    a.download = 'IMPORT_SIM_TEMPLATE.xlsx';
    a.click();
  };

  useEffect(() => {
    if (!visible) {
      setCurrentFile(undefined);
      methods.reset(DefaultValue);
    }
    return () => {
      setCurrentFile(undefined);
      methods.reset(DefaultValue);
    };
  }, [visible]);

  return (
    <Modal
      centered
      title={<Title>Nhập Sim</Title>}
      open={visible}
      width={720}
      onCancel={onClose}
      onOk={onClose}
      footer={null}
      closeIcon={
        <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
      }
    >
      <FormProvider {...methods}>
        <FormContainer onSubmit={handleSubmit(onSubmit)}>
          <GridFormWrapper>
            <HeaderSim profile={profile} />
            <Grid className='mt-3 grid-cols-2 gap-3'>
              <RRFieldInput
                id='ccid'
                control={control}
                label={t('sim.ccid')}
                placeholder={t('sim.enterccid')}
                errors={errors}
                disabled={!!currentFile}
              />
              <RRFieldInput
                id='sim_number'
                control={control}
                label={t('sim.phoneNumber')}
                placeholder={t('sim.enterPhoneNumber')}
                errors={errors}
                disabled={!!currentFile}
              />
              <RRFieldSelect
                id='network_operator'
                options={SimNetworkOperatorOptions}
                control={control}
                label={t('sim.provider')}
                placeholder={t('sim.enterProvider')}
                errors={errors}
              />
              <RRFieldInput
                id='service_package_name'
                control={control}
                label={t('sim.plan')}
                placeholder={t('sim.enterPlan')}
                errors={errors}
              />
              <RRFieldDatePicker
                id='active_date'
                control={control}
                label={t('sim.activationDate')}
                placeholder={t('sim.activationDate')}
                errors={errors}
                suffixIcon={images.Icon.CalendarSchedule}
              />
              <RRFieldDatePicker
                id='expire_date'
                control={control}
                label={t('sim.exprationDate')}
                errors={errors}
                placeholder={t('sim.activationDate')}
                suffixIcon={images.Icon.CalendarSchedule}
              />
              <UploadContainer>
                <SecondaryButton
                  size='small'
                  iconPosition='left'
                  icon={<Icon src={images.Icon.Excel} />}
                  onClick={handleDownloadTemplate}
                >
                  Biểu mẫu
                </SecondaryButton>
              </UploadContainer>
              <div className='flex items-center justify-end'>
                <Upload
                  name='file'
                  beforeUpload={() => false}
                  fileList={[]}
                  onChange={handleChangeFile}
                  showUploadList={false}
                  multiple={false}
                  maxCount={1}
                >
                  <LinkButton
                    size='small'
                    iconPosition='left'
                    className='h-6'
                    icon={<Icon src={images.Icon.PaperClip} />}
                  >
                    {t('business.attach')}
                  </LinkButton>
                </Upload>
              </div>
              <div className='col-span-2'>
                {currentFile && (
                  <Row className='items-start justify-start'>
                    <AttachmentLabel>{`Đính kèm (1)`}</AttachmentLabel>
                    <UploadContainer>
                      <ThumbnailFile key={currentFile.uid}>
                        <Icon src={images.Icon.Excel} />
                        <span className='truncate-1-line'>
                          {currentFile.name}
                        </span>
                        <Icon
                          className='cursor-pointer'
                          src={images.Icon.XClose}
                          onClick={handleRemoveFile}
                        />
                      </ThumbnailFile>
                    </UploadContainer>
                  </Row>
                )}
              </div>
            </Grid>
            <PrimaryButton
              htmlType='submit'
              className='mt-3 w-full'
              disabled={createSim.isPending || !formState.isValid}
              loading={createSim.isPending}
            >
              {t('business.confirm')}
            </PrimaryButton>
          </GridFormWrapper>
        </FormContainer>
      </FormProvider>
    </Modal>
  );
};
const Title = tw.span`text-xl leading-[28px]`;
const Row = tw.div`flex flex-col gap-1`;
const FormContainer = tw.form``;
const GridFormWrapper = tw.div`h-full rounded-2xl`;
const Grid = tw.div`grid gap-3`;
const AgencyAccountContainer = tw.div`w-full rounded-xl border-grey-100 border-[1px] border-solid box-border flex flex-row items-center justify-start py-2 px-3 gap-2 text-left text-sm text-text-secondary font-medium`;
const AgencyAccountLabel = tw.div`self-stretch relative leading-[24px] font-medium`;
const UploadContainer = tw.div`flex flex-1 gap-2 flex-wrap w-full`;
const ThumbnailFile = tw.div`flex flex-row items-center justify-center gap-1 truncate rounded-lg border border-grey-100 px-2 text-sm leading-[24px] text-grey-600 px-2 py-1 hover:bg-grey-50`;
const AttachmentLabel = tw.div`flex flex-row items-center justify-center gap-1 truncate px-2 text-sm leading-[24px] text-grey-600`;

export default AddDeviceModal;
