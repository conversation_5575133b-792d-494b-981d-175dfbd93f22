// Report feature type definitions
import { Dayjs } from 'dayjs';

export interface ReportFilters {
  deviceImei?: string;
  startDate?: Dayjs;
  endDate?: Dayjs;
  reportType?: string;
  userId?: string;
}

export interface DeviceReportData {
  id: string;
  deviceName: string;
  startTime: string;
  endTime: string;
  distance: number;
  engineOnTime: number;
  engineOffTime: number;
  lostConnectionCount: number;
  lostConnectionTime: number;
}

// Extended interface for table display with computed fields

export interface ReportResponse {
  items: any[];
  page: number;
  total_count: number;
  total_pages: number;
}

export interface DeviceOption {
  value: string;
  label: string;
  imei: string;
}

export interface ExportResponse {
  downloadUrl: string;
  filename: string;
}

export interface DateRangeValidation {
  isValid: boolean;
  error: string | null;
}

export interface ReportTypeOption {
  value: string;
  label: string;
}
