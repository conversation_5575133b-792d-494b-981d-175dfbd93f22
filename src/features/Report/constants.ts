import type { ColumnsType } from 'antd/es/table';

export const columns: ColumnsType<Record<string, any>> = [
  {
    title: 'STT',
    dataIndex: 'stt',
    key: 'stt',
    width: 40,
    align: 'center',
    render: (_: any, __: any, index: number) => index + 1,
  },
  {
    title: 'Thiết bị',
    dataIndex: 'deviceName',
    key: 'deviceName',
    align: 'center',
  },
  {
    title: 'Thời gian bắt đầu',
    dataIndex: 'startTimeFormatted',
    key: 'startTimeFormatted',
    width: 200,
    align: 'center',
  },
  {
    title: 'Thời gian kết thúc',
    dataIndex: 'endTimeFormatted',
    key: 'endTimeFormatted',
    width: 200,
    align: 'center',
  },
  {
    title: 'Quãng đường (km)',
    dataIndex: 'distanceKm',
    width: 200,
    key: 'distanceKm',
    align: 'center',
  },
  {
    title: 'Thời gian',
    children: [
      {
        title: '<PERSON><PERSON> máy (h)',
        dataIndex: 'engineOnHours',
        key: 'engineOnHours',
        align: 'center',
      },
      {
        title: 'Dừng máy (h)',
        dataIndex: 'engineOffHours',
        key: 'engineOffHours',
        align: 'center',
      },
    ],
  },
  {
    title: 'Mất kết nối',
    children: [
      {
        title: 'Số lần',
        dataIndex: 'lostConnectionCount',
        key: 'lostConnectionCount',
        align: 'center',
      },
      {
        title: 'Thời gian (h)',
        dataIndex: 'lostConnectionHours',
        key: 'lostConnectionHours',
        align: 'center',
      },
    ],
  },
  {
    title: 'Số lần truyền bù',
    dataIndex: '',
    key: '',
    width: 200,
    align: 'center',
  },
  {
    title: 'Tốc độ (km/h)',
    children: [
      {
        title: 'Cao nhất',
        dataIndex: 'maxSpeed',
        key: 'maxSpeed',
        align: 'center',
      },
      {
        title: 'Trung bình',
        dataIndex: 'averageSpeed',
        key: 'averageSpeed',
        align: 'center',
      },
    ],
  },
  {
    title: 'Nhiên liệu',
    children: [
      {
        title: 'Cảm biến',
        dataIndex: '',
        key: '',
        align: 'center',
      },
      {
        title: 'Tiêu thụ (lít)',
        dataIndex: '',
        key: '',
        align: 'center',
      },
      {
        title: 'Nạp (lít)',
        dataIndex: '',
        key: '',
        align: 'center',
      },
      {
        title: 'Xả (lít)',
        dataIndex: '',
        key: '',
        align: 'center',
      },
    ],
  },
];

export const reportTypes = [
  { value: 'summary', label: 'Báo cáo tổng hợp' },
  { value: 'daily_summary', label: 'Tổng hợp theo ngày' },
  { value: 'history_data', label: 'Dữ liệu lịch sử' },
  { value: 'route', label: 'Lộ trình' },
  { value: 'route_short', label: 'Lộ trình rút gọn' },
  { value: 'route_advanced', label: 'Lộ trình nâng cao' },
  { value: 'driving_time_daily', label: 'Thời gian lái xe theo ngày' },
  { value: 'driving_time_weekly', label: 'Thời gian lái xe theo tuần' },
  { value: 'status_history', label: 'Lịch sử trạng thái' },
  { value: 'toll_station', label: 'Trạm thu phí' },
  { value: 'engine_on_off', label: 'Dừng máy nổ' },
  { value: 'engine_on_time', label: 'Thời gian nổ máy' },
  { value: 'engine_on_time_detail', label: 'Thời gian nổ máy chi tiết' },
  { value: 'stop_points', label: 'Điểm dừng' },
  { value: 'door_status_detail', label: 'Chi tiết đóng/mở cửa' },
  { value: 'lost_connection_detail', label: 'Chi tiết mất kết nối' },
  { value: 'refueling_detail', label: 'Chi tiết bù trừ' },
];
