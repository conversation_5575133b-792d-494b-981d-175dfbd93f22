import { useMutation, useQuery } from '@tanstack/react-query';
import dayjs, { Dayjs } from 'dayjs';

import { t } from 'i18next';
import { qsStringifyUrl } from 'services/api/utils';
import { createApiMutationFn, createApiQueryFn } from 'services/reactQuery';
import { convertMeterToKm } from 'utils';
import { uppercaseString } from 'utils';
import { formatDateStr } from 'utils/date';

import showToast from 'components/RRToastMessage/Toast';

// Import types and re-export for convenience
import type {
  DateRangeValidation,
  ExportResponse,
  ReportFilters,
  ReportResponse,
} from '../types';

// Re-export types
export type {
  ReportFilters,
  DeviceReportData,
  ReportResponse,
  DeviceOption,
  ExportResponse,
  DateRangeValidation,
  ReportTypeOption,
} from '../types';

/**
 * Hook to fetch device reports with filters
 */
export const useDeviceReports = (
  filters: ReportFilters,
  page: number = 1,
  pageSize: number = 10,
  enabled: boolean = true,
) => {
  const queryParams: Record<string, string> = {
    page: page.toString(),
    per_page: pageSize.toString(),
  };

  return useQuery({
    queryKey: ['reports', 'summary'],
    queryFn: createApiQueryFn<ReportResponse>({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/devices/summary',
        query: {
          ...queryParams,
          imei: filters.deviceImei || '',
          report_type: filters.reportType || '',
          ...(filters.startDate && {
            start_date: filters.startDate.format('YYYY-MM-DD'),
          }),
          ...(filters.endDate && {
            end_date: filters.endDate.format('YYYY-MM-DD'),
          }),
        },
      }),
    }),
    enabled: enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes
    select: data => ({
      reports:
        (data?.items || [])?.map((report, index) => ({
          ...report,
          key: report.id || index.toString(),
          stt: (page - 1) * pageSize + index + 1,
          deviceName: uppercaseString(report.device_name),
          startTimeFormatted: formatDateStr(report.start_time),
          endTimeFormatted: formatDateStr(report.end_time),
          distanceKm: convertMeterToKm(report.total_distance),
          maxSpeed: report.max_speed,
          averageSpeed: report.average_speed,
        })) || [],
      pagination: {
        page: data.page,
        totalPages: data.total_pages,
        totalCount: data.total_count,
      },
    }),
  });
};

/**
 * Hook to export report to Excel
 */
export const useExportExcel = () => {
  return useMutation<ExportResponse, Error, { filters: ReportFilters }>({
    mutationFn: createApiMutationFn<ExportResponse, { filters: ReportFilters }>(
      ({ filters }) => ({
        method: 'POST',
        route: '/reports/export/excel',
        data: filters,
      }),
    ),
    onSuccess: response => {
      // Create download link
      const link = document.createElement('a');
      link.href = response.downloadUrl;
      link.download = response.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showToast(
        'success',
        t('report.exportExcelSuccess') || 'Export Excel thành công',
      );
    },
    onError: error => {
      console.error('Export Excel error:', error);
      showToast(
        'failed',
        t('report.exportExcelError') || 'Export Excel thất bại',
      );
    },
  });
};

/**
 * Hook to export report to PDF
 */
export const useExportPDF = () => {
  return useMutation<ExportResponse, Error, { filters: ReportFilters }>({
    mutationFn: createApiMutationFn<ExportResponse, { filters: ReportFilters }>(
      ({ filters }) => ({
        method: 'POST',
        route: '/reports/export/pdf',
        data: filters,
      }),
    ),
    onSuccess: response => {
      // Create download link
      const link = document.createElement('a');
      link.href = response.downloadUrl;
      link.download = response.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showToast(
        'success',
        t('report.exportPDFSuccess') || 'Export PDF thành công',
      );
    },
    onError: error => {
      console.error('Export PDF error:', error);
      showToast('failed', t('report.exportPDFError') || 'Export PDF thất bại');
    },
  });
};

/**
 * Hook to refresh report data
 */
export const useRefreshReports = () => {
  return useMutation<void, Error, { filters: ReportFilters }>({
    mutationFn: createApiMutationFn<void, { filters: ReportFilters }>(
      ({ filters }) => ({
        method: 'POST',
        route: '/reports/refresh',
        data: filters,
      }),
    ),
    onSuccess: () => {
      showToast(
        'success',
        t('report.refreshSuccess') || 'Làm mới dữ liệu thành công',
      );
    },
    onError: error => {
      console.error('Refresh reports error:', error);
      showToast(
        'failed',
        t('report.refreshError') || 'Làm mới dữ liệu thất bại',
      );
    },
  });
};

/**
 * Composite hook for report page
 */
export const useReportPage = () => {
  const exportExcel = useExportExcel();
  const exportPDF = useExportPDF();
  const refresh = useRefreshReports();

  return {
    exportExcel,
    exportPDF,
    refresh,
    isExporting: exportExcel.isPending || exportPDF.isPending,
    isRefreshing: refresh.isPending,
  };
};

/**
 * Hook for date range validation and helpers
 */
export const useDateRange = () => {
  const validateDateRange = (
    start?: Dayjs,
    end?: Dayjs,
  ): DateRangeValidation => {
    if (!start || !end) {
      return { isValid: false, error: 'Vui lòng chọn khoảng thời gian' };
    }
    const diffDays = end.diff(start, 'day');

    if (diffDays < 0) {
      return { isValid: false, error: 'Ngày kết thúc phải sau ngày bắt đầu' };
    }

    // if (diffDays > 31) {
    //   return {
    //     isValid: false,
    //     error: 'Khoảng thời gian không được quá 31 ngày',
    //   };
    // }

    return { isValid: true, error: null };
  };

  const getDefaultDateRange = () => {
    const endDate = dayjs();
    const startDate = dayjs().subtract(7, 'day');
    return { startDate, endDate };
  };

  return {
    validateDateRange,
    getDefaultDateRange,
  };
};
