import { useCallback, useMemo, useState } from 'react';

import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import { useDeviceSelectors } from 'features/DeviceBusiness/hooks/useDeviceData';
import { t } from 'i18next';
import { uppercaseString } from 'utils';

import images from 'assets/images';
import { BodyMdExtend, FontBold, FontSemibold, TitleMd } from 'assets/styles';
import { HeaderSection, Icon, RRCalendar } from 'components';
import { LinkButton, SecondaryButton } from 'components/Button';
import PrimaryButton from 'components/Button/PrimaryButton';
import { RRSelect } from 'components/FormField';
import RRTable from 'components/RRTable';
import showToast from 'components/RRToastMessage/Toast';

import { reportTypes as DEFAULT_REPORT_TYPES, columns } from './constants';
import {
  type ReportFilters,
  useDateRange,
  useDeviceReports,
  useReportPage,
} from './hooks';

interface ReportProps {}

const Report = ({}: ReportProps) => {
  const { profile } = useProfile();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const { getDefaultDateRange, validateDateRange } = useDateRange();

  // Initialize filters with default date range
  const [filters, setFilters] = useState<ReportFilters>(() => ({
    userId: profile?.id?.toString(),
    ...getDefaultDateRange(),
  }));

  // React Query hooks
  const { exportExcel, exportPDF, refresh, isRefreshing } = useReportPage();

  const { data: deviceSelect } = useDeviceSelectors();

  const {
    data: reportsData,
    isLoading: reportsLoading,
    refetch: refetchReports,
  } = useDeviceReports(filters, currentPage, pageSize);

  const deviceOptions = useMemo(() => {
    return (deviceSelect || []).map(item => ({
      label: uppercaseString(`${item.device_name} (${item.device_imei})`),
      value: item.device_imei,
    }));
  }, [deviceSelect]);

  const handleExportExcel = () => {
    exportExcel.mutate({ filters });
  };

  const handleExportPDF = () => {
    exportPDF.mutate({ filters });
  };

  const handleRefresh = () => {
    refresh.mutate(
      { filters },
      {
        onSuccess: () => {
          refetchReports();
        },
      },
    );
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const onPageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    refetchReports();
  };

  const handleFilterChange = useCallback(
    (key: keyof ReportFilters, value: any) => {
      setFilters(prev => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  const reportTypeLabel = useMemo(() => {
    return DEFAULT_REPORT_TYPES.find(type => type.value === filters.reportType)
      ?.label;
  }, [DEFAULT_REPORT_TYPES, filters.reportType]);

  return (
    <div className='flex h-full flex-col'>
      <div className='flex h-full flex-col gap-6 p-6'>
        <HeaderSection content='Báo cáo dữ liệu' />

        {/* Filters */}
        <div className='flex w-full items-center gap-4'>
          <div className='flex w-full items-center justify-between gap-4'>
            <RRSelect
              id='deviceImei'
              name='deviceImei'
              label='Thiết bị'
              options={deviceOptions}
              placeholder='Chọn thiết bị'
              className='h-[40px] w-full'
              onChange={value => handleFilterChange('deviceImei', value)}
              value={filters.deviceImei || ''}
            />
            <div className='flex w-full flex-col gap-1'>
              <div className={`${BodyMdExtend} ${FontSemibold}`}>Thời gian</div>
              <RRCalendar
                placeholder='Chọn khoảng thời gian'
                classNameInput='h-[40px]'
                minDateTime={undefined}
                startDate={filters.startDate}
                endDate={filters.endDate}
                maxDateTime={new Date()}
                onSelect={(type, date) => {
                  if (!type || !date) return;
                  if (type === 'start') {
                    const { isValid, error } = validateDateRange(
                      date,
                      filters.endDate,
                    );
                    if (!isValid) {
                      showToast('failed', error || '');
                      return;
                    }
                    handleFilterChange('startDate', date);
                  } else {
                    handleFilterChange('endDate', date);
                  }
                }}
              />
            </div>

            <RRSelect
              id='reportType'
              name='reportType'
              label='Loại báo cáo'
              options={DEFAULT_REPORT_TYPES}
              placeholder='Chọn loại báo cáo'
              className='h-[40px]'
              onChange={value => handleFilterChange('reportType', value)}
              value={filters.reportType}
            />
          </div>

          <PrimaryButton
            htmlType='submit'
            className='h-[40px] w-fit self-end justify-self-end whitespace-nowrap'
            onClick={handleSearch}
            disabled={reportsLoading}
            loading={reportsLoading}
          >
            {t('deviceLog.search')}
          </PrimaryButton>
        </div>

        {/* Results Table */}
        <div className='flex flex-1 flex-col'>
          <Flex className='mb-3 mt-6 justify-between'>
            <Div className={`${FontBold} ${TitleMd}`}>
              {reportTypeLabel || t('deviceLog.allData')} (
              {reportsData?.pagination.totalCount || 0})
            </Div>
            <Flex className='items-center gap-4'>
              <LinkButton
                size='small'
                iconPosition='left'
                className='h-6'
                icon={<Icon src={images.Icon.ArrowRotate} />}
                onClick={handleRefresh}
                loading={isRefreshing}
              >
                {t('deviceLog.refreshData')}
              </LinkButton>
              <SecondaryButton
                size='small'
                iconPosition='left'
                icon={<Icon src={images.Icon.FileExcel} />}
                onClick={handleExportExcel}
                loading={exportExcel.isPending}
                disabled={!reportsData?.reports.length}
              >
                {t('deviceLog.downloadExcel')}
              </SecondaryButton>
              <SecondaryButton
                size='small'
                iconPosition='left'
                icon={<Icon src={images.Icon.FilePdf} />}
                onClick={handleExportPDF}
                loading={exportPDF.isPending}
                disabled={!reportsData?.reports.length}
              >
                {t('deviceLog.downloadPDF')}
              </SecondaryButton>
            </Flex>
          </Flex>
          <RRTable
            headerRows={2}
            tableLayout='auto'
            scroll={{ x: 2400 }}
            columns={columns}
            data={reportsData?.reports || []}
            total={reportsData?.pagination.totalCount || 0}
            currentPage={currentPage}
            pageSize={pageSize}
            loading={reportsLoading}
            onPageChange={handlePageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </div>
      </div>
    </div>
  );
};

const Div = tw.div``;
const Flex = tw.div`flex`;

export default Report;
