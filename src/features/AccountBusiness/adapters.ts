
import { CollapseItemData } from 'components/RRCollapseTable';

import { addIsRoleType } from 'utils/role';
import { BreadCrumbItem } from './types';

export const adaptCollapseItemData = (
  data: CollapseItemData[],
): BreadCrumbItem[] => {
  return data.map(item => {
    return {
      label: item.name,
      role_id: item.role_id ?? '',
      level: item.level,
    };
  });
};

export const collapsedData = (user, children, level = 0): CollapseItemData => {
  const result = {
    ...(user || {}),
    key: user?.id?.toString() || 'unknown',
    name: user?.username || user?.name || `User ${user?.id || 'unknown'}`,
    level: level, // Set level cho parent
    parent_info: user?.parent_info
      ? { ...user.parent_info, id: user.parent_info.id }
      : undefined,
    children_count: user?.children_count,
    children: (children || []).map(child => {
      return {
        ...(child || {}),
        key: child?.id?.toString() || 'unknown',
        name:
          child?.full_name || child?.name || `User ${child?.id || 'unknown'}`,
        level: level + 1,
        parent_info: user?.parent_info
          ? { ...user?.parent_info, id: user.parent_info.id }
          : child?.parent_info,
        children: child?.children || [],
      };
    }),
  };

  return result;
};

export const collapsedChildData = (children, level = 1): CollapseItemData[] => {
  const result = (children || []).map(child => {
    return {
      ...child,
      key: child?.id?.toString() || 'unknown',
      name: child?.full_name || child?.name || `User ${child?.id || 'unknown'}`,
      level: level,
    };
  });

  return result;
};

export const addUsersToChildren = ({
  accounts,
  targetId,
  newUsers,
  roles,
  level = 0,
  pagination,
}) => {
  return accounts.map(account => {
    if (account.id?.toString() === targetId?.toString()) {
      const transformedUsers = newUsers.map(user => ({
        ...user,
        ...addIsRoleType(user, roles),
        name: user.full_name || user.name || `User ${user.id}`,
        level: level + 1,
        children: [],
      }));

      // load more
      if (account.pagination && pagination.page === account.pagination.page + 1) {
        return {
          ...account,
          pagination,
          children: account.children.concat(transformedUsers),
        };
      }

      // init

      return {
        ...account,
        pagination,
        children_count: pagination.total_count,
        children: transformedUsers,
      };
    }
    if (account.children && account.children.length > 0) {
      return {
        ...account,
        children_count: account.children_count,
        children: addUsersToChildren({
          accounts: account.children,
          targetId,
          newUsers,
          roles,
          level: level + 1,
          pagination,
        }),
      };
    }
    return account;
  });
};

export const removeItemById = (
  data: CollapseItemData[],
  id: string,
): CollapseItemData[] => {
  return data
    .filter(item => item.id !== id)
    .map(item => ({
      ...item,
      children: item.children ? removeItemById(item.children, id) : [],
    }));
};

export const flattenChildren = (
  data: CollapseItemData[],
): CollapseItemData[] => {
  const result: CollapseItemData[] = [];

  const traverse = (items: CollapseItemData[]) => {
    items.forEach(item => {
      if (item.children) {
        result.push(...item.children);
        traverse(item.children);
      }
    });
  };

  traverse(data);
  return result;
};

export const parseToAccountData = (users: any[], currentUser: any) => {
  if (!currentUser?.id) {
    return [];
  }

  const data = collapsedData(currentUser, users, 0);

  // Build full tree structure với expanded children và correct levels
  const buildTreeStructure = (
    parentData: CollapseItemData,
    allUsers: any[],
  ): CollapseItemData => {
    const result = { ...parentData };

    if (result.children) {
      result.children = result.children.map(child => {
        const hasExpandedChildren = child.children && child.children.length > 0;

        if (hasExpandedChildren) {
          return buildTreeStructure(child, allUsers);
        } else {
          return {
            ...child,
            level: (result.level || 0) + 1, // Ensure correct level
            children: [],
          };
        }
      });
    }

    return result;
  };

  const result = [buildTreeStructure(data, users)];

  return result;
};
