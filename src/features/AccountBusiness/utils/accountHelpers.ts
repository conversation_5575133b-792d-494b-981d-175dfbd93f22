import { CollapseItemData } from 'components/RRCollapseTable';

export const findAccountById = (
  accounts: CollapseItemData[],
  id: string,
): CollapseItemData | null => {
  const strId = id.toString();
  for (const account of accounts) {
    if (account.id?.toString() === strId) return account;
    if (account.children) {
      const found = findAccountById(account.children, strId);
      if (found) return found;
    }
  }
  return null;
};

export const buildBreadcrumbPath = (
  targetAccount: CollapseItemData,
  accounts: CollapseItemData[],
): CollapseItemData[] => {
  const findPath = (
    accounts: CollapseItemData[],
    target: CollapseItemData,
    currentPath: CollapseItemData[] = [],
  ): CollapseItemData[] | null => {
    for (const account of accounts) {
      const newPath = [...currentPath, account];
      if (account.id === target.id) return newPath;
      if (account.children) {
        const found = findPath(account.children, target, newPath);
        if (found) return found;
      }
    }
    return null;
  };
  return findPath(accounts, targetAccount) || [];
};

export const getMultipleSelectedAccounts = (
  selectedIds: string[],
  accountData: CollapseItemData[],
): CollapseItemData[] => {
  if (!selectedIds.length) return [];
  const flatRecursiveChildren = (accounts: CollapseItemData[]) => {
    return accounts.flatMap(account => {
      return [account, ...flatRecursiveChildren(account.children || [])];
    });
  };
  const allChildren = flatRecursiveChildren(accountData);
  return allChildren.filter(
    child => child?.id && selectedIds.includes(child.id.toString()),
  );
};

export const createBreadcrumbFromItem = (
  item: CollapseItemData,
  accountData: CollapseItemData[],
) => {
  const breadcrumbItems: any[] = [];
  let currentItem: CollapseItemData | null = item;

  while (currentItem) {
    const breadcrumbItem: any = {
      level: currentItem?.is_end_user ? 2 : currentItem.level,
    };

    breadcrumbItem.label =
      currentItem.username || currentItem.name || currentItem.full_name;
    if (currentItem.role_id) {
      const roleIdNumber = parseInt(currentItem.role_id, 10);
      if (!isNaN(roleIdNumber)) {
        breadcrumbItem.role_id = roleIdNumber;
      }
    }

    breadcrumbItems.unshift(breadcrumbItem);

    if (currentItem.parent_info?.id) {
      currentItem = findAccountById(accountData, currentItem.parent_info.id);
    } else {
      currentItem = null;
    }
  }

  return breadcrumbItems;
};
