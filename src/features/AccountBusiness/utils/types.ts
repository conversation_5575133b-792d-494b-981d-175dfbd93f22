import { CollapseItemData } from 'components/RRCollapseTable';

import { BreadCrumbItem } from '../types';

export interface AppState {
  accountData: CollapseItemData[];
  selectedAccount: CollapseItemData | null;
  expandAccountIds: string[];
  breadcrumb: BreadCrumbItem[];
  searchTerm: string;
  modals: Record<string, boolean>;
  modalProps: Record<string, any>;
  multipleSelect: { isActive: boolean; selectedIds: string[] };
}

export interface DevicePageInfo {
  page: number;
  total_count: number;
  total_pages: number;
}

export interface DeviceData {
  devices: any[];
  pageInfo: DevicePageInfo;
}
