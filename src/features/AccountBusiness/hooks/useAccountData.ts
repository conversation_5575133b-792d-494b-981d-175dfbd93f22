import { useQuery } from '@tanstack/react-query';

import { AccountRole } from 'features/Profile/types';
import { USER_ID_KEY, getCookie } from 'services/cookies';
import queryClient, { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';
import { addIsRoleType, getLevelAccount } from 'utils/role';

import { parseToAccountData } from '../adapters';

export const useAccountData = (profile?: any) => {
  const accounts = useQuery({
    queryKey: queryKeys.accounts.all,
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/users/children?user_id=${profile?.id}&page=1&per_page=50`,
    }),
    enabled: !!profile?.id,
    staleTime: 0,
    select: (data: any) => {
      const users = data.users || [];

      const { roles = [] } =
        queryClient.getQueryData<{
          roles: AccountRole[];
        }>(queryKeys.roles.all) || {};

      if (!users.length) {
        if (profile) {
          return [
            {
              ...profile,
              ...addIsRoleType(profile, roles),
              name: profile?.username || profile?.full_name || profile?.name,
              level: getLevelAccount(profile),
            },
          ];
        }
        return [];
      }

      const newUsers = users.map(user => ({
        ...user,
        ...addIsRoleType(user, roles),
      }));

      const result = parseToAccountData(newUsers, profile);
      return result;
    },
  });

  return { accounts };
};

// Hook cho user detail
export const useUserDetail = (userId?: string) => {
  return useQuery({
    queryKey: queryKeys.accounts.detail(userId || ''),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/users/${userId}`,
    }),
    enabled: !!userId,
    staleTime: 0,
  });
};

export const useSearchAccounts = (searchTerm: string, profile?: any) => {
  return useQuery({
    queryKey: queryKeys.accounts.search({
      searchTerm,
      userId: profile?.id,
    }),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/users/children?search=${searchTerm}&user_id=${
        profile?.id || getCookie(USER_ID_KEY)
      }`,
    }),
    enabled: !!searchTerm && !!profile?.id,
    staleTime: 0,
    select: (data: any) => {
      const { roles = [] } =
        queryClient.getQueryData<{
          roles: AccountRole[];
        }>(queryKeys.roles.all) || {};
      const users = data.users || [];
      const transformedUsers = users.map(user => {
        const addRole: {
          [key: string]: boolean;
        } = addIsRoleType(user, roles);
        return {
          ...user,
          ...addIsRoleType(user, roles),
          name:
            user?.full_name || user?.name || `User ${user?.id || 'unknown'}`,
          key: user?.id?.toString() || 'unknown',
          id: user?.id,
          level: addRole.is_end_user ? 2 : 1,
        };
      });
      return transformedUsers;
    },
  });
};
