import { useCallback, useState } from 'react';

import { AppState } from '../utils/types';

const initialState: AppState = {
  accountData: [],
  expandAccountIds: [],
  selectedAccount: null,
  breadcrumb: [],
  searchTerm: '',
  modals: {
    accountLog: false,
    addDevice: false,
    addAccount: false,
    moveAccount: false,
    confirmRemove: false,
  },
  modalProps: {},
  multipleSelect: { isActive: false, selectedIds: [] },
};

export const useAccountState = () => {
  const [state, setState] = useState<AppState>(initialState);

  const updateState = useCallback((updates: Partial<AppState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const toggleModal = useCallback(
    (modal: string, isOpen?: boolean, data?: any) => {
      setState(prev => ({
        ...prev,
        modals: { ...prev.modals, [modal]: isOpen ?? !prev.modals[modal] },
        modalProps: {
          ...prev.modalProps,
          [modal]: isOpen ? data : undefined,
        },
      }));
    },
    [],
  );

  const changeExpandAccountIds = useCallback((ids: string[]) => {
    updateState({ expandAccountIds: ids });
  }, []);

  const resetSelection = useCallback(() => {
    updateState({
      selectedAccount: null,
      breadcrumb: [],
    });
  }, []);

  return {
    state,
    updateState,
    toggleModal,
    resetSelection,
    changeExpandAccountIds,
  };
};
