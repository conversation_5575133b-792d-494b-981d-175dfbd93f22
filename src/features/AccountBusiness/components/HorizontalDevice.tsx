import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useQuery } from '@tanstack/react-query';
import useDebounce from 'hooks/useDebounce';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn } from 'services/reactQuery';
import type { DeviceType } from 'types/device';

import images from 'assets/images';
import { BodyMdBase, FontBold, InputSm } from 'assets/styles';
import { Icon } from 'components';
import { LinkButton } from 'components/Button';
import { DrawerDeviceDetail } from 'components/DeviceModal';
import Spinner from 'components/Spinner';

const defaultValue = {
  devices: [],
  pagination: {
    page: 1,
    total_count: 0,
    total_pages: 1,
  },
};

const DeviceTitle = ({ plateNumber, name }) => (
  <div
    className={`truncate-1-line flex items-center gap-3 uppercase ${BodyMdBase} ${FontBold}`}
  >
    {plateNumber && <span>{plateNumber}</span>} -{' '}
    {name && <span className='uppercase'>{name}</span>}
  </div>
);

const DeviceDescription = ({ imei, deviceSimNumber }) => {
  if (imei && deviceSimNumber) {
    return (
      <div
        className={`${InputSm} truncate-1-line flex items-center gap-3 text-grey-500`}
      >
        <span>{deviceSimNumber}</span> - <span>{imei}</span>
      </div>
    );
  }

  return (
    <div
      className={`${InputSm} truncate-1-line flex items-center gap-3 text-grey-500`}
    >
      <span>{deviceSimNumber || imei}</span>
    </div>
  );
};

const DeviceList = ({ accountId }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [selectedDeviceImei, setSelectedDeviceImei] = useState<string>('');
  const [openDrawer, setOpenDrawer] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [allDevices, setAllDevices] = useState<DeviceType[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Track current accountId to detect changes
  const prevAccountIdRef = useRef<number | null>(null);

  const {
    data,
    isLoading,
    isFetching,
    isError,
    error: queryError,
  } = useQuery({
    queryKey: ['devices', accountId, currentPage],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: '/devices',
        query: {
          page: currentPage.toString(),
          per_page: '20',
          owner_id: accountId?.toString(),
        },
      }),
    }),
    enabled: !!accountId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 1, // Chỉ retry 1 lần nếu fail
    select: (data: any): any => {
      return {
        devices: data.device_infos || [],
        pagination: {
          page: data.page,
          total_count: data.total_count,
          total_pages: data.total_pages,
        },
      };
    },
  });

  const { devices = [], pagination } = data || defaultValue;

  // Handle errors
  useEffect(() => {
    if (isError && queryError) {
      setError('Failed to load devices');
      setLoading(false);
    } else {
      setError(null);
    }
  }, [isError, queryError]);

  useEffect(() => {
    if (prevAccountIdRef.current !== accountId) {
      setAllDevices(devices); // Set devices from API directly
      setCurrentPage(1);
      setLoading(false);
      setHasMore(true);
      setError(null);
      prevAccountIdRef.current = accountId;
    } else {
      // Normal pagination - append devices
      if (currentPage === 1) {
        setAllDevices(devices);
      } else {
        setAllDevices(prev => {
          const existingIds = new Set(prev.map(d => d.imei));
          const newDevices = devices.filter(d => !existingIds.has(d.imei));
          return [...prev, ...newDevices];
        });
      }
    }

    const newHasMore = currentPage < (pagination?.total_pages || 1);
    setHasMore(newHasMore);
    setLoading(false);
  }, [
    devices,
    currentPage,
    pagination?.total_pages,
    accountId,
    prevAccountIdRef,
  ]);

  // Navigation button states
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const loadMoreDevices = useCallback(() => {
    if (
      !hasMore ||
      loading ||
      isFetching ||
      isLoading ||
      error ||
      currentPage >= (pagination?.total_pages || 1) ||
      allDevices.length === 0
    ) {
      return;
    }

    setLoading(true);
    setCurrentPage(prev => prev + 1);
  }, [
    hasMore,
    loading,
    isFetching,
    isLoading,
    error,
    currentPage,
    pagination?.total_pages,
    allDevices.length,
  ]);

  const debouncedLoadMore = useDebounce(loadMoreDevices, 1000);

  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftButton(scrollLeft > 0);
    setShowRightButton(scrollLeft + clientWidth < scrollWidth);

    if (
      allDevices.length > 0 && // Phải có devices
      scrollLeft > 0 && // Phải thực sự scroll (không phải lúc đầu)
      scrollLeft + clientWidth >= scrollWidth - 50 && // Gần cuối
      scrollWidth > clientWidth // Phải có content scroll được
    ) {
      debouncedLoadMore();
    }
  }, [debouncedLoadMore, allDevices.length]);

  const scrollLeft = useCallback(() => {
    if (scrollContainerRef.current) {
      // Scroll by 2 card widths (245px each + gap)
      scrollContainerRef.current.scrollBy({ left: -500, behavior: 'smooth' });
    }
  }, []);

  const scrollRight = useCallback(() => {
    if (scrollContainerRef.current) {
      // Scroll by 2 card widths (245px each + gap)
      scrollContainerRef.current.scrollBy({ left: 500, behavior: 'smooth' });
    }
  }, []);

  const handleOpenDrawerDevice = useCallback((deviceImei: string) => {
    if (!deviceImei) return;
    setSelectedDeviceImei(deviceImei);
    setOpenDrawer(true);
  }, []);

  const handleCloseDrawer = useCallback(() => {
    setOpenDrawer(false);
    setSelectedDeviceImei('');
  }, []);

  const navigateDevices = () => {
    navigate(`/business/devices?owner_id=${accountId}`);
  };

  // Calculate grid positioning for 2-row layout with pattern: 1 2 3 7 8 9 / 4 5 6 10 11 12
  const getGridPosition = useCallback(
    (index: number) => {
      // Special case: if only 2 devices, show them horizontally (side by side)
      if (allDevices.length === 2) {
        return `row-start-1 col-start-${index + 1}`; // Both on row 1, different columns
      }

      // Pattern: 1 2 3 7  8  9   (hàng 1)
      //          4 5 6 10 11 12  (hàng 2)
      // Logic:
      // - Devices 0,1,2 -> row 1, col 1,2,3
      // - Devices 3,4,5 -> row 2, col 1,2,3
      // - Devices 6,7,8 -> row 1, col 4,5,6 (tiếp tục hàng 1)
      // - Devices 9,10,11 -> row 2, col 4,5,6 (tiếp tục hàng 2)

      const groupIndex = Math.floor(index / 6); // Mỗi nhóm 6 devices (3 hàng 1 + 3 hàng 2)
      const positionInGroup = index % 6;

      let row, col;

      if (positionInGroup < 3) {
        // 3 devices đầu của nhóm -> hàng 1
        row = 1;
        col = groupIndex * 3 + (positionInGroup + 1);
      } else {
        // 3 devices sau của nhóm -> hàng 2
        row = 2;
        col = groupIndex * 3 + (positionInGroup - 3 + 1);
      }

      return `row-start-${row} col-start-${col}`;
    },
    [allDevices.length],
  );

  // Calculate grid template columns based on number of devices
  const gridTemplateColumns = useMemo(() => {
    // Use fixed width để đảm bảo tất cả items có width đều nhau
    const cols = Math.ceil(allDevices.length / 2);
    return `repeat(${cols}, 200px)`; // Fixed width 200px cho mỗi column
  }, [allDevices.length]);

  useEffect(() => {
    // Delay scroll listener để đợi devices render xong
    const timeoutId = setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.addEventListener('scroll', handleScroll);
        handleScroll(); // Initial check
      }
    }, 100); // Delay 100ms

    return () => {
      clearTimeout(timeoutId);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [handleScroll]);

  useEffect(() => {
    if (scrollContainerRef.current) {
      const { scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowRightButton(scrollWidth > clientWidth);
    }
  }, [allDevices]);

  return (
    <>
      <Container>
        <Header>
          <HeaderText className={BodyMdBase}>
            {t('deviceList.deviceList')} ({pagination?.total_count || 0})
          </HeaderText>
          <LinkButton onClick={navigateDevices} size='small' className='h-6'>
            {t('deviceList.manageDevices')}
          </LinkButton>
        </Header>
        {showLeftButton && (
          <ChevronButtonLeft onClick={scrollLeft}>
            <Icon src={images.Icon.ChevronLeft} />
          </ChevronButtonLeft>
        )}
        {showRightButton && (
          <ChevronButtonRight onClick={scrollRight}>
            <Icon src={images.Icon.ChevronRight} />
          </ChevronButtonRight>
        )}

        <ScrollContainer ref={scrollContainerRef}>
          {(isLoading || isFetching) && allDevices.length === 0 ? (
            <div className='flex w-full flex-col items-center justify-center py-8'>
              <Spinner />
            </div>
          ) : (
            <>
              {allDevices.length > 0 ? (
                <GridContainer
                  style={{
                    gridTemplateColumns,
                    gridTemplateRows: 'repeat(3, 1fr)',
                    height: '140px', // Fixed height for 2 rows (64px card + gap + padding)
                  }}
                >
                  {allDevices.map((device, index) => {
                    if (!device) return null;
                    return (
                      <GridItem
                        key={device.device_imei}
                        className={getGridPosition(index)}
                      >
                        <DeviceCard
                          onClick={() => {
                            handleOpenDrawerDevice(device.device_imei);
                          }}
                        >
                          <DeviceTitle
                            plateNumber={device.device_plate_number}
                            name={device.device_name}
                          />
                          <DeviceDescription
                            deviceSimNumber={device.device_sim_number}
                            imei={device.device_imei}
                          />
                        </DeviceCard>
                      </GridItem>
                    );
                  })}
                </GridContainer>
              ) : (
                !isLoading &&
                !isFetching && (
                  <div className='flex w-full flex-col items-center justify-center'>
                    <Icon
                      src={images.Icon.EmptyPanel}
                      className='h-[80px] w-auto'
                    />
                  </div>
                )
              )}
            </>
          )}
        </ScrollContainer>
      </Container>
      <DrawerDeviceDetail
        visible={openDrawer}
        onClose={handleCloseDrawer}
        selectedImei={selectedDeviceImei}
      />
    </>
  );
};

export default DeviceList;

const Container = tw.div`relative`;
const Header = tw.div`my-2 flex items-center justify-between`;
const HeaderText = tw.div`text-grey-400`;
const ChevronButtonLeft = tw.button`absolute left-0 top-1/2 size-[32px] justify-items-center rounded-lg border border-grey-100 bg-white-1000 z-10`;
const ChevronButtonRight = tw.button`absolute right-0 top-1/2 size-[32px] justify-items-center rounded-lg border border-grey-100 bg-white-1000 z-10`;
const ScrollContainer = tw.div`overflow-x-auto overflow-y-hidden`;
const GridContainer = tw.div`grid gap-2 min-w-full`;
const GridItem = tw.div``;
const DeviceCard = tw.div`cursor-pointer bg-white rounded-[12px] border border-grey-100 p-3 w-[200px] h-[64px] flex flex-col justify-between transition-shadow duration-200 hover:bg-grey-100`;
