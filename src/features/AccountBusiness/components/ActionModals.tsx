import React, { useCallback, useMemo } from 'react';

import { t } from 'i18next';

import { RRConfirmationModal } from 'components';
import { CollapseItemData } from 'components/RRCollapseTable';

import { getMultipleSelectedAccounts } from '../utils';
import AddAccountModal from './AddAccountModal';
import MoveAccountModal from './MoveAccountModal';

interface ActionModalsProps {
  accountData: CollapseItemData[];
  multipleSelect: {
    isActive: boolean;
    selectedIds: string[];
  };
  mutations: any;
  modals: Record<string, boolean>;
  modalProps: Record<string, any>;
  selectedAccount: CollapseItemData | null;
  onToggleModal: (modal: string, isOpen?: boolean) => void;
  refreshExpandParentAccount: (parentId: string) => void;
  onDeselectAccount: () => void;
  updateState: (data: any) => void;
}

const ActionModals: React.FC<ActionModalsProps> = ({
  accountData,
  multipleSelect,
  mutations,
  modals,
  modalProps,
  selectedAccount,
  onToggleModal,
  refreshExpandParentAccount,
  updateState,
  onDeselectAccount,
}) => {
  const multipleAccountSelected = useMemo(() => {
    return getMultipleSelectedAccounts(multipleSelect.selectedIds, accountData);
  }, [multipleSelect.selectedIds, accountData]);

  const handleCloseModal = (modalType: string) => () => {
    onToggleModal(modalType, false);
  };

  const handleSelectMultipleAccount = useCallback(
    (accounts: CollapseItemData[]) => {
      const ids = accounts.map(item => item.id?.toString());
      updateState({
        multipleSelect: { ...multipleSelect, selectedIds: ids },
      });
    },
    [updateState, multipleSelect],
  );

  const handleTransferMultipleAccount = useCallback(
    (userIds: string[], parentId: string) => {
      mutations.transferMultipleAccount.mutate(
        { userIds, parentId },
        {
          onSuccess: () => {
            updateState({
              multipleSelect: { isActive: false, selectedIds: [] },
            });
            handleCloseModal('moveAccount')();
          },
        },
      );
    },
    [mutations.transferMultipleAccount, updateState, handleCloseModal],
  );

  const handleDeleteAccount = useCallback(() => {
    const currentId = selectedAccount?.id;
    const currentParentId = selectedAccount?.parent_info?.id;
    if (currentId && currentParentId) {
      mutations.deleteAccount.mutate(
        {
          id: currentId,
          parentId: currentParentId,
        },
        {
          onSuccess: () => {
            refreshExpandParentAccount(currentParentId);
            setTimeout(() => {
              onDeselectAccount();
              handleCloseModal('confirmRemove')();
            }, 0);
          },
        },
      );
    }
  }, [
    mutations,
    selectedAccount?.id,
    mutations.deleteAccount,
    onDeselectAccount,
    handleCloseModal,
    refreshExpandParentAccount,
  ]);

  return (
    <>
      <AddAccountModal
        visible={modals.addAccount}
        modalProps={modalProps?.addAccount}
        refreshExpandParentAccount={refreshExpandParentAccount}
        onClose={handleCloseModal('addAccount')}
      />

      <MoveAccountModal
        multipleAccountSelected={multipleAccountSelected}
        visible={modals.moveAccount}
        onSelectedMultipleAccount={handleSelectMultipleAccount}
        onClose={handleCloseModal('moveAccount')}
        onSubmit={handleTransferMultipleAccount}
      />

      <RRConfirmationModal
        title={t('business.deleteAccount')}
        message={t('business.deleteAccountDesc')}
        visible={modals.confirmRemove}
        isSubmitting={mutations?.deleteAccount?.isPending}
        onCancel={handleCloseModal('confirmRemove')}
        onConfirm={handleDeleteAccount}
      />
    </>
  );
};

export default ActionModals;
