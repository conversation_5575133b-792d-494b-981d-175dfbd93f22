import React, { useEffect } from 'react';

import { Modal } from 'antd';
import { FormProvider, useForm } from 'react-hook-form';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { ButtonSm, FontMedium } from 'assets/styles';
import { Icon, RRFieldInput } from 'components';
import type { BreadcrumbItem } from 'components/AccountBreadcrumb';
import AccountBreadCrumb from 'components/AccountBreadcrumb';
import { CollapseItemData } from 'components/RRCollapseTable';

interface ResetPasswordModalProps {
  visible: boolean;
  isDeleting: boolean;
  breadcrumb: BreadcrumbItem[];
  selectedAccount: CollapseItemData;
  onClose: () => void;
  onSubmit: (payload: { id: string; password: string }) => void;
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  breadcrumb,
  visible,
  isDeleting,
  onClose,
  onSubmit,
  selectedAccount,
}) => {
  const methods = useForm({
    defaultValues: {
      password: '123456',
    },
  });

  const { control } = methods;

  const handleSubmit = () => {
    const password = methods.getValues('password');
    const payload = {
      id: selectedAccount.id ?? '',
      password: password,
      password_confirmation: password,
    };
    onSubmit(payload);
  };

  useEffect(() => {
    if (!visible) {
      methods.reset();
    }
  }, [visible]);

  return (
    <>
      <Modal
        centered
        title={<Title>{t('business.resetPass')}</Title>}
        open={visible}
        onCancel={onClose}
        onOk={onClose}
        footer={[]}
        closeIcon={
          <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
        }
      >
        <FormProvider {...methods}>
          <Body>
            <Row>
              <AgencyAccountContainer>
                <AccountBreadCrumb breadcrumb={breadcrumb} />
              </AgencyAccountContainer>
            </Row>
            <Row>
              <RRFieldInput
                control={control}
                defaultValue={methods.getValues('password')}
                label={t('login.password')}
                id='password'
                className='w-full'
                placeholder={t('login.passwordPlaceholder')}
                prefixIcon={images.Icon.Lock}
              />
            </Row>
            <SubmitButton
              className={`${ButtonSm} ${FontMedium}`}
              type='submit'
              onClick={handleSubmit}
              disabled={isDeleting}
            >
              {isDeleting ? t('common.saving') : t('business.saveChangeButton')}
            </SubmitButton>
          </Body>
        </FormProvider>
      </Modal>
    </>
  );
};
const Title = tw.span`text-xl leading-[28px]`;
const Body = tw.div`flex flex-col gap-3 py-1`;
const Row = tw.div`flex flex-col gap-1`;
const SubmitButton = tw.button`w-full h-[40px] rounded-lg bg-brand-300 py-2 text-black-1000`;
const AgencyAccountContainer = tw.div`w-full rounded-xl border-grey-100 border-[1px] border-solid box-border flex flex-row items-center justify-start py-2 px-3 gap-2 text-left text-sm text-text-secondary font-medium`;
export default ResetPasswordModal;
