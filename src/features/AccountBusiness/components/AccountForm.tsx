import { useEffect, useMemo } from 'react';

import dayjs from 'dayjs';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { AccountRole } from 'features/Profile/types';

import images from 'assets/images';
import { ButtonSm, FontMedium } from 'assets/styles';
import { LinkButton, PrimaryButton } from 'components/Button';
import RRFieldDatePicker from 'components/FormField/RRFieldDatePicker';
import RRFieldInput from 'components/FormField/RRFieldInput';
import RRFieldPassword from 'components/FormField/RRFieldPassword';
import RRFieldRadio from 'components/FormField/RRFieldRadio';
import RRFieldSelect from 'components/FormField/RRFieldSelect';
import { CollapseItemData } from 'components/RRCollapseTable';

import { useUserDetail } from '../hooks';

interface Props {
  isUpdating: boolean;
  isDeleting: boolean;
  roles: AccountRole[];
  selectedAccount: CollapseItemData;
  onUpdateAccount: (data: any) => void;
  onOpenModal: (modalType: string) => () => void;
  onOpenSimpleTransferModal: () => void;
}

const AccountForm: React.FC<Props> = ({
  isUpdating,
  selectedAccount,
  roles = [],
  onOpenModal,
  onUpdateAccount,
  onOpenSimpleTransferModal,
}) => {
  const { t } = useTranslation();

  const {
    data: { user } = {
      user: null,
    },
  } = useUserDetail(selectedAccount?.id);

  const rolesOptions = useMemo(
    () =>
      roles
        .filter(
          item =>
            selectedAccount.role_type &&
            item?.role_type === selectedAccount?.role_type,
        )
        .map(role => ({
          value: role.id,
          label: role.name,
        })),
    [roles, selectedAccount],
  );

  const methods = useForm({
    defaultValues: {
      id: '',
      username: '',
      role_id: '',
      full_name: '',
      phone_no: '',
      email: '',
      address: '',
      country: '',
      created_at: '',
      active: true,
      active_area: 'Việt Nam',
      password: '',
    },
  });
  const control = methods.control;

  const { handleSubmit, formState, reset, watch } = methods || {};
  const { errors } = formState;
  const watchCreatedAt = watch('created_at');

  useEffect(() => {
    if (user) {
      reset({
        id: user?.id?.toString(),
        role_id: user.role_id?.toString(),
        full_name: user.full_name,
        phone_no: user.phone_no,
        email: user.email,
        address: user.address,
        active_area: user.active_area,
        active: user.active,
        created_at: user?.created_at,
        username: user?.username,
        password: user?.raw_password,
      });
    }
  }, [user, reset]);

  const onSubmit = data => {
    onUpdateAccount({
      ...data,
      role_id: data?.role,
    });
  };

  return (
    <FormProvider {...methods}>
      <FormContainer onSubmit={handleSubmit(onSubmit)} autoComplete='off'>
        <GridFormWrapper>
          <Grid className='grid-cols-2 gap-3 border-b border-grey-100'>
            <div>
              <RRFieldInput
                id='username'
                disabled
                control={control}
                label={t('accountForm.account')}
                placeholder={t('accountForm.account')}
                className='w-full'
                prefixIcon={images.Icon.User}
                autoComplete='off'
              />
              {selectedAccount?.is_end_user && (
                <div className='flex w-full items-center justify-end'>
                  <LinkButton
                    onClick={onOpenSimpleTransferModal}
                    size='small'
                    className='mb-3 mt-2 h-6  text-end'
                  >
                    {t('accountForm.transferAccount')}
                  </LinkButton>
                </div>
              )}
            </div>
            <div>
              <RRFieldPassword
                control={control}
                id='password'
                label={t('accountForm.password')}
                disabled
                placeholder={t('accountForm.password')}
                className='w-full'
                prefixIcon={images.Icon.Lock}
                autoComplete='new-password'
              />
              <div className='flex w-full items-center justify-end'>
                <LinkButton
                  onClick={onOpenModal('resetPassword')}
                  size='small'
                  className='mb-3 mt-2 h-6 text-end'
                >
                  {t('accountForm.resetPassword')}
                </LinkButton>
              </div>
            </div>
          </Grid>
          <Grid className='grid-cols-2 gap-3 pt-3'>
            <RRFieldSelect
              id='role_id'
              control={control}
              label={t('accountForm.role')}
              className='w-full'
              options={rolesOptions}
              errors={errors}
            />
            <RRFieldInput
              id='full_name'
              control={control}
              label={t('accountForm.fullName')}
              placeholder={t('accountForm.enterFullName')}
              className=' w-full'
              errors={errors}
              prefixIcon={images.Icon.AaIcon}
            />
            <RRFieldInput
              id='phone_no'
              control={control}
              label={t('accountForm.phoneNumber')}
              placeholder={t('accountForm.enterPhoneNumber')}
              errors={errors}
              prefixIcon={images.Icon.PhoneCall}
            />
            <RRFieldInput
              id='email'
              control={control}
              label={t('accountForm.email')}
              placeholder={t('accountForm.enterEmail')}
              className='w-full'
              errors={errors}
              prefixIcon={images.Icon.MailClose}
            />
            <RRFieldInput
              id='address'
              control={control}
              label={t('accountForm.address')}
              placeholder={t('accountForm.enterAddress')}
              className='w-full'
              errors={errors}
              prefixIcon={images.Icon.PinLocation}
            />
            <RRFieldInput
              id='country'
              control={control}
              label={t('accountForm.operatingArea')}
              disabled
              placeholder={t('accountForm.vietnam')}
              className='w-full'
              prefixIcon={images.Icon.VNFlag}
            />
            <RRFieldDatePicker
              control={control}
              disabled
              id='created_at'
              label={t('accountForm.creationDate')}
              placeholder={t('accountForm.creationDate')}
              className='w-full'
              defaultValue={watchCreatedAt ? dayjs(watchCreatedAt) : undefined}
              dateFormat='DD/MM/YYYY HH:mm:ss'
              errors={errors}
              prefixIcon={images.Icon.CalendarSchedule}
            />
            <RRFieldRadio
              control={control}
              id='active'
              label={t('accountForm.loginStatus')}
              className='w-full'
              errors={errors}
            />
          </Grid>
          <PrimaryButton
            className={`${ButtonSm} ${FontMedium} mt-6 w-full`}
            htmlType='submit'
            disabled={isUpdating || !formState.isDirty || !formState.isValid}
            loading={isUpdating}
          >
            {isUpdating ? t('common.saving') : t('accountForm.saveInfo')}
          </PrimaryButton>
        </GridFormWrapper>
      </FormContainer>
    </FormProvider>
  );
};

export default AccountForm;

export const FormContainer = tw.form``;
export const GridFormWrapper = tw.div`h-full rounded-2xl p-6`;
export const Grid = tw.div`grid gap-3`;
export const SubmitButton = tw.button`w-full mt-6 h-[40px] rounded-lg bg-brand-300 text-black-1000`;
