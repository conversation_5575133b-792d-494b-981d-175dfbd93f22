import { useEffect, useRef, useState } from 'react';

import { List } from 'antd';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { Icon } from 'components';
import { LinkButton } from 'components/Button';
import EmptyPanel from 'components/EmptyPanel';
import RRCollapseTable, {
  CollapseItemData,
  SearchCollapseItem,
} from 'components/RRCollapseTable';
import RRScrollView from 'components/RRScrollView/RRScrollView';

interface AccountListProps {
  className?: string;
  enableMultipleSelect?: boolean;
  searchTerm: string;
  expandAccountIds: string[];
  searchData: CollapseItemData[];
  accountData: CollapseItemData[];
  selectedAccount: CollapseItemData | null;
  multipleSelect: { isActive: boolean; selectedIds: string[] };
  onSelectAccount: (item: any, path?: any) => void;
  onToggleMultipleSelect: (isActive: boolean) => void;
  onOpenMoveModal: () => void;
  onOpenAddAccountModal: (data: CollapseItemData) => void;
  onExpandAccount: (id: string) => void;
  onSelectedMultipleIds: (ids: string[]) => void;
  onChangeBreadcrumb: (item: any) => void;
  onLoadMore: (data: CollapseItemData) => void;
}

const AccountList = ({
  className,
  enableMultipleSelect,
  expandAccountIds,
  searchTerm,
  searchData,
  accountData,
  selectedAccount,
  multipleSelect,
  onSelectAccount,
  onToggleMultipleSelect,
  onOpenMoveModal,
  onOpenAddAccountModal,
  onExpandAccount,
  onSelectedMultipleIds,
  onChangeBreadcrumb,
  onLoadMore,
}: AccountListProps) => {


  const accountListWrapper = useRef(null);

  if (searchTerm) {
    return (
      <RRScrollView className='w-[calc(100%+16px)]'>
        <div className='h-full w-[calc(100%-16px)] overflow-x-hidden rounded-xl border border-[#d9d9d9]'>
          <List
            itemLayout='horizontal'
            dataSource={searchData}
            locale={{ emptyText: 'Không tìm thấy' }}
            renderItem={item => (
              <SearchCollapseItem
                key={item.id}
                isActive={selectedAccount?.id === item.id}
                item={item}
                onSelectItem={() => {
                  onSelectAccount(item);
                  onChangeBreadcrumb(item);
                }}
              />
            )}
          />
        </div>
      </RRScrollView>
    );
  }

  return (
    <div className={twMerge('h-full flex flex-col', className)}>
      {enableMultipleSelect && (
        <>
          {multipleSelect.isActive ? (
            <div className='mb-3 flex items-center justify-between'>
              <LinkButton
                onClick={onOpenMoveModal}
                size='small'
                className='h-6'
                disabled={!multipleSelect.selectedIds.length}
              >
                Chuyển tài khoản
              </LinkButton>
              <div
                className='flex items-center gap-3'
                onClick={() => onToggleMultipleSelect(false)}
              >
                <div className='text-sm text-grey-400'>
                  {`Đã chọn(${multipleSelect.selectedIds.length})`}
                </div>
                <RemoveButton>
                  <Icon src={images.Icon.RadiusRemove} />
                </RemoveButton>
              </div>
            </div>
          ) : (
            <div className='mb-3 flex items-center justify-end'>
              <LinkButton
                onClick={() => onToggleMultipleSelect(true)}
                size='small'
                className='h-6'
              >
                {t('business.selectMultipleAccount')}
              </LinkButton>
            </div>
          )}
        </>
      )}
      {accountData?.length === 0 && (
        <div className='flex h-[300px] items-center justify-center'>
          <EmptyPanel />
        </div>
      )}
      {accountData?.length > 0 && (
        <div className='h-full flex-1' ref={accountListWrapper}>
          <RRScrollView className='w-[calc(100%+16px)]'>
            <div className='w-[calc(100%-16px)]'>
              <RRCollapseTable
                expandIds={expandAccountIds}
                selectedItem={selectedAccount}
                multipleSelectedIds={multipleSelect.selectedIds}
                isSelectedMultiple={multipleSelect.isActive}
                data={accountData}
                onExpand={onExpandAccount}
                onSelectId={(item, path) => {
                  onSelectAccount(item, path);
                  onChangeBreadcrumb(item);
                }}
                onAddNewItem={onOpenAddAccountModal}
                onSelectedMultipleIds={onSelectedMultipleIds}
                onLoadMore={onLoadMore}
              />
            </div>
          </RRScrollView>
        </div>
      )}
    </div>
  );
};

const RemoveButton = tw.button`flex size-5 items-center justify-center rounded-[1000px] bg-red-10 p-[2px] text-sm`;

export default AccountList;
