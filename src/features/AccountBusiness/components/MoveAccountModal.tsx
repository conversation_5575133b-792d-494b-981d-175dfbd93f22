import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Flex, Modal } from 'antd';
import { Trans } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import {
  BodyMdExtend,
  ButtonSm,
  FontMedium,
  FontSemibold,
} from 'assets/styles';
import { Icon } from 'components';
import AccountDestinationModal from 'components/AccountDestinationModal';
import { CollapseItemData } from 'components/RRCollapseTable';

interface MoveDeviceModalProps {
  visible: boolean;
  multipleAccountSelected: CollapseItemData[];
  onClose?: () => void;
  onSubmit: (userIds: string[], parentId: string) => void;
  onSelectedMultipleAccount?: (keys: CollapseItemData[]) => void;
}

const MoveAccountModal: React.FC<MoveDeviceModalProps> = ({
  visible,
  onClose,
  onSubmit,
  onSelectedMultipleAccount,
  multipleAccountSelected = [],
}) => {
  const [showDestinationModal, setShowDestinationModal] = useState(false);
  const [destinationAccount, setDestinationAccount] = useState<
    CollapseItemData | null | undefined
  >(null);

  const isButtonDisable = useMemo(() => {
    return !destinationAccount || multipleAccountSelected.length === 0;
  }, [destinationAccount, multipleAccountSelected]);

  const handleSelectDestinationModal = (
    selectedItem: CollapseItemData | null,
  ) => {
    if (selectedItem) {
      setDestinationAccount(selectedItem);
      setShowDestinationModal(false);
    }
  };

  const handleSubmit = useCallback(() => {
    const userIds = multipleAccountSelected.map(
      item => item?.id?.toString(),
    ) as string[];
    const parentId = destinationAccount?.id?.toString() ?? '';
    if (userIds.length > 0 && parentId) {
      onSubmit(userIds, parentId);
    }
  }, [onSubmit, destinationAccount, multipleAccountSelected]);

  const handleRemoveAccount = useCallback(
    (id?: string) => () => {
      const filtered = multipleAccountSelected.filter(
        item => item.id && item.id.toString() !== id?.toString(),
      );
      onSelectedMultipleAccount?.(filtered);
    },
    [multipleAccountSelected],
  );

  const handleResetState = useCallback(() => {
    setShowDestinationModal(false);
    setDestinationAccount(null);
  }, []);

  const toggleDestinationModal = useCallback(
    (status: boolean) => () => {
      setShowDestinationModal(status);
    },
    [],
  );

  const handleClose = useCallback(() => {
    handleResetState();
    setTimeout(() => {
      onClose?.();
    }, 0);
  }, [onClose]);

  useEffect(() => {
    if (!visible) {
      handleResetState();
    }
  }, [visible]);

  return (
    <>
      <Modal
        centered
        width={480}
        title={<Title>{t('business.titleMovingAccount')}</Title>}
        open={visible}
        onCancel={handleClose}
        onOk={handleClose}
        footer={null}
        closeIcon={
          <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
        }
      >
        <Body>
          <Row>
            <Label className={`${BodyMdExtend} text-gray-600`}>
              <Trans
                i18nKey='business.selectedAccount'
                values={{ count: multipleAccountSelected.length }}
              />
            </Label>
            <div className='w-full overflow-auto pb-1'>
              <ListAccountWrapper>
                {multipleAccountSelected.map((item, index) => (
                  <AccountItemWrapper key={index}>
                    <div className='flex max-w-[160px] flex-col'>
                      <AccountItemHeader>
                        {item.parent_info?.full_name}
                      </AccountItemHeader>
                      <p className='truncate-1-line w-full whitespace-nowrap text-xs text-grey-600'>
                        {item.name}
                      </p>
                    </div>
                    {multipleAccountSelected.length > 1 && (
                      <RemoveButton
                        onClick={handleRemoveAccount(item?.id?.toString())}
                      >
                        <Icon src={images.Icon.RadiusRemove} />
                      </RemoveButton>
                    )}
                  </AccountItemWrapper>
                ))}
              </ListAccountWrapper>
            </div>
          </Row>
          <Row>
            <Label className={`${BodyMdExtend} ${FontSemibold}`}>
              {t('business.destinationAccount')}
            </Label>
            <AgencyAccountContainer
              className='bg-grey-50 '
              onClick={toggleDestinationModal(true)}
            >
              <Flex
                className='w-full'
                flex={1}
                justify='space-between'
                align='center'
              >
                <SelectionAccount>
                  {destinationAccount?.name ??
                    t('business.selectDestinationAccount')}
                </SelectionAccount>
                <Icon
                  src={images.Icon.CaretDownSm}
                  className={`size-5 rotate-0 transition-transform duration-200 ease-in-out ${
                    showDestinationModal ? '-rotate-180' : ''
                  }`}
                />
              </Flex>
            </AgencyAccountContainer>
          </Row>
          <SubmitButton
            className={`mt-3 ${ButtonSm} ${FontMedium} ${
              isButtonDisable ? 'cursor-not-allowed opacity-50' : ''
            }`}
            type='submit'
            onClick={handleSubmit}
            disabled={isButtonDisable}
          >
            {t('business.transfer')}
          </SubmitButton>
        </Body>
      </Modal>
      {showDestinationModal && (
        <AccountDestinationModal
          expandUserRole={['distributor']}
          open={showDestinationModal}
          selectedId={destinationAccount?.id?.toString() ?? ''}
          onSelectItem={handleSelectDestinationModal}
          onClose={toggleDestinationModal(false)}
        />
      )}
    </>
  );
};
const Title = tw.span`text-xl leading-[28px]`;
const Body = tw.div`flex flex-col gap-3 py-1`;
const Row = tw.div`flex flex-col gap-1`;
const Label = tw.label`block text-left text-black-1000`;
const SubmitButton = tw.button`w-full h-[40px] rounded-lg bg-brand-300 py-2 text-black-1000`;
const AgencyAccountContainer = tw.div`w-full rounded-xl flex flex-row items-center justify-start py-2 px-3 gap-2 text-left text-sm text-text-secondary font-medium cursor-pointer`;
const SelectionAccount = tw.div`text-sm text-grey-600 leading-[24px] overflow-hidden text-ellipsis whitespace-nowrap truncate-1-line`;
const RemoveButton = tw.button`flex size-5 min-h-[20px] min-w-[20px] items-center justify-center rounded-[1000px] bg-red-10 p-[2px]`;
const ListAccountWrapper = tw.div`flex w-fit min-w-full flex-row flex-nowrap gap-3 overflow-auto`;
const AccountItemWrapper = tw.div`flex w-fit flex-auto items-center justify-between gap-3 overflow-auto rounded-2xl border border-grey-100 p-3`;
const AccountItemHeader = tw.p`whitespace-nowrap text-sm font-bold text-black-1000`;

export default MoveAccountModal;
