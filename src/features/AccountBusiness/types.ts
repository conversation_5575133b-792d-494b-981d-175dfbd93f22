export interface BreadCrumbItem {
  label: string;
  level: number;
  role_id: string;
}

export interface Role {
  description: string;
  id: number;
  name: string;
  role_type: string;
}

export interface AccountLog {
  city: string;
  created_at: string;
  id: number;
  identity: string;
  ip: string;
  latitude: number;
  longitude: number;
  region: string | null;
  user_agent: string;
  user_id: number;
}

export interface PageInfo {
  page: number;
  total_pages: number;
  total_count: number;
}

// hooks type

export interface CreateAccountData {
  [key: string]: any;
}

export interface UpdateAccountData {
  id: string | number;
  [key: string]: any;
}

export interface TransferAccountsData {
  userIds: string[];
  parentId: string;
}

export interface ResetPasswordData {
  id: string | number;
  password: string;
}

export interface ExpandAccountResponse {
  users: any[];
  [key: string]: any;
}

export interface AccountsQueryData {
  page: number;
  total_count: number;
  total_pages: number;
  users: any[];
}
