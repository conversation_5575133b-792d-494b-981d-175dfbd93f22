import * as yup from 'yup';

import { EMAIL_REGEX, PHONE_REGEX } from 'utils/regex';

// Base validation schemas
export const baseValidations = {
  username: yup.string().required('<PERSON><PERSON><PERSON> khoản là bắt buộc'),

  password: yup
    .string()
    .required('<PERSON>ật khẩu là bắt buộc')
    .min(6, '<PERSON>ật khẩu phải có ít nhất 6 ký tự'),

  passwordConfirmation: yup
    .string()
    .required('Xác nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], 'Mật khẩu xác nhận không khớp'),

  roleId: yup
    .number()
    .required('Vai trò là bắt buộc')
    .typeError('Vui lòng chọn vai trò'),

  fullName: yup
    .string()
    .required('<PERSON>ọ và tên là bắt buộc')
    .min(2, 'H<PERSON> và tên phải có ít nhất 2 ký tự')
    .max(100, '<PERSON><PERSON> và tên không được quá 100 ký tự'),

  phone: yup
    .string()
    .required('Số điện thoại là bắt buộc')
    .matches(
      PHONE_REGEX,
      'Số điện thoại không đúng định dạng (VD: **********)',
    ),

  email: yup
    .string()
    .required('Email là bắt buộc')
    .matches(EMAIL_REGEX, 'Email không đúng định dạng'),

  address: yup.string().required('Địa chỉ là bắt buộc'),

  activeArea: yup.string().required('Khu vực hoạt động là bắt buộc'),
};

// Create Account Schema (Normal User)
export const createAccountSchema = yup.object({
  username: baseValidations.username,
  password: baseValidations.password,
  password_confirmation: baseValidations.passwordConfirmation,
  role_id: baseValidations.roleId,
  full_name: baseValidations.fullName,
  phone_no: baseValidations.phone,
  email: baseValidations.email,
  address: baseValidations.address,
  active_area: baseValidations.activeArea,
});

// Create Account Schema (Distributor - no password confirmation)
export const createAccountDistributorSchema = yup.object({
  username: baseValidations.username,
  password: baseValidations.password,
  role_id: baseValidations.roleId,
  full_name: baseValidations.fullName,
  phone_no: baseValidations.phone,
  email: baseValidations.email,
  address: baseValidations.address,
  active_area: baseValidations.activeArea,
});

// Update Account Schema
export const updateAccountSchema = yup.object({
  role_id: yup.string().required('Vai trò là bắt buộc'),
  full_name: baseValidations.fullName,
  phone_no: baseValidations.phone,
  email: baseValidations.email,
  address: baseValidations.address,
  active_area: baseValidations.activeArea,
  active: yup.boolean().default(true),
});

// Reset Password Schema
export const resetPasswordSchema = yup.object({
  password: baseValidations.password,
});

// Transfer Accounts Schema
export const transferAccountsSchema = yup.object({
  userIds: yup
    .array()
    .of(yup.string().required())
    .min(1, 'Vui lòng chọn ít nhất một tài khoản để chuyển')
    .required('Vui lòng chọn tài khoản để chuyển'),
  parentId: yup.string().required('Vui lòng chọn tài khoản đích'),
});

// Export types
export type CreateAccountFormData = yup.InferType<typeof createAccountSchema>;
export type CreateAccountDistributorFormData = yup.InferType<
  typeof createAccountDistributorSchema
>;
export type UpdateAccountFormData = yup.InferType<typeof updateAccountSchema>;
export type ResetPasswordFormData = yup.InferType<typeof resetPasswordSchema>;
export type TransferAccountsFormData = yup.InferType<
  typeof transferAccountsSchema
>;
