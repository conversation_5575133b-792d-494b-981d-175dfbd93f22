import { useCallback, useEffect } from 'react';

import { Breadcrumb, Flex } from 'antd';
import { useProfile } from 'hooks';
import { useRole } from 'hooks/useRole';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { UserRole } from 'types/UserRoleTypes';

import images from 'assets/images';
import { Icon, RRInput } from 'components';
import EmptyChoice from 'components/EmptyChoice';
import { CollapseItemData } from 'components/RRCollapseTable/types';
import WithRoleAccess from 'components/WithRoleAccess';

import { adaptCollapseItemData, addUsersToChildren } from './adapters';
import { AccountDetails, AccountList, ActionModals } from './components';
import {
  useAccountData,
  useAccountMutations,
  useAccountState,
  useSearchAccounts,
} from './hooks';
import {
  buildBreadcrumbPath,
  createBreadcrumbFromItem,
  findAccountById,
} from './utils';

const ManageAccountBusiness = () => {
  const { profile } = useProfile();
  const {
    state,
    updateState,
    toggleModal,
    resetSelection,
    changeExpandAccountIds,
  } = useAccountState();

  const { roles } = useRole();
  const {
    accounts: {
      data: profileData,
      status: profileStatus,
      refetch: refetchProfileData,
    } = {
      data: [],
      profileStatus,
    },
  } = useAccountData(profile);

  const searchAccounts = useSearchAccounts(state.searchTerm, profile);
  const mutations = useAccountMutations(profile);

  const searchData = searchAccounts.data || [];
  const accountData = state.accountData || [];

  const handleSelectAccount = useCallback(
    (selectedItem, path) => {
      if (!selectedItem) {
        resetSelection();
        return;
      }

      const currentSelected = state.selectedAccount;
      if (currentSelected?.id === selectedItem?.id) {
        resetSelection();
      } else {
        updateState({
          selectedAccount: selectedItem,
          breadcrumb: adaptCollapseItemData(
            path || buildBreadcrumbPath(selectedItem, accountData),
          ),
        });
      }
    },
    [state.selectedAccount, accountData, resetSelection, updateState],
  );

  const handleSearch = useCallback(
    (value: string) => {
      updateState({ searchTerm: value });
    },
    [updateState],
  );

  const handleToggleMultipleSelect = useCallback(
    (isActive: boolean) => {
      if (isActive) {
        updateState({
          selectedAccount: null,
          multipleSelect: { isActive: true, selectedIds: [] },
        });
      } else {
        updateState({ multipleSelect: { isActive: false, selectedIds: [] } });
      }
    },
    [updateState],
  );

  const handleChangeBreadcrumb = useCallback(
    item => {
      const breadcrumb = createBreadcrumbFromItem(item, accountData);
      updateState({ breadcrumb });
    },
    [accountData, updateState],
  );

  const refreshExpandParentAccount = useCallback(
    (parentId: string) => {
      if (parentId?.toString() === profile?.id?.toString()) {
        refetchProfileData?.();
        return;
      }
      if (parentId) {
        mutations.expandAccount.mutate(
          { id: parentId },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: parentId,
                newUsers: res.users || [],
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
                roles,
              });
              updateState({
                accountData: newAccountData,
              });
            },
          },
        );
      }
    },
    [
      profile,
      state.accountData,
      roles,
      updateState,
      changeExpandAccountIds,
      mutations.expandAccount,
      refetchProfileData,
    ],
  );

  const handleExpandAccount = useCallback(
    (id: string) => {
      const targetAccount = findAccountById(state.accountData, id?.toString());

      if (!targetAccount) {
        return;
      }

      if (targetAccount?.is_end_user) {
        return;
      }

      // collapse
      if (state.expandAccountIds.includes(id)) {
        changeExpandAccountIds(
          state.expandAccountIds.filter(i => i?.toString() !== id?.toString()),
        );
        return;
      }
      // expand
      changeExpandAccountIds([...state.expandAccountIds, id]);

      if (
        targetAccount?.children_count &&
        targetAccount?.children_count > 0 &&
        targetAccount?.children?.length === 0
      ) {
        mutations.expandAccount.mutate(
          { id },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: id,
                newUsers: res.users || [],
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
                roles,
              });

              //

              updateState({
                accountData: newAccountData,
              });
            },
            onError: () => {
              changeExpandAccountIds(
                state.expandAccountIds.filter(i => i !== id),
              );
            },
          },
        );
      }
    },
    [
      findAccountById,
      mutations.expandAccount,
      state.expandAccountIds,
      state.accountData,
      roles,
      changeExpandAccountIds,
      updateState,
    ],
  );

  const handleLoadMore = useCallback(
    (data: CollapseItemData) => {
      const {
        id,
        pagination: { total_pages, page } = {
          total_pages: 1,
          page: 1,
        },
      } = data || {};
      if (total_pages && page && total_pages > page) {
        mutations.expandAccount.mutate(
          {
            id,
            page: page + 1,
          },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: data.id,
                newUsers: res.users || [],
                roles,
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
              });

              updateState({
                accountData: newAccountData,
              });
            },
          },
        );
      }
    },
    [mutations.expandAccount, state.accountData, roles, updateState],
  );

  const handleSelectedMultipleIds = useCallback(
    (ids: string[]) => {
      updateState({
        multipleSelect: {
          ...state.multipleSelect,
          selectedIds: ids,
        },
      });
    },
    [updateState, state.multipleSelect],
  );

  useEffect(() => {
    if (profileData && profileData.length > 0 && profileStatus === 'success') {
      // Ensure profileData has the required key property for CollapseItemData
      const transformedData: CollapseItemData[] = profileData.map(item => ({
        ...item,
        key: item.key || item.id?.toString() || 'unknown',
      }));

      const firstItem = transformedData[0];
      updateState({
        accountData: transformedData,
        expandAccountIds:
          firstItem?.children_count &&
          firstItem.children_count > 0 &&
          firstItem?.id != null
            ? [firstItem.id]
            : [],
      });
    }
  }, [profileStatus, profileData]);

  // select first item
  useEffect(() => {
    if (accountData.length > 0 && !state.selectedAccount) {
      handleSelectAccount(accountData[0], [accountData[0]]);
    }
  }, [accountData]);

  return (
    <Container>
      <MainContainer vertical className='w-[400px] min-w-[400px]'>
        <Header>
          <Breadcrumb separator={<Icon src={images.Icon.ChevronRightGrey} />}>
            <Breadcrumb.Item className='text-grey-600'>
              {t('breadcrumb.business')}
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <div className='font-semibold'>
                {t('breadcrumb.accountManagement')}
              </div>
            </Breadcrumb.Item>
          </Breadcrumb>
        </Header>
        <AccountContainer className='flex flex-col'>
          <RRInput
            type='text'
            value={state.searchTerm}
            prefixIcon={images.Icon.SearchLoupe}
            className='mb-3 w-full'
            placeholder={t('map.filterPlaceholder')}
            showClearButton={true}
            onChange={e => handleSearch(e.target.value)}
            onClear={() => handleSearch('')}
          />
          <div className='flex-1'>
            <AccountList
              enableMultipleSelect={true}
              expandAccountIds={state.expandAccountIds}
              searchTerm={state.searchTerm}
              searchData={searchData}
              accountData={accountData}
              selectedAccount={state.selectedAccount}
              multipleSelect={state.multipleSelect}
              onSelectAccount={handleSelectAccount}
              onToggleMultipleSelect={handleToggleMultipleSelect}
              onExpandAccount={handleExpandAccount}
              onChangeBreadcrumb={handleChangeBreadcrumb}
              onOpenMoveModal={() => toggleModal('moveAccount', true)}
              onOpenAddAccountModal={data =>
                toggleModal('addAccount', true, data)
              }
              onSelectedMultipleIds={handleSelectedMultipleIds}
              onLoadMore={handleLoadMore}
            />
          </div>
        </AccountContainer>
      </MainContainer>
      <RightContainer>
        {state.selectedAccount ? (
          <AccountDetails
            mutations={mutations}
            modals={state.modals}
            selectedAccount={state.selectedAccount}
            breadcrumb={state.breadcrumb}
            updateState={updateState}
            onDeselectAccount={resetSelection}
            onToggleModal={toggleModal}
            onChangeBreadcrumb={handleChangeBreadcrumb}
            refreshExpandParentAccount={refreshExpandParentAccount}
          />
        ) : (
          <EmptyChoice content='Chọn tài khoản để xem thông tin' />
        )}
      </RightContainer>

      <ActionModals
        accountData={accountData}
        multipleSelect={state.multipleSelect}
        mutations={mutations}
        modals={state.modals}
        modalProps={state.modalProps}
        selectedAccount={state.selectedAccount}
        onToggleModal={toggleModal}
        updateState={updateState}
        refreshExpandParentAccount={refreshExpandParentAccount}
        onDeselectAccount={resetSelection}
      />
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 w-[70%] p-6`;
const MainContainer = tw(Flex)`p-6 grow h-screen`;
const Header = tw.div`flex justify-between mb-6`;
const AccountContainer = tw.div`bg-white-1000 border-grey-100 h-full`;

const BusinessAgency = WithRoleAccess(ManageAccountBusiness, [UserRole.Agency]);
export default BusinessAgency;
