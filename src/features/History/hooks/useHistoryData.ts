import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { t } from 'i18next';
import { qsStringifyUrl } from 'services/api/utils';
import { createApiMutationFn, createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';
import { formatDateStr } from 'utils/date';

import showToast from 'components/RRToastMessage/Toast';

// Types
interface PageInfo {
  page: number;
  totalPages: number;
  totalCount: number;
}

interface UserSession {
  id: string;
  ip: string;
  user_agent: string;
  browser: string;
  platform: string;
  logged_in_at: string;
  logged_out_at?: string;
  is_active: boolean;
}

interface LoginActivitiesResponse {
  user_sessions: UserSession[];
  page: number;
  total_count: number;
  total_pages: number;
}

/**
 * Hook to fetch login activities (historical sessions)
 */
export const useLoginActivities = ({
  userId,
  page = 1,
  pageSize = 10,
  isActive = false,
}) => {
  return useQuery({
    queryKey: ['history', 'login-activities', userId, page, pageSize, isActive],
    queryFn: createApiQueryFn<LoginActivitiesResponse>({
      method: 'GET',
      route: qsStringifyUrl({
        url: `/users/user-sessions`,
        query: {
          page: page.toString(),
          per_page: pageSize.toString(),
          is_active: isActive.toString(), // Historical/logged out sessions
        },
      }),
    }),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    select: data => ({
      activities:
        data.user_sessions?.map(session => ({
          ...session,
          key: session.id,
          type: `${session.browser} - ${session.platform}`,
          ipAddress: session.ip,
          address: `${session.browser}, ${session.platform}`,
          loggedInAt: formatDateStr(session.logged_in_at),
          loggedoutAt: session.logged_out_at
            ? formatDateStr(session.logged_out_at)
            : '-',
        })) || [],
      pageInfo: {
        page: data.page,
        totalPages: data.total_pages,
        totalCount: data.total_count,
      } as PageInfo,
    }),
  });
};

/**
 * Hook to fetch user sessions for a specific account (for AccountLogModal)
 */
export const useAccountSessions = (
  accountId: string,
  isActive: boolean = false,
  page: number = 1,
  pageSize: number = 20,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: ['account-sessions', accountId, isActive, page, pageSize],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: `/users/${accountId}/user-sessions`,
        query: {
          is_active: isActive.toString(),
          page: page.toString(),
          per_page: pageSize.toString(),
        },
      }),
    }),
    enabled: !!accountId && enabled,
    staleTime: 30 * 1000, // 30 seconds for active sessions, 5 minutes for inactive
    select: (data: any) => {
      const sessions = data.user_sessions || [];
      return {
        data:
          sessions.map(item => ({
            ...item,
            key: item.id,
            type: `${item.browser} - ${item.platform}`,
            ipAddress: item.ip,
            address: `${item.browser}, ${item.platform}`,
            loggedInAt: formatDateStr(item.logged_in_at),
            loggedoutAt: item.logged_out_at
              ? formatDateStr(item.logged_out_at)
              : '-',
          })) || [],
        pagination: {
          page: data.page,
          total_count: data.total_count,
          total_pages: data.total_pages,
        },
      };
    },
  });
};

export const useBlockIpAddress = () => {
  return useQuery({
    queryKey: ['blocked-ip-addresses'],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: '/users/current-user/blocked-ip-addresses',
    }),
    enabled: true,
    staleTime: 30 * 1000, // 30 seconds
    select: (data: any) => {
      return data.blocked_ip_addresses || [];
    },
  });
};

/**
 * Mutation to sign out all sessions for a user
 */
export const useSignOutAllSessions = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createApiMutationFn<void, { accountId: string }>(
      ({ accountId }) => ({
        method: 'POST',
        route: `/users/${accountId}/sign_out_all_sessions`,
      }),
    ),
    onSuccess: (_, { accountId }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: ['account-sessions', accountId],
      });
      queryClient.invalidateQueries({
        queryKey: ['history', 'current-session'],
      });
      queryClient.invalidateQueries({
        queryKey: ['history', 'login-activities'],
      });

      showToast('success', 'Đăng xuất tất cả thiết bị thành công');
    },
  });
};

/**
 * Mutation to logout a specific device/session
 */
export const useLogoutDevice = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createApiMutationFn<void, { sessionId: string }>(
      ({ sessionId }) => ({
        method: 'POST',
        route: `/users/sessions/${sessionId}/logout`,
      }),
    ),
    onSuccess: () => {
      // Invalidate all session queries
      queryClient.invalidateQueries({
        queryKey: ['history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['account-sessions'],
      });

      showToast('success', 'Đăng xuất thiết bị thành công');
    },
  });
};

export const useBlockIP = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createApiMutationFn<void, { ip: string }>(({ ip }) => ({
      method: 'POST',
      route: `/users/current-user/blocked-ip-addresses/${ip}/block`,
    })),
    onSuccess: () => {
      // Invalidate blocked IP queries
      queryClient.invalidateQueries({
        queryKey: ['blocked-ip-addresses'],
      });

      showToast('success', 'Chặn IP thành công');
    },
  });
};

export const useUnblockIP = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createApiMutationFn<void, { ip: string }>(({ ip }) => ({
      method: 'POST',
      route: `/users/current-user/blocked-ip-addresses/${ip}/unblock`,
    })),
    onSuccess: () => {
      // Invalidate blocked IP queries
      queryClient.invalidateQueries({
        queryKey: ['blocked-ip-addresses'],
      });

      showToast('success', 'Bỏ chặn IP thành công');
    },
  });
};
