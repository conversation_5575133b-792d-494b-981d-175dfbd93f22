import { useCallback, useEffect, useRef, useState } from 'react';

import { Popover, Tooltip } from 'antd';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdBase, Colors } from 'assets/styles';
import { HeaderSection, Icon, RRConfirmationModal } from 'components';
import { LinkButton } from 'components/Button';
import EmptyPanel from 'components/EmptyPanel';
import RRTable from 'components/RRTable';
import Spinner from 'components/Spinner';

import {
  useBlockIP,
  useBlockIpAddress,
  useLoginActivities,
  useLogoutDevice,
  useSignOutAllSessions,
  useUnblockIP,
} from './hooks';

const renderTooltip = (text: string): JSX.Element => {
  if (text === '-') return <div>{text}</div>;
  return (
    <Tooltip
      title={text}
      color={'white'}
      overlayInnerStyle={{
        backgroundColor: Colors.white[1000],
        color: Colors.black[1000],
      }}
    >
      <div className='truncate-1-line'>{text}</div>
    </Tooltip>
  );
};

interface HistoryProps {}

const History = ({}: HistoryProps) => {
  const { profile } = useProfile();

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [showConfirmLogOutAll, setShowConfirmLogOutAll] = useState(false);
  const [showConfirmUnlockIp, setShowConfirmUnlockIp] = useState('');

  // React Query hooks
  const { data: loginActivitiesData, isLoading: loginActivitiesLoading } =
    useLoginActivities({
      userId: profile?.id?.toString() || '',
      page: 1,
      pageSize: 20,
      isActive: true,
    });

  const { data: logoutActivitiesData, isLoading: logoutActivitiesLoading } =
    useLoginActivities({
      userId: profile?.id?.toString() || '',
      page: currentPage,
      pageSize,
      isActive: false,
    });

  const { data: blockedIpData, isLoading: blockedIpLoading } =
    useBlockIpAddress();

  const signOutAllMutation = useSignOutAllSessions();
  const logoutDeviceMutation = useLogoutDevice();
  const blockIPMutation = useBlockIP();
  const unblockIPMutation = useUnblockIP();

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const onPageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // Table columns with actions
  const getColumns = useCallback(
    (isLoggedIn: boolean) => [
      {
        title: t('history.type'),
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: t('history.ipAddress'),
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        render: (text: string, record: any) => (
          <Popover
            content={
              <div>
                <Action
                  onClick={() => {
                    blockIPMutation.mutate({ ip: text });
                  }}
                >
                  {t('history.block')}
                </Action>
                {isLoggedIn && (
                  <Action
                    onClick={() => {
                      logoutDeviceMutation.mutate({ sessionId: record.id });
                    }}
                  >
                    {t('history.logoutDevice')}
                  </Action>
                )}
              </div>
            }
            trigger='click'
            placement='bottom'
          >
            <IpAddress>{text}</IpAddress>
          </Popover>
        ),
      },
      {
        title: t('history.address'),
        dataIndex: 'address',
        key: 'address',
        render: renderTooltip,
      },
      {
        title: 'Thời gian đăng nhập',
        dataIndex: 'loggedInAt',
        key: 'loggedInAt',
        render: renderTooltip,
      },
      {
        title: 'Thời gian đăng xuất',
        dataIndex: 'loggedoutAt',
        key: 'loggedoutAt',
        render: renderTooltip,
      },
    ],
    [],
  );

  return (
    <>
      <Container>
        <MainContainer>
          <div className='flex-2 flex w-[70%] flex-col gap-6 border-r border-grey-100 p-6'>
            <HeaderSection content='Lịch sử đăng nhập' />
            <div className='h-full'>
              <div className='h-1/3'>
                <TableHeader>
                  <GrayText className={`${BodyMdBase}`}>
                    {t('history.deviceLogged')}
                  </GrayText>
                  <LinkButton
                    onClick={() => setShowConfirmLogOutAll(true)}
                    size='small'
                    className='h-6 text-end'
                    disabled={signOutAllMutation.isPending}
                    loading={signOutAllMutation.isPending}
                  >
                    {t('history.logoutAll')}
                  </LinkButton>
                </TableHeader>
                <div className='h-[calc(100%-36px)]'>
                  <RRTable
                    headerRows={1}
                    columns={getColumns(true)}
                    data={loginActivitiesData?.activities || []}
                    total={loginActivitiesData?.pageInfo.totalCount || 0}
                    currentPage={1}
                    pageSize={20}
                    loading={loginActivitiesLoading}
                    hidePagination
                  />
                </div>
              </div>
              <div className='relative mt-2 h-2/3'>
                <TableHeader>
                  <GrayText className={`${BodyMdBase}`}>
                    {t('history.deviceLogOut')}
                  </GrayText>
                </TableHeader>
                <div className='h-[calc(100%-36px)]'>
                  <RRTable
                    columns={getColumns(false)}
                    data={logoutActivitiesData?.activities || []}
                    total={logoutActivitiesData?.pageInfo.totalCount || 0}
                    currentPage={currentPage}
                    pageSize={pageSize}
                    loading={logoutActivitiesLoading}
                    onPageChange={handlePageChange}
                    onPageSizeChange={onPageSizeChange}
                  />
                </div>
              </div>
            </div>
          </div>

          <RightContainer className='gap-3'>
            <LeftHeader>{t('history.leftHeader')}</LeftHeader>
            <LeftContent>
              <LeftRowContainer>
                <DesktopIcon>
                  <Icon src={images.Icon.Desktop} />
                </DesktopIcon>
                <div className='flex-1 flex-col'>
                  <BrowserInfoLabel>Chrome - Windows</BrowserInfoLabel>
                  <div className='flex items-center gap-2'>
                    <Icon src={images.Icon.ArrowLocation} />
                    <SubText>**************</SubText>
                  </div>
                </div>
              </LeftRowContainer>

              <LeftRowContainer>
                <Icon src={images.Icon.PinLocation} />
                <ColumnGap2>
                  <SubText>{t('history.address')}</SubText>
                  <SubText className='text-black'>
                    Hồ Chí Minh, Việt Nam
                  </SubText>
                </ColumnGap2>
              </LeftRowContainer>
              <Divider />
              <LeftRowContainer>
                <Icon src={images.Icon.ClockWatch} />
                <ColumnGap2>
                  <SubText>{t('history.loginTime')}</SubText>
                  <SubText className='text-black'>31/07/2024 9:41</SubText>
                </ColumnGap2>
              </LeftRowContainer>
              <Divider />
              <LeftRowContainer>
                <Icon src={images.Icon.ClockWatch} />
                <ColumnGap2>
                  <SubText>{t('history.lastLogin')}</SubText>
                  <SubText className='text-black'>31/07/2024 9:41</SubText>
                </ColumnGap2>
              </LeftRowContainer>
            </LeftContent>
            {/* {currentSessionLoading ? (
            <div className='flex justify-center py-8'>
              Loading current session...
            </div>
          ) : currentSession ? (

          ) : (
            <LeftContent>
              <SubText>Không có phiên đăng nhập nào đang hoạt động</SubText>
            </LeftContent>
          )} */}

            <div className='text-sm font-medium leading-normal text-grey-400'>
              {t('history.blockManagementTitle')}
            </div>
            {blockedIpLoading ? (
              <div className='flex justify-center py-8'>
                <Spinner />
              </div>
            ) : (
              <>
                {blockedIpData?.length === 0 && (
                  <div className='flex justify-center py-8'>
                    <EmptyPanel />
                  </div>
                )}
                {(blockedIpData || [])?.map((item, index) => (
                  <div key={index}>
                    <div className='mb-3 flex flex-row gap-3'>
                      <ColumnGap2>
                        <div className='text-black text-sm font-bold leading-normal'>
                          {item?.platform}
                        </div>
                        <FullRowGap>
                          <Icon src={images.Icon.ArrowLocation} />
                          <SubText>{t('history.IPAddress')}</SubText>
                          <SubText className='text-black'>{item?.ip}</SubText>
                        </FullRowGap>
                      </ColumnGap2>

                      <LinkButton
                        onClick={() => setShowConfirmUnlockIp(item?.ip)}
                        size='small'
                        className='mb-3 mt-2 h-6 text-end'
                      >
                        {t('history.unlockButton')}
                      </LinkButton>
                    </div>
                    <Divider />
                  </div>
                ))}
              </>
            )}
          </RightContainer>
        </MainContainer>
      </Container>
      <RRConfirmationModal
        title={t('history.logouttitle')}
        message={t('history.logoutContent')}
        onCancel={() => setShowConfirmLogOutAll(false)}
        onConfirm={() => {
          signOutAllMutation.mutate(
            {
              accountId: profile?.id?.toString() || '',
            },
            {
              onSuccess: () => {
                setShowConfirmLogOutAll(false);
              },
            },
          );
        }}
        visible={showConfirmLogOutAll}
        isSubmitting={signOutAllMutation.isPending}
      />
      <RRConfirmationModal
        title='Bỏ chặn thiết bị?'
        message='Thiết bị này có thể truy cập lại vào tài khoản của bạn sau khi thao tác bỏ chặn'
        onCancel={() => setShowConfirmUnlockIp('')}
        onConfirm={() => {
          unblockIPMutation.mutate(
            {
              ip: showConfirmUnlockIp,
            },
            {
              onSuccess: () => {
                setShowConfirmUnlockIp('');
              },
            },
          );
        }}
        visible={!!showConfirmUnlockIp}
        isSubmitting={unblockIPMutation.isPending}
      />
    </>
  );
};

const Container = tw.div`flex h-full flex-col`;
const RightContainer = tw.div`flex flex-col flex-1 p-6`;
const MainContainer = tw.div`flex flex-1`;
const LeftHeader = tw.span`text-lg font-bold`;
const SubText = tw.div`text-sm font-normal leading-normal text-grey-600`;
const ColumnGap2 = tw.div`inline-flex flex-1 flex-col gap-1`;
const Divider = tw.div`flex h-px bg-grey-100`;
const FullRowGap = tw.div`inline-flex items-center justify-start gap-1 self-stretch`;
const TableHeader = tw.div`flex justify-between items-center mb-3`;
const GrayText = tw.span`text-grey-400`;
const IpAddress = tw.span`text-blue-200 cursor-pointer underline`;
const Action = tw.div`min-w-[168px] h-[40px] cursor-pointer content-center rounded p-2  hover:bg-grey-50`;
const LeftContent = tw.div`flex flex-col gap-3 rounded-2xl border  border-grey-100 p-4 mb-3`;

const LeftRowContainer = tw.div`flex flex-row gap-3`;
const BrowserInfoLabel = tw.div`text-black font-['Inter'] text-base font-bold leading-normal`;
const DesktopIcon = tw.div`rounded-lg border border-grey-100 p-3 size-12`;

export default History;
