import { useCallback, useState } from 'react';

import { Modal, Popover, Tooltip } from 'antd';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdBase, Colors } from 'assets/styles';
import { Icon, RRConfirmationModal } from 'components';
import { LinkButton } from 'components/Button';
import RRTable from 'components/RRTable';

import {
  useAccountSessions,
  useBlockIP,
  useLogoutDevice,
  useSignOutAllSessions,
} from '../hooks';

const renderTooltip = (text: string): JSX.Element => {
  if (text === '-') return <div>{text}</div>;
  return (
    <Tooltip
      title={text}
      color={'white'}
      overlayInnerStyle={{
        backgroundColor: Colors.white[1000],
        color: Colors.black[1000],
      }}
    >
      <div className='truncate-1-line'>{text}</div>
    </Tooltip>
  );
};

interface AccountLogModalProps {
  accountId: string;
  visible: boolean;
  onClose: () => void;
}

const AccountLogModal = ({
  accountId,
  visible,
  onClose,
}: AccountLogModalProps) => {
  const [pageSize, setPageSize] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [showConfirm, setShowConfirm] = useState(false);

  // Active sessions (logged in)
  const {
    data: { data: logsIn = [] } = { data: [] },
    isLoading: logsInLoading,
  } = useAccountSessions(accountId, true, 1, 20, visible);

  // Inactive sessions (logged out) with pagination
  const {
    data: {
      data: logsOut = [],
      pagination = { page: 1, total_count: 0, total_pages: 1 },
    } = {
      data: [],
      pagination: { page: 1, total_count: 0, total_pages: 1 },
    },
    isLoading: logsOutLoading,
  } = useAccountSessions(accountId, false, currentPage, pageSize, visible);

  const signOutMutation = useSignOutAllSessions();
  const logoutDeviceMutation = useLogoutDevice();
  const blockIPMutation = useBlockIP();

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const onPageSizeChange = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  }, []);

  const logOutAll = useCallback(() => {
    signOutMutation.mutate(
      { accountId },
      {
        onSuccess: () => {
          setShowConfirm(false);
          onClose();
        },
      },
    );
  }, [accountId, signOutMutation, onClose]);

  // Table columns with actions
  const getColumns = useCallback(
    (isLoggedIn: boolean) => [
      {
        title: t('history.type'),
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: t('history.ipAddress'),
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        render: (text: string, record: any) => (
          <Popover
            content={
              <div>
                <Action
                  onClick={() => {
                    blockIPMutation.mutate({ ip: text });
                  }}
                >
                  {t('history.block')}
                </Action>
                {isLoggedIn && (
                  <Action
                    onClick={() => {
                      logoutDeviceMutation.mutate({ sessionId: record.id });
                    }}
                  >
                    {t('history.logoutDevice')}
                  </Action>
                )}
              </div>
            }
            trigger='click'
            placement='bottom'
          >
            <IpAddress>{text}</IpAddress>
          </Popover>
        ),
      },
      {
        title: t('history.address'),
        dataIndex: 'address',
        key: 'address',
        render: renderTooltip,
      },
      {
        title: 'Thời gian đăng nhập',
        dataIndex: 'loggedInAt',
        key: 'loggedInAt',
        render: renderTooltip,
      },
      {
        title: 'Thời gian đăng xuất',
        dataIndex: 'loggedoutAt',
        key: 'loggedoutAt',
        render: renderTooltip,
      },
    ],
    [],
  );

  return (
    <>
      <Modal
        className='login-history-modal'
        centered
        title={t('history.modalTitle')}
        open={visible}
        onOk={onClose}
        onCancel={onClose}
        width={920}
        footer={null}
        zIndex={1000}
        closeIcon={
          <Icon className='size-8 rounded-full p-1' src={images.Icon.X} />
        }
      >
        <Container className='h-full'>
          <div className='flex h-full flex-col gap-3'>
            <div className='h-1/3'>
              <TableHeader>
                <GrayText className={`${BodyMdBase}`}>
                  {t('history.deviceLogged')}
                </GrayText>
                <LinkButton
                  onClick={() => setShowConfirm(true)}
                  size='small'
                  className='h-6 text-end'
                  disabled={signOutMutation.isPending}
                >
                  {signOutMutation.isPending
                    ? 'Đang xử lý...'
                    : t('history.logoutAll')}
                </LinkButton>
              </TableHeader>
              <div className='h-[calc(100%-36px)]'>
                <RRTable
                  hidePagination
                  columns={getColumns(true)}
                  data={logsIn}
                  pageSize={20}
                  currentPage={1}
                  total={logsIn.length}
                  loading={logsInLoading}
                />
              </div>
            </div>

            <div className='h-2/3'>
              <TableHeader>
                <GrayText className={`${BodyMdBase}`}>
                  {t('history.deviceLogOut')}
                </GrayText>
              </TableHeader>
              <div className='h-[calc(100%-36px)]'>
                <RRTable
                  columns={getColumns(false)}
                  data={logsOut}
                  total={pagination.total_count}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  loading={logsOutLoading}
                  onPageChange={handlePageChange}
                  onPageSizeChange={onPageSizeChange}
                />
              </div>
            </div>
          </div>
        </Container>
      </Modal>

      <RRConfirmationModal
        title={t('history.logouttitle')}
        message={t('history.logoutContent')}
        onCancel={() => setShowConfirm(false)}
        onConfirm={logOutAll}
        visible={showConfirm}
        isSubmitting={signOutMutation.isPending}
      />
    </>
  );
};

const Container = tw.div`flex h-full flex-col max-h-[90vh]`;
const TableHeader = tw.div`flex justify-between items-center my-3`;
const GrayText = tw.span`text-grey-400`;
const IpAddress = tw.span`text-blue-200 cursor-pointer underline`;
const Action = tw.div`min-w-[168px] h-[40px] cursor-pointer content-center rounded p-2  hover:bg-grey-50`;

export default AccountLogModal;
