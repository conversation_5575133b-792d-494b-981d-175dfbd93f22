import * as yup from 'yup';

export const yupResolver = (schema: yup.Schema) => async (values: any) => {
  try {
    const validatedValues = await schema.validate(values, {
      abortEarly: false,
    });
    return {
      values: validatedValues,
      errors: {},
    };
  } catch (errors) {
    const formErrors: any = {};

    if (errors instanceof yup.ValidationError) {
      errors.inner.forEach(error => {
        if (error.path) {
          formErrors[error.path] = {
            type: 'validation',
            message: error.message,
          };
        }
      });
    }

    return {
      values: {},
      errors: formErrors,
    };
  }
};

export const getErrorMessage = (error: any): string | undefined => {
  if (!error) return undefined;
  if (typeof error === 'string') return error;
  if (error && typeof error === 'object' && error.message) {
    return String(error.message);
  }
  return undefined;
};

export default yupResolver;
