import { useQuery } from '@tanstack/react-query';

import type { AccountRole } from 'features/Profile/types';
import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

export interface UseRoleReturn {
  roles: AccountRole[];
  isLoading: boolean;
  isFetching: boolean;
  error: string | null;
  refetch: () => void;
}

export const useRole = (): UseRoleReturn => {
  const query = useQuery({
    queryKey: queryKeys.roles.all,
    queryFn: createApiQueryFn<{ roles: AccountRole[] }>({
      method: 'GET',
      route: '/roles',
    }),
    staleTime: 30 * 60 * 1000,
    select: data => {
      const rolesData = Array.isArray(data) ? data : data.roles || [];

      return rolesData.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description || '',
        role_type: role.role_type || role.role || '',
      }));
    },
  });

  return {
    roles: query.data || [],
    isLoading: query.isLoading,
    error: query.error?.message || null,
    refetch: query.refetch,
    isFetching: query.isFetching,
  };
};

export default useRole;
