import { useEffect, useMemo } from 'react';

import { useQuery } from '@tanstack/react-query';
import useRole from 'hooks/useRole';

// Thay thế Redux import bằng React Query hooks
import { useLoginData } from 'features/Login/hooks/useLoginData';
import { useUpdateProfileMutation } from 'features/Login/hooks/useLoginMutations';
import { Account } from 'features/Profile/types';
import { USER_ID_KEY, getCookie } from 'services/cookies';
import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';
import { addIsRoleType } from 'utils/role';

export interface UseProfileReturn {
  profile: Account | null;
  isLoading: boolean;
  isFetching: boolean;
  error: string | null;
  refetch: () => void;
}

export const useProfile = (userId?: string): UseProfileReturn => {
  // Sử dụng React Query hooks thay vì Redux
  const { profile: profileFromStore } = useLoginData();
  const updateProfileMutation = useUpdateProfileMutation();

  const userIdFromCookie = getCookie(USER_ID_KEY);
  const { roles } = useRole();

  // Determine target user ID with fallback logic
  const targetUserId = useMemo(() => {
    if (userId) return userId;
    if (profileFromStore?.id) return profileFromStore.id.toString();
    if (userIdFromCookie) return userIdFromCookie;
    return '';
  }, [userId, profileFromStore?.id, userIdFromCookie]);

  const isCurrentUser = !userId;
  const shouldUseStore = isCurrentUser && !!profileFromStore;
  const shouldCallApi = !!targetUserId && !shouldUseStore;

  // API Query
  const apiQuery = useQuery({
    queryKey: queryKeys.users.detail(targetUserId),
    queryFn: createApiQueryFn({
      method: 'GET',
      route: `/users/${targetUserId}`,
    }),
    enabled: shouldCallApi,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry for 4xx errors
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Update store when API data is available for current user
  useEffect(() => {
    if (isCurrentUser && apiQuery.data && !profileFromStore) {
      const userData = apiQuery.data?.user || apiQuery.data;
      if (userData) {
        const profileWithRole = {
          ...userData,
          ...addIsRoleType(userData, roles),
        } as Account;
        // Sử dụng React Query mutation thay vì Redux dispatch
        updateProfileMutation(profileWithRole);
      }
    }
  }, [
    isCurrentUser,
    apiQuery.data,
    profileFromStore,
    updateProfileMutation,
    roles,
  ]);

  // Memoize final result
  const result = useMemo((): Omit<UseProfileReturn, 'isRole'> => {
    // Case 1: Use store data for current user
    if (shouldUseStore && profileFromStore) {
      return {
        profile: {
          ...profileFromStore,
          ...addIsRoleType(profileFromStore, roles),
        } as Account,
        isLoading: false,
        error: null,
        isFetching: false,
        refetch: () => {},
      };
    }

    // Case 2: Use API data
    if (shouldCallApi) {
      const userData = apiQuery.data?.user || apiQuery.data || null;
      return {
        profile: {
          ...userData,
          ...addIsRoleType(userData, roles),
        },
        isLoading: apiQuery.isLoading,
        error: apiQuery.error?.message || null,
        isFetching: apiQuery.isFetching,
        refetch: apiQuery.refetch,
      };
    }

    // Case 3: No data available
    return {
      profile: null,
      isLoading: false,
      isFetching: false,
      error: !targetUserId ? 'No user ID available' : null,
      refetch: () => {},
    };
  }, [
    shouldUseStore,
    profileFromStore,
    shouldCallApi,
    apiQuery.data,
    apiQuery.isLoading,
    apiQuery.error,
    apiQuery.isFetching,
    apiQuery.refetch,
    targetUserId,
    roles,
  ]);

  return {
    ...result,
  };
};

export default useProfile;
