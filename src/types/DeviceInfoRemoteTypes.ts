export type DeviceRemote = {
  device_ccid?: string;
  device_name?: string;
  device_plate_number?: string;
  gps?: GpsDataRemote;
  distance?: number;
  half_of_course?: number;
  latitude?: number;
  locating_information?: string;
  longitude?: number;
  satellites?: number;
  speed?: number;
  timestamp?: number;
  imei?: string;
  inactive_duration?: number;
  status?: string;
  device_category?: string;
};

export type GpsDataRemote = {
  timestamp?: number;
  latitude?: number;
  longitude?: number;
  locating_information?: string;
  satellites?: number;
  speed?: number;
  distance?: number;
  half_of_course?: number;
};

export type Device = {
  deviceCcid?: string;
  deviceName?: string;
  devicePlateNumber?: string;
  gps: {
    timestamp?: number;
    latitude?: number;
    longitude?: number;
    locatingInformation?: string;
    satellites?: number;
    speed?: number;
    distance?: number;
    halfOfCourse?: number;
  };
  distance?: number;
  halfOfCourse?: number;
  latitude?: number;
  locatingInformation?: string;
  longitude?: number;
  satellites?: number;
  speed?: number;
  timestamp?: number;
  imei?: string;
  inactiveDuration?: number;
  status?: string;
  deviceCategory?: string;
};

export type DriverRemote = {
  timestamp?: number;
  order_of_driver?: number;
  driver_tag?: string;
  driver_license_and_name?: string;
};

export type OperationDataRemote = {
  timestamp?: number;
  driver_index?: number;
  driver_tag?: string;
  over_4h_count?: number;
  time_of_continuous_driving?: number;
  driving_time_per_day?: number;
};

export type SpeedDataRemote = {
  timestamp?: number;
  half_of_course?: number;
  instant_speed?: number;
  historical_speed?: number[];
};

export type DeviceStatusRemote = {
  timestamp?: number;
  battery_power_in_voltage?: number;
  main_power_in_voltage?: number;
  open_door_count?: number;
  over_speed_count?: number;
  statistic_state?: StatisticStateRemote;
};

export type StatisticStateRemote = {
  acc?: number;
  door?: number;
  ben?: number;
  air?: number;
  engine?: number;
  power?: number;
  buzzer?: number;
  sos?: number;
};
