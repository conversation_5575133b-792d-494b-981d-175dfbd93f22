import { DeviceStatus } from 'constants/device';

export interface DeviceType {
  imei: string;
  device_name: string;
  device_plate_number: string;
  device_category: string;
  status: DeviceStatus;
  owner_name: string;
  transport_ownership: string;
  device_ccid: string;
  device_sim_number: string;
  service_package_id: string;
  activated_at: string;
  expired_at: string;
  is_allow_data_transport: boolean;
  gps: any;
  max_allowable_speed: number;
  max_allowable_ignition_swich_time: number;
  driver: any;
  operation: any;
  firmware: any;
  transport_type: string;
  device_pin: string;
  main_power_in_voltage: string;
  engine: any;
  gprs_interval: number;
  min_speed: number;
  stop_distance: number;
  timezone: string;
  sensor_type: string;
  stop_time: number;
  [key: string]: any;
}
