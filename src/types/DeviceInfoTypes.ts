import { CamelCasedPropertiesDeep, OverrideProperties } from 'type-fest';
import {
  DeviceRemote,
  DeviceStatusRemote,
  DriverRemote,
  GpsDataRemote,
  OperationDataRemote,
  SpeedDataRemote,
  StatisticStateRemote,
} from './DeviceInfoRemoteTypes';

export type Device = CamelCasedPropertiesDeep<DeviceRemote> & {
  driver?: Driver;
  operation?: OperationData;
  gps?: GpsData;
  status?: DeviceStatus;
  speed?: SpeedData;
};

export type Driver = CamelCasedPropertiesDeep<DriverRemote>;
export type GpsData = CamelCasedPropertiesDeep<GpsDataRemote>;

export type OperationData = CamelCasedPropertiesDeep<OperationDataRemote>;

export type SpeedData = CamelCasedPropertiesDeep<SpeedDataRemote>;

export type DeviceStatus = OverrideProperties<
  CamelCasedPropertiesDeep<DeviceStatusRemote>,
  {
    statisticState?: StatisticState;
  }
>;

export type StatisticState = CamelCasedPropertiesDeep<StatisticStateRemote>;
