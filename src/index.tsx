import { QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, Rate } from 'antd';
import vi from 'antd/locale/vi_VN';
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import isoWeek from 'dayjs/plugin/isoWeek';
import dayLocaleData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { createRoot } from 'react-dom/client';
import tw from 'tailwind-styled-components';
import 'tailwindcss/tailwind.css';

import App from 'features/App';
import { queryClient } from 'services/reactQuery';

import images from 'assets/images';
import {
  BodyLg,
  BodyMdBase,
  ButtonSm,
  FontBold,
  FontMedium,
  antdThemeConfig,
} from 'assets/styles';
import { Icon } from 'components';
import QueryErrorBoundary from 'components/ErrorBoundary/QueryErrorBoundary';

import './global.css';
import './i18n';
import './override.css';

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);

dayjs.extend(isoWeek);
dayjs.extend(dayLocaleData);
dayjs.locale('vi');

const container = document.getElementById('root') as HTMLDivElement;
const root = createRoot(container);

const AppName = tw.div`items-center w-full border border-grey-100 rounded-2xl p-4 mx-4`;
const SubmitButton = tw.button`w-full rounded-lg bg-brand-300 py-2 text-black-1000 mt-4`;
const IconContainer = tw.div`bg-yellow-400 flex size-12 items-center justify-center rounded-full`;
const Name = tw.div`text-lg font-bold`;
const RateContainer = tw.div`flex items-center`;
const RateValue = tw.span`mr-2 text-grey-400`;

root.render(
  <QueryErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ConfigProvider theme={antdThemeConfig} locale={vi}>
        <div>
          <div className='hidden lg:block'>
            <App />
          </div>
          <div className='xs:hidden flex h-screen items-center justify-center nv:flex lg:hidden'>
            <AppName>
              <div className='flex space-x-4 '>
                <IconContainer>
                  <Icon
                    width={'60px'}
                    height={'60px'}
                    src={images.Icon.NavioAppIcon}
                  />
                </IconContainer>
                <div>
                  <Name className={`${BodyLg} ${FontBold}`}>Navio</Name>
                  <RateContainer>
                    <RateValue className={`${BodyMdBase} ${FontMedium}`}>
                      4.5
                    </RateValue>
                    <Rate
                      allowHalf
                      disabled
                      defaultValue={4.98}
                      style={{ fontSize: 16 }}
                    />
                  </RateContainer>
                </div>
              </div>
              <SubmitButton className={`${ButtonSm} ${FontMedium}`}>
                Tải App Navio
              </SubmitButton>
            </AppName>
          </div>
        </div>
      </ConfigProvider>
    </QueryClientProvider>
  </QueryErrorBoundary>,
);
