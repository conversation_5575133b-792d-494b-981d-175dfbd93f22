.ant-tabs-tab.ant-tabs-tab-active {
  background-color: #f0f00b !important;
  border-radius: 8px;
}
.ant-tabs-tab:hover {
  background-color: #f0f1f5 !important;
  border-radius: 8px;
}

.ant-tabs-tab.ant-tabs-tab-active div {
  color: black !important;
  box-shadow: none !important;
  text-shadow: none !important;
}
/* .ant-tabs .ant-tabs-tab {
  margin-right: 2px !important;
} */
.ant-tabs-nav-wrap {
  min-width: 180px;
}

.ant-tabs > .ant-tabs-nav {
  position: unset;
  gap: 4px;
}
.ant-tabs-nav-list {
  gap: 8px;
}

.ant-tabs-nav-list > .ant-tabs-tab {
  padding: 8px;
}

.ant-tabs .ant-tabs-tab + .ant-tabs-tab {
  margin: 0 !important;
}

.ant-tabs-nav-list > .ant-tabs-ink-bar.ant-tabs-ink-bar-animated {
  width: 0px !important;
}

/* first child */
.popover-account-list .scrollbar-container div:nth-child(1) {
  position: relative !important;
}

.ant-select-item.ant-select-item-option {
  background-color: transparent !important;
}

.ant-select-item.ant-select-item-option:hover {
  background-color: #f0f1f5 !important;
}

.rc-virtual-list-holder-inner .ant-select-item.ant-select-item-option {
  margin-bottom: 8px;
}

.rc-virtual-list-holder-inner
  .ant-select-item.ant-select-item-option:last-child {
  margin-bottom: 0;
}

.ant-modal {
  border-radius: 16px !important;
  overflow: hidden;
}

.ant-select-item-option-content {
  font-weight: 500;
}

.rr-custom-popover .ant-popover-content {
  height: 100%;
}

.rr-custom-popover .ant-popover-content .ant-popover-inner-content{
  height: 100%;
}

.ant-popover-content .ant-popover-inner {
  padding: 8px !important;
  height: 100%;
}

.rr-custom-popover.select-account-popover  {
  height: calc(100vh - 24px);
  width: 360px;
}

.rr-custom-popover .ant-popover-content .ant-popover-inner {
  padding: 16px !important;
  border-radius: 12px !important;
  box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  border-radius: 12px !important;
}

.map-device-list.rr-custom-popover {
  box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.12);
  border-radius: 12px !important;
}

.sidebar-collapsed.map-device-list.rr-custom-popover {
  inset: 12px auto auto 468px !important;
}

.sidebar-expanded.map-device-list.rr-custom-popover {
  inset: 12px auto auto 628px !important;
}

.wrapper-mobile-buttons .buttons-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px !important;
}

.wrapper-mobile-buttons .buttons-container .button-store-name {
  font-size: 16px;
}

.ant-tooltip {
  .ant-tooltip-arrow {
    &::before {
      background-color: #ffffff !important;
      border-color: #ffffff !important;
    }
  }
  .ant-tooltip-content {
    .ant-tooltip-arrow {
      &::before {
        background-color: #ffffff !important;
        border-color: #ffffff !important;
      }
    }
  }
}

.ant-modal-root .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.ant-modal.login-history-modal {
  height: calc(100vh - 96px);
  width: 920px !important;
  max-height: 1000px;
}

.ant-modal.login-history-modal div[tabindex='0'] {
  height: 100%;
}

.ant-modal.login-history-modal .ant-modal-content {
  height: 100%;
}
.ant-modal .ant-modal-body {
  height: calc(100% - 38px);
}
