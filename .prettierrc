{"printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^react$", "^react-dom$", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^(features|i18n|services|store|utils|types)(.*)$", "^(components|assets)(.*)$", "__mocks__", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}